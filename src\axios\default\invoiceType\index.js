// 开票
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {

  queryInvoiceTypesByPage: `${API_Header}/invoiceType/queryInvoiceTypesByPage`, // 开票类目
  queryInvoiceTypes: `${API_Header}/invoiceType/queryInvoiceTypes`, // 查询一级
  deleteInvoiceType: `${API_Header}/invoiceType/deleteInvoiceType`, // 删除发票类型
  insertInvoiceType: `${API_Header}/invoiceType/insertInvoiceType`, // 添加发票类型
  queryMerchantInfo: `${API_Header}/merchantInfo/queryMerchantInfo`, // 可开票金额企业接口
  queryMerchantInvInfo: `${API_Header}/merchantInfo/queryMerchantInvInfo`, // 查询商户开票信息
  insertInvoiceInfo: `${API_Header}/invoiceInfo/insertInvoiceInfo`, // 查询商户开票信息
  queryInvoiceInfo: `${API_Header}/invoiceInfo/queryInvoiceInfo`, // 开票列表
  queryInvoiceTypeByMerId: `${API_Header}/invoiceType/queryInvoiceTypeByMerId`, // 开票类目查询接口
  auditInvoiceInfo: `${API_Header}/invoiceInfo/auditInvoiceInfo`, // 发票审批功能
  queryInvoiceAuditRecord: `${API_Header}/invoiceInfo/queryInvoiceAuditRecord`, // 查询发票审核记录
  updateInvoiceInfoById: `${API_Header}/invoiceInfo/updateInvoiceInfoById`, // 添加快递单号
  uploadInvoice: `${API_Header}/invoiceInfo/uploadInvoice`, // 提交上传发票
  queryInvoiceInfoById: `${API_Header}/invoiceInfo/queryInvoiceInfoById`, // 开票详情
  deleteAttachFile: `${API_Header}/attachFile/deleteAttachFile`, // 开票详情

  downInvoiceInfo: `${API_Header}/invoiceInfo/downInvoiceInfo`, // 开票导出
  invoiceToAudit: `${API_Header}/invoiceInfo/invoiceToAudit`, // 开票邮寄退回开票终审
  nextMonthAudit: `${API_Header}/invoiceInfo/nextMonthAudit`//下月审核
}

const invoiceType = {
  queryInvoiceTypeInfo: params => {
    return request.postJson(api.queryInvoiceTypeInfo, params)
  },
  queryInvoiceTypesByPage: params => {
    return request.postJson(api.queryInvoiceTypesByPage, params)
  },
  queryInvoiceTypes: params => {
    return request.postJson(api.queryInvoiceTypes, params)
  },
  deleteInvoiceType: params => {
    return request.postJson(api.deleteInvoiceType, params)
  },
  insertInvoiceType: params => {
    return request.postJson(api.insertInvoiceType, params)
  },
  queryMerchantInfo: params => {
    return request.postJson(api.queryMerchantInfo, params)
  },
  queryMerchantInvInfo: params => {
    return request.postJson(api.queryMerchantInvInfo, params)
  },
  insertInvoiceInfo: params => {
    return request.postJson(api.insertInvoiceInfo, params)
  },
  queryInvoiceInfo: params => {
    return request.postJson(api.queryInvoiceInfo, params)
  },
  queryInvoiceTypeByMerId: params => {
    return request.postJson(api.queryInvoiceTypeByMerId, params)
  },
  auditInvoiceInfo: params => {
    return request.postJson(api.auditInvoiceInfo, params)
  },
  queryInvoiceAuditRecord: params => {
    return request.postJson(api.queryInvoiceAuditRecord, params)
  },
  updateInvoiceInfoById: params => {
    return request.postJson(api.updateInvoiceInfoById, params)
  },
  invoiceToAudit: params => {
    return request.postJson(api.invoiceToAudit, params)
  },
  nextMonthAudit: params => {
    return request.postJson(api.nextMonthAudit, params)
  },
  uploadInvoice: params => {
    return request.postJson(api.uploadInvoice, params)
  },
  queryInvoiceInfoById: params => {
    return request.postJson(api.queryInvoiceInfoById, params)
  },
  deleteAttachFile: params => {
    return request.postJson(api.deleteAttachFile, params)
  },
  downInvoiceInfo: (params, filename, type) => {
    return request.downFiles(api.downInvoiceInfo, params, filename, type)
  },
  downInvoiceFile: (params, filename, type) => {
    return request.downFiles(api.downInvoiceFile, params, filename, type)
  }

}

export default invoiceType

