<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formDataPro" class="demo-form-inline" size="mini">
              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="signUpDetailDownLoadData">批量下载</el-button>
                    <!--                    <el-button icon="el-icon-search" type="primary">批量下载交付</el-button>-->
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          v-hasPermission="['sign:noShow-edit']"
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <!-- <el-table-column
            prop="openTime"
            label="报名时间"
            show-overflow-tooltip
          /> -->
          <el-table-column
            prop="name"
            label="姓名"
          />
          <el-table-column
            prop="idCard"
            label="身份证号"
            show-overflow-tooltip
          />
          <el-table-column
            prop="bankCardNo"
            label="银行卡号"
            show-overflow-tooltip
          />
          <el-table-column
            prop="mobile"
            label="手机号"
          />
          <el-table-column
            prop="signStatus"
            label="状态"
          >
            <template slot-scope="scope">
              {{ scope.row.signStatus == '1' ? '已签约' : '已解约' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="taskStatus"
            label="任务情况"
          >
            <template slot-scope="scope">
              {{ scope.row.taskStatus == '0' ? '结束' : '进行中' }}
            </template>
          </el-table-column>

        </el-table>
        <el-table
          v-hasNoPermission="['sign:noShow-edit']"
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <!-- <el-table-column
            prop="openTime"
            label="报名时间"
            show-overflow-tooltip
          /> -->
          <el-table-column
            prop="name"
            label="姓名"
          />
          <el-table-column
            prop="idCard"
            label="身份证号"
            show-overflow-tooltip
          />
          <el-table-column
            prop="bankCardNo"
            label="银行卡号"
            show-overflow-tooltip
          />
          <el-table-column
            prop="mobile"
            label="手机号"
          />
          <el-table-column
            prop="signStatus"
            label="状态"
          >
            <template slot-scope="scope">
              {{ scope.row.signStatus == '1' ? '已签约' : '已解约' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="taskStatus"
            label="任务情况"
          >
            <template slot-scope="scope">
              {{ scope.row.taskStatus == '0' ? '结束' : '进行中' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="remark"
            label="交付物"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="openBox(scope.row.id)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    编辑交付物-->
    <el-dialog title="编辑交付物" :visible.sync="dialogProShow" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form :model="dialogProData" size="mini" class="form-style" label-width="120">
          <el-form-item label="交付物说明" class="form-items">
            <el-input v-model="dialogProData.remark" type="textarea" :row="2" autocomplete="off" />
          </el-form-item>

          <div class="table-top table-header">
            <el-upload
              ref="upload"
              class="file-upload"
              :show-file-list="false"
              action=""
              :disabled="upLoading"
              accept=".pdf,.jpg,.jpeg,.png"
              :http-request="(params => uprequest(params,'0'))"
            >
              <el-button plain icon="el-icon-plus" size="mini">添加附件</el-button>
            </el-upload>
            <el-button plain size="mini" @click="batchDownLoadFile">批量下载交付</el-button>
          </div>
          <el-table
            :data="jfwlistData"
            border
            size="mini"
            class="list-table"
          >
            <el-table-column
              prop="fileName"
              label="文件名称"
            >
              <template slot-scope="scope">
                {{ scope.row.fileName + scope.row.fileExt }}
              </template>
            </el-table-column>
            <el-table-column
              prop="fileSize"
              label="文件大小"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.fileSize > 1024*1024">{{ Math.ceil(scope.row.fileSize / 1024 / 1024) }}MB</div>
                <div v-else>{{ Math.ceil(scope.row.fileSize / 1024) }}KB</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="fileType"
              label="文件状态"
            >
              <template slot-scope="scope">
                已上传
              </template>
            </el-table-column>
            <el-table-column
              prop=""
              label="操作"
            >
              <template slot-scope="scope">

                <el-upload
                  ref="upload"
                  class="file-upload resetUpload"
                  :show-file-list="false"
                  action=""
                  :disabled="upLoading"
                  accept=".pdf,.jpg,.jpeg,.png"
                  :http-request="(params => uprequest(params,scope.row.fileType,scope.$index))"
                >
                  <a class="el-button el-button--text resetbtn">替换</a>
                </el-upload>

                <a class="el-button el-button--text resetbtn" target="_blank" :href="encodeURI(scope.row.filePath)">预览</a>
                <el-button type="text" @click="delFiles(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="btnbox">
            <el-button type="primary" size="mini" @click="submit">保存</el-button>
            <el-button type="" size="mini" @click="dialogProShow = false">取消</el-button>
          </div>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import proManage from '@/axios/default/proManage'
import publics from '@/axios/default/public'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'Sign',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formDataPro: {
        projectId: ''
      },
      formLabelWidth: '180px',
      listData: [],
      dialogProShow: false,
      dialogProData: this.initDialogProData(),
      jfwlistData: [],
      upLoading: false,
      upData: {
        file: '',
        fileType: ''
      }
    }
  },
  watch: {
    '$route.path': {
      handler() {
        if (this.$route.query.id) {
          this.formDataPro.projectId = this.$route.query.id
          this.list()
        }
      }
    }
  },
  mounted() {
    this.formDataPro.projectId = this.$route.query.id
    this.list()
  },
  methods: {
    list() {
      proManage.signList({
        ...this.formDataPro,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        console.log(res)
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },

    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    // 打开弹框
    openBox(id) {
      this.dialogProShow = true
      this.dialogProData = this.initDialogProData()
      this.jfwlistData = []
      this.getOneDeliveryFile(id)
    },
    initDialogProData() {
      return {
        signUpId: '',
        remark: ''
      }
    },
    getOneDeliveryFile(id) {
      proManage.getOneDeliveryFile({
        signUpId: id
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogProData = res.data.data
          this.dialogProData.id = id
          this.jfwlistData = res.data.data.files || []
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    delFiles(index) {
      this.jfwlistData.splice(index, 1)
    },
    // 上传
    uprequest(param, fileType, index) {
      const that = this
      that.upLoading = true
      const upEvent = param.file
      const size = upEvent.size
      const name = upEvent.name
      const type = upEvent.type
      const limitSize = size / 1024 / 1024 < 50
      if (!limitSize) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      that.upData.file = upEvent
      that.upData.fileType = fileType
      this.fileListData(index)
    },
    fileListData(index) {
      const formDataPro = new FormData()
      const data = this.upData
      for (const key in data) {
        formDataPro.append(key, data[key])
      }
      publics.upFiles(formDataPro).then(res => {
        if (res.data.code == '0000') {
          const { fileName, fileSize, fileExt, filePath, fileType } = res.data.data
          if (Object.prototype.toString.call(index) == '[object Number]') {
            this.$set(this.jfwlistData, index, { fileName, fileExt, filePath, fileSize, fileType })
          } else {
            console.log(this.jfwlistData)
            this.jfwlistData.push({
              fileName,
              fileExt,
              filePath,
              fileSize: fileSize,
              fileType: fileType
            })
            console.log(this.jfwlistData)
          }
          this.upLoading = false
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
          this.upLoading = false
        }
      }).catch(res => {
        this.upLoading = false
      })
    },
    batchDownLoadFile() {
      proManage.batchDownLoadFile({
        signUpId: this.dialogProData.id
      }, `交付物列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.zip`, '.zip').then(res => {

      })
    },
    submit() {
      proManage.addDeliveryFile({
        signUpId: this.dialogProData.id,
        remark: this.dialogProData.remark,
        files: this.jfwlistData
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
          this.dialogProShow = false
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    signUpDetailDownLoadData() {
      proManage.signUpDetailDownLoadData({
        id: this.formDataPro.projectId
      }, `报名详情列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .resetUpload{
    display: inline-block;
    .el-button{
      border: none;
      background: none;
      font-size: 14px;
      color: #409EFF;
      /deep/ i {
        display: none!important;
      }
    }
  }
  .btnbox{
    text-align: right;
  }

  .table-header{
    display: flex;
    justify-content: flex-end;
    .file-upload{
      margin-right: 5px;
    }
  }
</style>
