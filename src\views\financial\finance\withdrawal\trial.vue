<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="提现时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                      @change="resetTime"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          stripe
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="提现时间"
            show-overflow-tooltip
          />
          <el-table-column
            prop="accountName"
            label="企业名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
          />
          <el-table-column
            prop="destBankName"
            label="收款银行"
          />
          <el-table-column
            prop="destAcctName"
            label="收款账户名"
            min-width="220"
          />
          <el-table-column
            prop="destAcctNo"
            label="收款卡号"
          />
          <el-table-column
            prop="cashAmount"
            label="提现金额"
          />
          <el-table-column
            prop="cashStatus"
            label="审核状态"
          >
            <template slot-scope="scope">
              {{ cashStatus[scope.row.cashStatus] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="userName"
            label="申请人"
          />
          <el-table-column
            prop="remark"
            label="备注"
          />
          <el-table-column
            prop="remark"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" size="mini" class="btn-view" @click="openbox(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    提现初审-->
    <el-dialog title="提现初审" :visible.sync="dialogMain" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogForm" :model="dialogMainData" size="mini" class="form-style" :rules="rules" label-width="120px" label-position="right">
          <el-form-item label="企业名称">
            <el-input v-model="dialogMainData.accountName" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="代征主体">
            <el-input v-model="dialogMainData.levyBodyName" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="通道名称">
            <el-input v-model="dialogMainData.channelName" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="提现金额">
            <el-input v-model="dialogMainData.cashAmount" type="text" placeholder="请输入内容" disabled>
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="申请备注">
            <el-input v-model="dialogMainData.remark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="备注" prop="firistAuditRemark">
            <el-input v-model="dialogMainData.firistAuditRemark" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="" class="form-items">
            <el-button :loading="btnLoading" type="primary" @click="auditCashOrderInfo('dialogForm','2')">通过</el-button>
            <el-button :loading="btnLoading" type="primary" @click="auditCashOrderInfo('dialogForm','3')">不通过</el-button>
            <!--            <el-button @click="dialogMain=false">取消</el-button>-->
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import finance from '@/axios/default/finance'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Trial',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        accountName: ''
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogMain: false,
      dialogMainData: {},
      cashStatus: { 1: '待审核', 2: '初审通过', 3: '初审拒绝', 4: '复审拒绝', 5: '提现完成' },
      rules: {
        firistAuditRemark: [{ required: true, message: '请填写初审备注', trigger: 'blur' }]
      },
      btnLoading: false

    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      finance.cashOrderInfoList({
        ...this.formData,
        cashStatus: '1',
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    openbox(data) {
      this.dialogMain = true
      this.$nextTick(function() {
        this.$refs['dialogForm'].clearValidate()
        this.$refs['dialogForm'].resetFields()
      })
      this.dialogMainData = data
    },
    auditCashOrderInfo(formName, cashStatus) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          finance.auditCashOrderInfo({
            id: this.dialogMainData.id,
            cashStatus: cashStatus,
            firistAuditRemark: this.dialogMainData.firistAuditRemark
          }).then(res => {
            this.btnLoading = false
            this.$refs[formName].clearValidate()
            this.$refs[formName].resetFields()
            if (res.data.code == '0000') {
              this.dialogMain = false
              this.$message({ type: 'success', message: '操作成功' })
              this.list()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
