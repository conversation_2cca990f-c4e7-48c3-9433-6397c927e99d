// 项目负责人
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  queryCashApplyByPage: `${API_Header}/cashApply/queryCashApplyByPage`, // 分润列表查询
  auditApplyCash: `${API_Header}/cashApply/auditApplyCash`, // 分润审核功能
  queryApplyCashCal: `${API_Header}/cashApply/queryApplyCashCal`, // 提现明细查询
  checkPassword: `${API_Header}/cashApply/checkPassword`, // 密码校验
  submitApplyCash: `${API_Header}/cashApply/submitApplyCash`, // 提交审核
  queryAgentProfitByBatchNum: `${API_Header}/agentProfit/queryAgentProfitByBatchNum` // 提现明细查询

}

const profits = {
  queryCashApplyByPage: params => {
    return request.postJson(api.queryCashApplyByPage, params)
  },
  auditApplyCash: params => {
    return request.postJson(api.auditApplyCash, params)
  },
  queryApplyCashCal: params => {
    return request.postJson(api.queryApplyCashCal, params)
  },
  checkPassword: params => {
    return request.postJson(api.checkPassword, params)
  },
  submitApplyCash: params => {
    return request.postJson(api.submitApplyCash, params)
  },
  queryAgentProfitByBatchNum: params => {
    return request.postJson(api.queryAgentProfitByBatchNum, params)
  }
}

export default profits

