<template>
  <div class="content-main">
    <div class="general-layout">

      <!--        <div class="table-top">-->
      <!--          <el-button v-if="settNum" plain size="mini" @click="downSettReceipt">结算回单批量下载</el-button>-->
      <!--        </div>-->
      <el-table
        :data="listData"
        border
        size="mini"
        class="list-table"
      >
        <el-table-column
          prop="applyTime"
          label="申请日期"
          width="150"
          show-overflow-tooltip
        />

        <el-table-column
          prop="money"
          label="提现金额"
          show-overflow-tooltip
        />

        <el-table-column
          prop="auditStatus"
          label="审核状态"
          width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ auditStatus[scope.row.auditStatus] }}
          </template>
        </el-table-column>

        <el-table-column
          prop="applyName"
          label="申请人"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          prop="feeRate"
          label="费率"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="saleName"
          label="上级代理／销售"
          width="150"
          show-overflow-tooltip
        />

        <el-table-column
          label="操作"
          width="150"
        >
          <template slot-scope="scope">
            <a class="tb-active-red mar-right" @click="viewInfo(scope.row)">提现明细</a>
            <a class="tb-active-red" @click="trial(scope.row)">分润初审</a>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>

    </div>

    <el-dialog title="分润初审" :visible.sync="dialogTrial" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogTrialForm" label-width="140px" size="mini" class="form-style" :model="dialogTrialForm" :rules="rules">
          <el-form-item label="申请人" prop="applyName">
            <el-input v-model="dialogTrialForm.applyName" disabled />
          </el-form-item>
          <el-form-item label="申请金额" prop="money">
            <el-input v-model="dialogTrialForm.money" disabled />
          </el-form-item>
          <el-form-item label="打款金额" prop="cashMoney">
            <el-row>
              <el-col :span="16"><el-input v-model="dialogTrialForm.cashMoney" /></el-col>
              <el-col :span="8"><span class="apply-money" @click="setMoney">申请金额</span></el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="备    注" prop="auditRemark">
            <el-input v-model="dialogTrialForm.auditRemark" type="textarea" :row="2" />
          </el-form-item>

          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogTrialForm','1')">通过</el-button>
              <el-button :loading="btnLoading" @click="submit('dialogTrialForm','2')">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import profits from '@/axios/default/profits'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import publicApi from '@/axios/default/public'
import moment from 'moment'
export default {
  name: 'Trial',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      auditStatus: { 1: '初审中', 2: '初审拒绝', 3: '复审中', 4: '复审拒绝', 5: '复审成功', 6: '打款中', 7: '打款成功', 8: '打款失败' },
      formData: {
        auditStatus: '1'
      },
      listData: [],
      dialogTrial: false,
      dialogTrialForm: this.initdialogTrialForm(),
      btnLoading: false,
      rules: {
        cashMoney: [{ required: true, message: '请填写打款金额', trigger: 'blur' }],
        auditRemark: [{ required: true, message: '请填写备注', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    submit(formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          profits.auditApplyCash({
            batchNum: this.dialogTrialForm.batchNum,
            btnType: type,
            auditStage: '1',
            cashMoney: this.dialogTrialForm.cashMoney,
            auditRemark: this.dialogTrialForm.auditRemark
          }).then(res => {
            if (res.data.code === '0000') {
              this.dialogTrial = false
              this.$refs['dialogTrialForm'].resetFields()
              this.requestBack('操作成功', 'success', res => {
                this.btnLoading = false
              })
              this.list()
            } else {
              const that = this
              newAlert(this.$tips, res.data.message, function() {
                this.btnLoading = false
              })
            }
          })
        } else {
          return false
        }
      })
    },
    requestBack(message, type, callback) {
      this.$message({
        message: message,
        type: type,
        onClose: () => {
          callback && callback()
        }
      })
    },
    list() {
      profits.queryCashApplyByPage({
        'pageSize': this.pageSize,
        'pageNum': this.pageNum,
        ...this.formData
      }).then(res => {
        if (res.data.code == '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    initdialogTrialForm() {
      return {
        batchNum: '',
        applyName: '',
        money: '',
        auditRemark: '',
        cashMoney: ''
      }
    },
    trial(data) {
      this.dialogTrialForm = this.initdialogTrialForm()
      this.dialogTrial = true
      this.dialogTrialForm.batchNum = data.batchNum
      this.dialogTrialForm.applyName = data.applyName
      this.dialogTrialForm.money = data.money
      // this.$nextTick(() =>{
      //   this.$refs.dialogTrialForm.clearFiles()
      //   this.dialogTrialForm.batchNum = data.batchNum
      //   this.dialogTrialForm.applyName = data.applyName
      //   this.dialogTrialForm.money = data.money
      // })
    },
    setMoney() {
      this.$nextTick(res => {
        this.dialogTrialForm.cashMoney = this.dialogTrialForm.money
      })
    },
    viewInfo(data) {
      this.$router.push({
        path: '/profits/detail',
        query: {
          batchNum: data.batchNum
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .table-head-money .el-col .table-headers{
    display: flex;
    flex-flow: nowrap column;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #eff2f6;
    .table-header-num{
      font-size: 18px;
      margin-bottom: 5px;
      color: #333;
    }
    .table-header-name{
      font-size: 12px;
      color: #999;
    }
  }
  .table-head-money .el-col:last-child .table-headers{
    border-right: none;
    .table-header-num{
      color: #F54343;
    }
  }
  .mar-right{
    margin-right: 10px;
  }
  .apply-money{
    display: block;
    cursor: pointer;
    width: 100%;
    color: #f54343;
    text-decoration: underline;
    text-align: center;
  }
</style>

