<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="调帐时间" class="form-items"> <el-date-picker
                    v-model="pickTime"
                    class="auto-width"
                    type="daterange"
                    align="right"
                    unlink-panels
                    value-format="yyyy-MM-dd"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :picker-options="pickerOptions"
                    @change="resetTime"
                  />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="调帐企业" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-search" type="primary" @click="accountExcel">批量下载</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="" plain type="primary" @click="accountTolevy">企业至代征主体调账</el-button>
                    <el-button icon="" plain type="primary" @click="addDZ">代征主体至企业调账</el-button>
                    <el-button icon="" plain type="primary" @click="addQY">企业账户调账</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="调账时间"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="accountName"
            label="被调帐企业"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="accountNo"
            label="被调账账号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="channelName"
            label="被调账通道"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="realAmount"
            label="被调账金额"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="toAccountName"
            label="至调账企业"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="toAccountNo"
            label="至调账账号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="toChannelName"
            label="至调账通道"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="realAmount"
            label="至调账金额"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="adjustStatus"
            label="审核状态"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ adjustStatusEnum[scope.row.adjustStatus] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="userName"
            label="申请人"
            show-overflow-tooltip
          />
          <el-table-column
            prop="adjustDesc"
            label="备注"
            width="200"
            show-overflow-tooltip
          />
        </el-table>

        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>
    </div>

    <!--    账户至代征主体调账-->
    <el-dialog title="企业至代征主体调账" :visible.sync="dialogAccount" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogAccountData" :model="dialogAccountData" size="mini" class="form-style" :rules="rulesAccount">
          <el-row>
            <el-col>
              <el-row>
                <el-col :span="11">
                  <el-form-item ref="fromOwnerId" label="企业名称" class="form-items" prop="fromOwnerId">
                    <el-select
                      v-model="dialogAccountData.fromOwnerId"
                      class="auto-width"
                      filterable
                      clearable
                      remote
                      placeholder="请输入关键词"
                      :remote-method="merAllListAccount"
                      :loading="loading"
                      @change="pickAccountLevy"
                    >
                      <el-option v-for="item in AccmerIdArr" :key="item.id" :label="item.merName" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="2" style="text-align: center;line-height: 28px">
                  =>
                </el-col>
                <el-col :span="11">
                  <el-form-item ref="toOwnerIdAcc" label="代征主体" class="form-items" prop="toOwnerId">
                    <el-select v-model="dialogAccountData.toOwnerId" placeholder="请选择代征主体" class="auto-width" @change="pickChannelAcc">
                      <!--                      <el-option v-if="AcclevyBodyArr.length>1" label="请选择" value=""></el-option>-->
                      <el-option v-for="(item,index) in AcclevyBodyArr" :key="item.id" :label="item.NAME" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row>
                <el-form-item ref="channelIdAcc" label="通道名称" class="form-items" prop="channelId">
                  <el-select v-model="dialogAccountData.channelId" placeholder="请选择通道名称" class="auto-width">
                    <!--                    <el-option v-if="AccchannelList.length>1" label="请选择" value=""></el-option>-->
                    <el-option v-for="(item,index) in AccchannelList" :key="item.id" :label="item.channel_name" :value="item.id" />
                  </el-select>
                </el-form-item>
                <!--              <el-form-item label="" label-width="70px" class="form-items">-->
                <!--                <el-radio v-model="dialogDZData.radio" label="1">账户调增</el-radio>-->
                <!--                <el-radio v-model="dialogDZData.radio" label="2">账户调减</el-radio>-->
                <!--              </el-form-item>-->
                <el-form-item label="" label-width="80px" class="form-items" prop="realAmount">
                  <el-input v-model="dialogAccountData.realAmount" autocomplete="off" class="auto-width" type="text">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="备注" label-width="80px" prop="adjustDesc">
                  <el-input v-model="dialogAccountData.adjustDesc" type="textarea" :rows="2" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="" label-width="80px" class="form-items">
                  <el-button :loading="btnLoading" type="primary" @click="submit('dialogAccountData','dialogAccount')">提交审核</el-button>
                  <el-button :loading="btnLoading" @click="dialogAccount=false">取消</el-button>
                </el-form-item>
              </el-row>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
    </el-dialog>

    <!--    代征主体至账户调账-->
    <el-dialog title="代征主体至企业调账" :visible.sync="dialogDZ" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogDZData" :model="dialogDZData" size="mini" class="form-style" :rules="rulesDZ">
          <el-row>
            <el-col>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="代征主体" class="form-items" prop="fromOwnerId">
                    <el-select v-model="dialogDZData.fromOwnerId" placeholder="请选择代征主体" class="auto-width" @change="pickChannelDZ">
                      <el-option v-if="DZlevyBodyArr.length>1" label="请选择" value="" />
                      <el-option v-for="(item,index) in DZlevyBodyArr" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="2" style="text-align: center;line-height: 28px">
                  =>
                </el-col>
                <el-col :span="11">
                  <el-form-item ref="toOwnerId" label="企业名称" class="form-items" prop="toOwnerId">
                    <el-select
                      v-model="dialogDZData.toOwnerId"
                      class="auto-width"
                      filterable
                      clearable
                      remote
                      placeholder="请输入关键词"
                      :remote-method="merAllList"
                      :loading="loading"
                      @change="pickDZChannelName"
                    >
                      <el-option v-for="item in DZmerIdArr" :key="item.id" :label="item.merName" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item ref="channelId" label="通道名称" class="form-items" prop="channelId">
                  <el-select v-model="dialogDZData.channelId" placeholder="请选择通道名称" class="auto-width">
                    <el-option v-for="(item,index) in DZchannelList" :key="item.id" :label="item.channel_name" :value="item.id" />
                  </el-select>
                </el-form-item>
                <!--              <el-form-item label="" label-width="70px" class="form-items">-->
                <!--                <el-radio v-model="dialogDZData.radio" label="1">账户调增</el-radio>-->
                <!--                <el-radio v-model="dialogDZData.radio" label="2">账户调减</el-radio>-->
                <!--              </el-form-item>-->
                <el-form-item label="" label-width="80px" class="form-items" prop="realAmount">
                  <el-input v-model="dialogDZData.realAmount" autocomplete="off" class="auto-width" type="text">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="备注" label-width="80px" prop="adjustDesc">
                  <el-input v-model="dialogDZData.adjustDesc" type="textarea" :rows="2" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="" label-width="80px" class="form-items">
                  <el-button :loading="btnLoading" type="primary" @click="submit('dialogDZData','dialogDZ')">提交审核</el-button>
                  <el-button :loading="btnLoading" @click="dialogDZ=false">取消</el-button>
                </el-form-item>
              </el-row>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
    </el-dialog>
    <!--    企业账户调账-->
    <el-dialog title="企业账户调账" :visible.sync="dialogQY" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogQYData" :model="dialogQYData" size="mini" class="form-style" :rules="rulesQY">
          <el-row>
            <el-col :span="24">
              <el-form-item label="企业名称" class="form-items" prop="fromOwnerId">
                <!--                <el-select v-model="dialogQYData.fromOwnerId" placeholder="请选择企业名称" class="auto-width" @change="pickQYlevyBody">-->
                <!--                  <el-option v-for="(item,index) in QYmerIdArr" :key="item.id" :label="item.merName" :value="item.id" />-->
                <!--                </el-select>-->
                <el-select
                  v-model="dialogQYData.fromOwnerId"
                  class="auto-width"
                  filterable
                  clearable
                  remote
                  placeholder="请输入关键词"
                  :remote-method="merAllListQY"
                  :loading="loading"
                  @change="pickQYlevyBody"
                >
                  <el-option v-for="item in QYmerIdArr" :key="item.id" :label="item.merName" :value="item.id" />
                </el-select>

              </el-form-item>
              <el-form-item ref="QYlveyBody" label="代征主体" class="form-items" prop="QYlveyBody">
                <el-select v-model="dialogQYData.QYlveyBody" placeholder="请选择代征主体" class="auto-width" @change="pickQYChannel">
                  <el-option v-for="(item,index) in QYlevyBodyArr" :key="index" :label="item.NAME" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-row>
                <el-col :span="11">
                  <el-form-item ref="channelId" label="通道名称" class="form-items" prop="channelId">
                    <el-select v-model="dialogQYData.channelId" placeholder="请选择通道名称" class="auto-width" clearable>
                      <el-option v-for="(item,index) in QYchannelList" :key="item.id" :label="item.channel_name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="2" style="text-align: center;line-height: 28px">
                  =>
                </el-col>
                <el-col :span="11">
                  <el-form-item ref="toChannelId" label="通道名称" class="form-items" prop="toChannelId">
                    <el-select v-model="dialogQYData.toChannelId" placeholder="请选择活动区域" class="auto-width">
                      <el-option v-for="(item,index) in QYchannelList" :key="item.id" :label="item.channel_name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--              <el-form-item label="" label-width="70px" class="form-items">-->
              <!--                <el-radio v-model="dialogQYData.radio" label="1">账户调增</el-radio>-->
              <!--                <el-radio v-model="dialogQYData.radio" label="2">账户调减</el-radio>-->
              <!--              </el-form-item>-->
              <el-form-item label="" label-width="70px" class="form-items" prop="realAmount">
                <el-input v-model="dialogQYData.realAmount" autocomplete="off" class="auto-width" type="text">
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注" label-width="70px" prop="adjustDesc">
            <el-input v-model="dialogQYData.adjustDesc" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="" label-width="70px" class="form-items">
            <el-button :loading="btnLoading" type="primary" @click="submit('dialogQYData','dialogQY')">提交审核</el-button>
            <el-button :loading="btnLoading" @click="dialogQY=false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import account from '@/axios/default/finance'
import Pagination from '@/components/Pagination'
import publics from '@/axios/default/public'
import { parseTime, newAlert } from '@/utils'

export default {
  name: 'List',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      adjustStatusEnum: { 1: '待审核', 2: '初审通过', 3: '初审拒绝', 4: '复审拒绝', 5: '调账完成' },
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        accountName: ''
      },
      // formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogDZ: false,
      dialogDZData: this.initDialogDZ(),
      dialogQY: false,
      dialogQYData: this.initDialogQY(),
      dialogAccount: false,
      dialogAccountData: this.initDialogAccount(),
      AccmerIdArr: [],
      AcclevyBodyArr: [],
      AccchannelList: [],
      DZmerIdArr: [],
      DZlevyBodyArr: [],
      DZchannelList: [],
      QYmerIdArr: [],
      QYlevyBodyArr: [],
      QYchannelList: [],
      rulesAccount: {
        fromOwnerId: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        toOwnerId: [{ required: true, message: '请选择企业名称', trigger: 'change' }],
        channelId: [{ required: true, message: '请选择通道名称', trigger: 'change' }],
        // toChannelId:[{required:true,message:'请选择通道名称',trigger:'change'}],
        realAmount: [{ required: true, message: '请填写调帐金额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /[1-9]+(\d*|\.\d*)$/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        adjustDesc: [{ required: true, message: '请填写备注', trigger: 'blur' }]
      },
      rulesDZ: {
        fromOwnerId: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        toOwnerId: [{ required: true, message: '请选择企业名称', trigger: 'change' }],
        channelId: [{ required: true, message: '请选择通道名称', trigger: 'change' }],
        // toChannelId:[{required:true,message:'请选择通道名称',trigger:'change'}],
        realAmount: [{ required: true, message: '请填写调帐金额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /[1-9]+(\d*|\.\d*)$/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        adjustDesc: [{ required: true, message: '请填写备注', trigger: 'blur' }]
      },
      rulesQY: {
        fromOwnerId: [{ required: true, message: '请选择企业名称', trigger: 'change' }],
        QYlveyBody: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        channelId: [{ required: true, message: '请选择通道名称', trigger: 'change' }],
        toChannelId: [{ required: true, message: '请选择通道名称', trigger: 'change' }],
        realAmount: [{ required: true, message: '请填写调帐金额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /[1-9]+(\d*|\.\d*)$/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        adjustDesc: [{ required: true, message: '请填写备注', trigger: 'blur' }]
      },
      btnLoading: false,
      loading: false
    }
  },
  watch: {
    QYmerIdArr: function(n, o) {
      if (n.length === 0) {
        this.dialogQYData.fromOwnerId = ''
        this.dialogQYData.QYlveyBody = ''
        this.dialogQYData.toChannelId = ''
        this.dialogQYData.channelId = ''
      }
    },
    QYlevyBodyArr: function(n, o) {
      if (n.length === 0) {
        this.dialogQYData.QYlveyBody = ''
        this.dialogQYData.toChannelId = ''
        this.dialogQYData.channelId = ''
      }
    },
    QYchannelList: function(n, o) {
      if (n.length === 0) {
        this.dialogQYData.toChannelId = ''
        this.dialogQYData.channelId = ''
      }
    }
  },
  mounted() {
    this.list()
    // this.merAllList()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      account.findAccuntInfoList({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    accountExcel() {
      account.accountExcel({
        ...this.formData
      }, `调账列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {
        // this.$message.success('操作成功')
      })
    },
    submit(formName, dialogName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (dialogName === 'dialogDZ') {
            this.addAccountAdjust(this.dialogDZData, dialogName, 'dialogDZData')
          } else if (dialogName === 'dialogQY') {
            this.addAccountAdjust(this.dialogQYData, dialogName, 'dialogQYData')
          } else {
            this.addAccountAdjust(this.dialogAccountData, dialogName, 'dialogAccountData')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addAccountAdjust(data, dialogName, name) {
      account.addAccountAdjust(data).then(res => {
        if (res.data.code === '0000') {
          this.$refs[name].resetFields()
          this[dialogName] = false
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
        this.btnLoading = false
      })
    },
    // 代征主体至企业
    initDialogDZ() {
      return {
        fromOwnerId: '',
        channelId: '',
        toOwnerId: '',
        toChannelId: '',
        accountType: '3',
        ownerType: '1',
        adjustType: '2',
        realAmount: '',
        adjustDesc: ''
      }
    },
    // 企业至代征主体
    initDialogAccount() {
      return {
        fromOwnerId: '',
        channelId: '',
        toOwnerId: '',
        toChannelId: '',
        accountType: '2',
        ownerType: '2',
        adjustType: '3',
        realAmount: '',
        adjustDesc: ''
      }
    },
    // 企业调账
    initDialogQY() {
      return {
        fromOwnerId: '',
        channelId: '',
        QYlveyBody: '',
        toOwnerId: '',
        toChannelId: '',
        accountType: '2',
        ownerType: '2',
        adjustType: '1',
        realAmount: '',
        adjustDesc: ''
      }
    },
    accountTolevy() {
      this.dialogAccount = true
      this.$nextTick(function() {
        this.dialogAccountData = this.initDialogAccount()
      })
    },
    addDZ() {
      this.dialogDZ = true
      // this.$nextTick(function() {
      //   this.$refs['dialogDZData'].resetFields()
      //   this.dialogDZData = this.initDialogDZ()
      // })
      this.$nextTick(function() {
        this.dialogDZData = this.initDialogDZ()
        this.queryLevyBodyInfos()
      })
    },
    addQY() {
      // this.queryLevyBodyInfos()
      this.dialogQY = true
      this.$nextTick(function() {
        this.dialogQYData = this.initDialogQY()
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    pickChannelAcc() {
      // dialogAccountData.fromOwnerId
      this.$refs['channelIdAcc'].resetField()
      this.AccchannelList = []
      this.initAddRechInfo({ queryType: '3', merId: this.dialogAccountData.fromOwnerId, levyId: this.dialogAccountData.toOwnerId }, 'AccchannelList')
    },
    pickAccountLevy() {
      this.$refs['toOwnerIdAcc'].resetField()
      this.$refs['channelIdAcc'].resetField()
      this.AcclevyBodyArr = []
      this.AccchannelList = []
      if (!this.dialogAccountData.fromOwnerId) return
      this.initAddRechInfo({ queryType: '2', merId: this.dialogAccountData.fromOwnerId }, 'AcclevyBodyArr')
    },
    // 企业账户调账
    merAllListAccount(query) {
      if (query !== '') {
        this.loading = true
        publics.initAddRechInfo({ queryType: '1', merName: query }).then(res => {
          if (res.data.code === '0000') {
            this.loading = false
            this.AccmerIdArr = res.data.data
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      } else {
        this.AccmerIdArr = []
      }
    },
    // 代征主体账户调账
    merAllList(query) {
      if (query !== '') {
        this.loading = true
        publics.getMerchantInfoByLevyId({
          merName: query,
          levyId: this.dialogDZData.fromOwnerId
        }).then(res => {
          if (res.data.code === '0000') {
            this.loading = false
            this.DZmerIdArr = res.data.data
            if (this.DZmerIdArr.length === 1) {
              this.dialogDZData.toOwnerId = this.DZmerIdArr[0].id
              this.pickDZChannelName(this.dialogDZData.toOwnerId)
            }
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      } else {
        this.DZmerIdArr = []
      }
    },
    // 企业账户调账
    merAllListQY(query) {
      // if (query !== '') {
      //   this.loading = true
      //   publics.getMerchantInfoByLevyId({
      //     merName: query,
      //     // levyId: this.dialogDZData.fromOwnerId
      //   }).then(res => {
      //     this.loading = false
      //     this.QYmerIdArr = res.data.data
      //   })
      // } else {
      //   this.QYmerIdArr = []
      // }
      if (query !== '') {
        this.loading = true
        publics.initAddRechInfo({ queryType: '1', merName: query }).then(res => {
          if (res.data.code === '0000') {
            this.loading = false
            this.QYmerIdArr = res.data.data
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      } else {
        this.QYmerIdArr = []
      }
    },
    // // 代征主体
    queryLevyBodyInfos() {
      publics.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.DZlevyBodyArr = res.data.data
          if (this.DZlevyBodyArr.length === 1) {
            this.dialogDZData.fromOwnerId = this.DZlevyBodyArr[0].id
            this.pickChannelDZ(this.dialogDZData.fromOwnerId)
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    pickChannelDZ(id) {
      this.$refs['toOwnerId'].resetField()
      this.$refs['channelId'].resetField()
      this.DZmerIdArr = []
      this.DZchannelList = []
      this.merAllList()
    },
    // 企业账户调账
    // 商户列表
    initAddRechInfo(data, item) {
      publics.initAddRechInfo(data).then(res => {
        this[item] = res.data.data
        console.log(this[item].length)
        if (this[item].length === 1) {
          if (item == 'DZchannelList') {
            this.dialogDZData.channelId = this[item][0].id
          } else if (item == 'QYmerIdArr') {
            this.dialogQYData.fromOwnerId = this[item][0].id
            this.pickQYlevyBody()
          } else if (item == 'QYlevyBodyArr') {
            this.dialogQYData.QYlveyBody = this[item][0].id
            this.pickQYChannel()
          } else if (item == 'QYchannelList') {
            this.dialogQYData.channelId = this[item][0].id
            this.dialogQYData.toChannelId = this[item][0].id
          } else if (item == 'AcclevyBodyArr') {
            this.dialogAccountData.toOwnerId = this[item][0].id
            this.pickChannelAcc()
          } else if (item == 'AccchannelList') {
            this.dialogAccountData.channelId = this[item][0].id
          }
        }
      })
    },
    pickDZChannelName() {
      this.$refs['channelId'].resetField()
      this.DZchannelList = []
      this.initAddRechInfo({ queryType: '3', merId: this.dialogDZData.toOwnerId, levyId: this.dialogDZData.fromOwnerId }, 'DZchannelList')
    },
    pickQYlevyBody() {
      this.$refs['QYlveyBody'].resetField()
      this.$refs['channelId'].resetField()
      this.$refs['toChannelId'].resetField()
      this.initAddRechInfo({ queryType: '2', merId: this.dialogQYData.fromOwnerId }, 'QYlevyBodyArr')
    },
    pickQYChannel() {
      this.$refs['channelId'].resetField()
      this.$refs['toChannelId'].resetField()
      this.initAddRechInfo({ queryType: '3', merId: this.dialogQYData.fromOwnerId, levyId: this.dialogQYData.QYlveyBody }, 'QYchannelList')
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
