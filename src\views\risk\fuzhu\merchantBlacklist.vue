<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="商户编号" class="form-items">
                    <el-input v-model="formData.merchantNo" placeholder="请输入商户编号" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入商户名称" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="社会信用码" class="form-items">
                    <el-input v-model="formData.taxNo" placeholder="请输入社会信用码" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增商户黑名单</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantNo"
            fixed
            min-width="140"
            label="商户编号"
          />
          <el-table-column
            prop="merchantName"
            min-width="180"
            label="商户名称"
          />
          <el-table-column
            prop="taxNo"
            min-width="140"
            label="社会信用码"
          />
          <el-table-column
            prop="remark"
            min-width="300"
            label="备注"
          />
          <el-table-column
            prop="createTime"
            min-width="140"
            label="创建时间"
          />
          <el-table-column
            prop="merName"
            label="操作"
            fixed="right"
            min-width="100"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="edit(scope.row, '0')">修改</el-button>
              <el-button type="text" @click="edit(scope.row, '1')">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>

      <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">
        <el-scrollbar class="dialog-scroll">
          <el-form ref="dialogData" :model="dialogData" :rules="rules" :label-width="formLabelWidth" size="mini" class="form-style">
            <el-form-item label="商户名称" prop="merchantName">
              <el-select v-model="dialogData.merchantName" :disabled="dialogTitle == '编辑'" class="auto-width" filterable clearable remote placeholder="请输入关键词" :remote-method="getMerList" :loading="loading" @change="onSetMerNo">
                <el-option v-for="item in merArr" :key="item.id" :label="item.merName" :value="item.merName" />
              </el-select>
            </el-form-item>
            <el-form-item label="社会信用码" prop="taxNo">
              <el-input v-model="dialogData.taxNo" autocomplete="off" />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="dialogData.remark" type="textarea" :rows="2" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item>
              <div style="text-align: right">
                <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">保存</el-button>
                <el-button :loading="btnLoading" @click="dialogFormVisible = false">取消</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import risk from '@/axios/default/risk'
import publics from '@/axios/default/public'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'
export default {
  name: 'merchantBlacklist',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        merchantNo: '',
        merchantName: '',
        taxNo: ''
      },
      listData: [],
      merArr: [],
      loading: false,
      btnLoading: false,

      dialogData: this.initData(),
      dialogFormVisible: false,
      dialogTitle: '',
      formLabelWidth: '180px',
      rules: {
        merchantName: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
        taxNo: [{ required: true, message: '请输入社会信用码', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入备注信息', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    initData() {
      return {
        id: '',
        merchantNo: '',
        merchantName: '',
        taxNo: '',
        remark: '',
        deleteFlag: 0 // 0-新增/更新， 1- 删除
      }
    },
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    list() {
      risk.findMerchantBlacklists({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },

    add() {
      this.dialogFormVisible = true
      this.dialogTitle = '新增'
      this.$nextTick(function() {
        this.dialogData = this.initData()
      })
    },
    edit(args, status) {
      this.dialogData = args
      this.$set(this.dialogData, 'deleteFlag', status)
      if (status === '0') {
        // 新增/编辑
        this.dialogFormVisible = true
        this.dialogTitle = '编辑'
      } else {
        // 删除
        risk.addMerchantBlacklist({ ...this.dialogData }).then(res => {
          if (res.data.code == '0000') {
            this.dialogFormVisible = false
            this.btnLoading = false
            this.$message.success(res.data.message)
            this.list()
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      }
    },

    getMerList(query = '') {
      this.loading = true
      publics.merAllList({
        merName: query
      }).then(res => {
        this.loading = false
        this.merArr = res.data.data
      })
    },
    onSetMerNo(v) {
      const merNo = this.merArr.filter(item => item.merName === v)[0].merNo
      this.$set(this.dialogData, 'merchantNo', merNo)
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          risk.addMerchantBlacklist({ ...this.dialogData }).then(res => {
            if (res.data.code == '0000') {
              this.dialogFormVisible = false
              this.btnLoading = false
              this.$message.success(res.data.message)
              this.getMerList()
              this.list()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    },
    mounted() {
      this.getMerList()
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.auto-width{
  width: 100%;
}

.dialog-form{
  display: flex;
  flex-flow: column nowrap;
}
.el-form-item__content{
  margin-left: 0;
}

.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
  /deep/ .warning-row {
    background: #fbc4c4 !important;
  }
  /deep/ .warning-oldlace {
    background: oldlace !important;
  }

}
</style>
