<template>
  <div class="content-main">
    <div class="formData">
      <step1 :id="id" ref="step1" :data="formData" :view-merchant="viewMerchant" />
      <step2 :id="id" ref="step2" :is-audit="formData.isAudit" :edit-merchant="editMerchant" :data="formData" :view-merchant="viewMerchant" @changeModifyIsSave="getModifyIsSave" />
      <step3 :id="id" ref="step3" :data="formData" :view-merchant="viewMerchant" />
    </div>

    <div class="btn-bar">
      <el-button @click="goBack">返回</el-button>
      <template v-if="!viewMerchant">
        <template v-if="formData.isAudit">
          <!--        已提审-->
          <el-button v-if="isSave" @click="submit('formData','0')">保存</el-button>
          <el-button v-if="!isSave" :loading="subLoading" @click="submitView('formData','1')">提交审核</el-button>
        </template>
        <template v-else>
          <!--        未提审-->
          <el-button v-if="isSave" @click="submit('formData','0')">保存</el-button>
          <el-button :loading="subLoading" @click="submitView('formData','1')">提交审核</el-button>
        </template>
      </template>
    </div>
  </div>
</template>
<script>
import merchants from '@/axios/default/merchants'
import step1 from './step1'
import step2 from './step2'
import step3 from './step3'
import { newAlert } from '@/utils'

export default {
  name: 'MerAction',
  components: {
    step1,
    step2,
    step3
  },
  data() {
    return {
      id: '', // 商户ID
      formData: this.initData(),
      agentArr: [],
      saleArr: [],
      leaderArr: [],
      levyIdArr: [],
      subLoading: false, // 保存中状态
      viewMerchant: false, // 是否为查看详情
      editMerchant: false, // 是否为编辑商户
      isSave: true // true-保存按钮, false-审核按钮
    }
  },
  watch: {
    '$route.fullPath': {
      handler() {
        this.initPageData()
      }
    }
  },
  mounted() {
    this.initPageData()
  },
  methods: {
    initPageData() {
      this.id = this.$route.query.id
      this.viewMerchant = this.$route.query.page == 'view'
      this.editMerchant = this.$route.query.page == 'edit'
      if (this.id) {
        this.getData(this.id)
      } else {
        this.formData = this.initData()
        this.$refs.step1.$refs.step1Form.resetFields()
        this.$refs.step2.$refs.step2Form.resetFields()
        this.$refs.step2.invoiceArr = []
        this.$refs.step2.payArr = []
        this.$refs.step3.$refs.step3Form.resetFields()
        this.$refs.step3.fileList = []
      }
    },
    getModifyIsSave(result) {
      console.log(22222222222222, result)
      this.isSave = result
    },
    // 回显方法
    getData(id) {
      merchants.getOneMerchantInfo({
        id: id
      }).then(res => {
        if (res.data.code === '0000') {
          this.formData = { ...res.data.data }
          this.$refs.step1.initAgentInfo(this.formData.saleId)
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initData() {
      return {
        loginName: '',
        merNo: '',
        authEmail: '',
        preOpenFlag: 0,
        authMobile: '',
        projectLeaderId: '',
        businessName: '',
        saleId: '',
        businessMobile: '',
        agentId: '',
        isSendMsg: 0,
        isRisk: 1,
        isSendMsgRecharge: 0,
        isSpecialTicket: 1,
        shortName: '',
        'merName': '',
        'taxNo': '',
        'taxOpenBank': '',
        'postName': '',
        'contractPostAddr': '',
        'regAddr': '',
        'taxMobile': this.authMobile,
        'taxAcc': '',
        'postMobile': '',
        // 'invoiceTypeId': '',
        // 'itemCode': '',
        'isFreeLancer': 1,
        'isSetAudit': 0,
        'isUpConfirm': 0,
        'isUpSettlement': 0,
        'isCheck': 1,
        'frimLimitDay': '-1',
        'frimLimitMonth': '-1',
        'frimLimitYear': '-1',
        'levys': [{
          'levyIds': '',
          'feeRateTypes': '3',
          'feeRates': '',
          'taxFeeRateTypes': 0,
          'payChannelIds': [],
          'invoiceTypeIds': [],
          'isUploadCardPic': 0,
          freelancerLimitDay: '98000',
          freelancerLimitMonth: '98000',
          freelancerLimitYear: '1176000',
          minInvoicedAmount: '10000'
        }],
        isAudit: 0, // 是否已提交审核
        files: []
      }
    },

    // 提交表单
    submit(formName, saveFlag) {
      this.$refs.step1.formData.postMobile = this.$refs.step1.formData.authMobile
      this.$refs.step1.$refs.step1Form.validateField('loginName')
      this.$refs.step1.$refs.step1Form.validateField('merName')
      if (this.$refs.step1.formData.loginName == '') {
        newAlert(this.$tips, '请填写用户名！')
        return
      }
      if (this.$refs.step1.formData.merName == '') {
        newAlert(this.$tips, '请填写企业名称！')
        return
      }
      Object.assign(this.formData, this.$refs.step1.formData, this.$refs.step2.formData)
      this.formData.files = this.$refs.step3.fileList
      if (!this.checkRepeat(this.formData.levys)) {
        return false
      }
      if (this.id) {
        this.edit(saveFlag)
      } else {
        this.add(saveFlag)
      }
    },
    submitView(formName, saveFlag) {
      this.$refs.step1.formData.postMobile = this.$refs.step1.formData.authMobile
      Object.assign(this.formData, this.$refs.step1.formData, this.$refs.step2.formData)
      this.formData.files = this.$refs.step3.fileList
      if (!this.checkRepeat(this.formData.levys)) {
        return false
      }
      const form1 = this.$refs.step1.step1Form
      const form2 = this.$refs.step2.step2Form
      Promise.all([form1(), form2()]).then(res => {
        const validateResult = res.every(item => !!item)
        if (validateResult) {
          if (this.id) {
            this.edit(saveFlag)
          } else {
            this.add(saveFlag)
          }
        } else {
          this.$message({ type: 'error', message: '资料未填写完整' })
          return false
        }
      }).catch(error => {
        this.$message({ type: 'error', message: '资料未填写完整' })
      })
    },
    add(saveFlag) {
      this.subLoading = true
      const data = {
        ...this.formData,
        saveFlag: saveFlag
      }
      data.levys.forEach(item => {
        item.invoiceTypeIds.forEach((itemChild, indexChild) => {
          if (itemChild.length == 1) {
            item.invoiceTypeIds[indexChild] = itemChild[0]
          } else if (itemChild.length == 2) {
            item.invoiceTypeIds[indexChild] = itemChild[1]
          } else if (itemChild.length == 3) {
            item.invoiceTypeIds[indexChild] = itemChild[2]
          }
        })
      })
      merchants.addMerchantInfo(data).then(res => {
        this.subLoading = false
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.$router.push({
            path: '/merchants/list'
          })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    edit(saveFlag) {
      this.subLoading = true
      const data = {
        ...this.formData,
        saveFlag: saveFlag
      }

      data.levys.forEach(item => {
        item.invoiceTypeIds.forEach((itemChild, indexChild) => {
          if (itemChild.length == 1) {
            item.invoiceTypeIds[indexChild] = itemChild[0]
          } else if (itemChild.length == 2) {
            item.invoiceTypeIds[indexChild] = itemChild[1]
          } else if (itemChild.length == 3) {
            item.invoiceTypeIds[indexChild] = itemChild[2]
          }
        })
      })
      merchants.updateMerchantInfo(data).then(res => {
        this.subLoading = false
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.$router.push({
            path: '/merchants/list'
          })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    checkRepeat(arr) {
      const obj = {}
      const that = this
      let tag = true
      arr.forEach(function(res) {
        const name = that.formData.levys.find(r => r.levyIds == res.levyIds).levyName
        if (!obj[res.levyIds]) {
          obj[res.levyIds] = res.levyIds
        } else {
          console.log('重复' + name)
          tag = false
          setTimeout(function() {
            that.$message.error(`代征主体:<<${name}>>重复`)
          }, 100)
        }
      })
      return tag
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped lang="scss">
  .formData{
    background: #fff;
    padding: 20px 20px 10px 20px;
    min-height: calc(100% - 60px);
  }
  .form-items{
    width: 100%;
    display: flex;
  }
  .auto-width{
    width: 100%;
  }
  /deep/ .el-form-item__content{
    margin-left: 0!important;
  }

  .btn-bar{
    width: 100%;
    background: #fff;
    padding: 10px;
    text-align: right;
    margin-bottom: 10px;
  }

</style>
