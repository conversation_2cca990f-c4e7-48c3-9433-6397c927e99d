module.exports = {
  title: process.env.VUE_APP_BASE_TITLE,
  // 获取令牌时，请求头信息(Basic Base64.encode(client_id:client_secret))
  authorizationValue: 'Basic ZmViczoxMjM0NTY=',
  // 社交登录后台地址
  socialLoginUrl: 'http://221.204.177.29:8301/auth/social/login',
  // 请替换为您的实际地址
  pages: {
    // TaxPay-Admin 控制台地址
    springBootAdminUrl: 'http://221.204.177.29:8401/login',
    // kibana 控制台地址
    kibanaUrl: 'http://221.204.177.29:5601',
    // nacos 控制台地址
    nacosUrl: 'http://221.204.177.29:8001/nacos',
    // skywalking地址
    skywalkingUrl: 'http://221.204.177.29:8080/',
    // 文档中心
    docUrl: 'http://221.204.177.29:8301/doc.html',
    // Granfana控制台
    grafanaUrl: 'http://221.204.177.29:8404/',
    // tx-manager控制台
    txUrl: 'http://221.204.177.29:8501/admin/index.html#/login'
  }
}
