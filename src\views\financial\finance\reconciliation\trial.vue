<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="调帐时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                      @change="resetTime"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="调帐企业" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="调帐时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="accountName"
            label="被调帐企业"
            width="180"
          />
          <el-table-column
            prop="channelName"
            label="被调账通道"
            width="180"
          />
          <el-table-column
            prop="realAmount"
            label="被调账金额"
            width="100"
          />
          <el-table-column
            prop="toAccountName"
            label="至调账企业"
            width="180"
          />
          <el-table-column
            prop="toChannelName"
            label="至调账通道"
            width="180"
          />
          <el-table-column
            prop="realAmount"
            label="至调账金额"
            width="100"
          />
          <el-table-column
            prop="userName"
            label="申请人"
            width="100"
          />
          <el-table-column
            prop="adjustDesc"
            label="备注"
            width="200"
          />
          <el-table-column
            prop="fileType"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="edit(scope.row)">审核</el-button>
            </template>
          </el-table-column>

        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>
    </div>

    <!--    调账初审-->
    <el-dialog title="调账初审" :visible.sync="dialogTrial" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogTrialData" :model="dialogTrialData" size="mini" class="form-style" label-width="80px" :rules="rules">
          <el-row>
            <el-col :span="11">
              <el-form-item label="企业名称">
                {{ dialogTrialData.accountName }}
              </el-form-item>
              <el-form-item label="通道名称">
                {{ dialogTrialData.channelName }}
              </el-form-item>
              <el-form-item label="银行账号">
                {{ dialogTrialData.accountNo }}
              </el-form-item>
              <el-form-item label="" label-width="80px">
                账户调减 {{ dialogTrialData.realAmount }}元
              </el-form-item>
            </el-col>

            <el-col :span="2">
              -》
            </el-col>

            <el-col :span="11">
              <el-form-item label="企业名称">
                {{ dialogTrialData.toAccountName }}
              </el-form-item>
              <el-form-item label="通道名称">
                {{ dialogTrialData.toChannelName }}
              </el-form-item>
              <el-form-item label="银行账号">
                {{ dialogTrialData.toAccountNo }}
              </el-form-item>
              <el-form-item label="" label-width="80px">
                账户调增 {{ dialogTrialData.realAmount }}元
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="*提交备注" label-width="80px">
            {{ dialogTrialData.adjustDesc }}
          </el-form-item>
          <el-form-item label="备注" label-width="80px" prop="firistAuditRemark">
            <el-input v-model="dialogTrialData.firistAuditRemark" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="" label-width="80px" class="form-items">
            <el-button type="primary" @click="submit('dialogTrialData','2')">通过</el-button>
            <el-button @click="submit('dialogTrialData','3')">不通过</el-button>
          </el-form-item>

        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import account from '@/axios/default/finance'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Trial',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      adjustStatusEnum: { 1: '待审核', 2: '初审通过', 3: '初审拒绝', 4: '复审拒绝', 5: '调账完成' },
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        accountName: ''
      },
      // formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogTrial: false,
      dialogTrialData: {},
      rules: {
        firistAuditRemark: [{ required: true, message: '请填写备注', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    initDialog() {
      return {

      }
    },
    edit(data) {
      this.dialogTrial = true
      this.dialogTrialData = this.initDialog()
      this.dialogTrialData = data
    },
    list() {
      account.findAccuntInfoList({
        ...this.formData,
        adjustStatus: '1',
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    submit(formName, adjustStatus) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.auditAccountAdjust(adjustStatus)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    auditAccountAdjust(adjustStatus) {
      account.auditAccountAdjust({
        'id': this.dialogTrialData.id,
        'adjustStatus': adjustStatus,
        'firistAuditRemark': this.dialogTrialData.firistAuditRemark
      }).then(res => {
        if (res.data.code === '0000') {
          this.dialogTrial = false
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    }
  }

}
</script>
<style scoped lang="scss">
.list-card {
  margin-bottom: 20px;
}

.auto-width {
  width: 100%;
}

.dialog-form {
  display: flex;
  flex-flow: column nowrap;
}

.el-form-item__content {
  margin-left: 0;
}

.list-table {
  margin: 0 0 20px 0;

  .el-button {
    padding: 0;
  }
}

.dialog-scroll {
  overflow-y: auto;
  height: calc(100%);
}

.reset-dialog {
  /deep/ .el-dialog {
    height: 80vh;
    overflow: hidden;

    .el-dialog__body {
      height: calc(100% - 54px);
    }
  }

  overflow: hidden;
}

.reset-dialog-small {
  /deep/ .el-dialog {
    height: 65vh;
  }

  overflow: hidden;
}

.form-style {
  padding-right: 20px;
}

</style>
