<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <router-view v-if="cache" :key="key" />
      <keep-alive v-else :exclude="exclude">
        <router-view :key="key" />
      </keep-alive>

    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  data() {
    return {
      exclude: []
    }
  },
  computed: {
    key() {
      return this.$route.path
    },
    cache() {
      return this.$route.meta.noCache
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-main {
    /* 50= navbar  50  */
    min-height: calc(100vh - 50px);
    width: 100%;
    position: relative;
    overflow: hidden;
    background: #f3f5f8;
    font-size: .88rem;
  }

  .fixed-header+.app-main {
    padding-top: 50px;
    height: 100vh;
    overflow: auto;
  }

  .hasTagsView {
    .app-main {
      /* 84 = navbar + tags-view = 50 + 34 */
      min-height: calc(100vh - 84px);
    }

    .fixed-header+.app-main {
      padding-top: 84px;
      overflow-x: hidden;
    }
  }
</style>

<style lang="scss">
  // fix css style bug in open el-dialog
  .el-popup-parent--hidden {
    .fixed-header {
      padding-right: 15px;
    }
  }
</style>
