// 结算通道管理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  findPayChannelInfoList: `${API_Header}/payChannelInfo/findPayChannelInfoList`, // 结算列表查询接口
  addPayChannelInfo: `${API_Header}/payChannelInfo/addPayChannelInfo`, // 新增通道
  updatePayChannelInfo: `${API_Header}/payChannelInfo/updatePayChannelInfo`, // 修改通道
  findAccountInfoList: `${API_Header}/accounInfo/findAccountInfoList`, // 企业（子）账户管理
  findAccountInfoDownload: `${API_Header}/accounInfo/download`, // 企业（子）账户管理下载
  updateAccountInfo: `${API_Header}/accounInfo/updateAccountInfo`, // 企业（子）账户管理
  findWechatPayMerchantRelationList: `${API_Header}/wechatPayMerchantRelation/findWechatPayMerchantRelationList`, // 查询微信通道商户列表
  findWechatPayMerchant: `${API_Header}/wechatPayMerchantRelation/findWechatPayMerchant`, // 根据代征主体 id 查询已开通微信通道商户信息
  saveWechatPayMerchantRelation: `${API_Header}/wechatPayMerchantRelation/saveWechatPayMerchantRelation`, // 新增微信通道
  updateWechatPayMerchantRelation: `${API_Header}/wechatPayMerchantRelation/updateWechatPayMerchantRelation`, // 修改微信通道
  deleteWechatPayMerchantRelation: `${API_Header}/wechatPayMerchantRelation/deleteWechatPayMerchantRelation` // 删除微信通道
}

const payChannelInfo = {
  findPayChannelInfoList: params => {
    return request.postJson(api.findPayChannelInfoList, params)
  },
  addPayChannelInfo: params => {
    return request.postJson(api.addPayChannelInfo, params)
  },
  updatePayChannelInfo: params => {
    return request.postJson(api.updatePayChannelInfo, params)
  },
  findAccountInfoList: params => {
    return request.postJson(api.findAccountInfoList, params)
  },
  updateAccountInfo: params => {
    return request.postJson(api.updateAccountInfo, params)
  },
  findWechatPayMerchantRelationList: params => {
    return request.postJson(api.findWechatPayMerchantRelationList, params)
  },
  findWechatPayMerchant: params => {
    return request.postJson(api.findWechatPayMerchant, params)
  },
  saveWechatPayMerchantRelation: params => {
    return request.postJson(api.saveWechatPayMerchantRelation, params)
  },
  updateWechatPayMerchantRelation: params => {
    return request.postJson(api.updateWechatPayMerchantRelation, params)
  },
  deleteWechatPayMerchantRelation: params => {
    return request.postJson(api.deleteWechatPayMerchantRelation, params)
  },
  findAccountInfoDownload: (params, filename, type) => {
    return request.downFiles(api.findAccountInfoDownload, params, filename, type)
  }
}

export default payChannelInfo
