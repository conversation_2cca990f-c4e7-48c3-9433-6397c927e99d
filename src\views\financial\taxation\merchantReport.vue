<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="6" :xs="24">
              <el-form-item label="统计方式" class="form-items">
                <el-radio-group v-model="formData.reportWay" @change="handleReportWay">
                  <el-radio-button label="2">按月</el-radio-button>
                  <el-radio-button label="1">按日</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="统计日期" class="form-items">
                <el-date-picker
                  v-if="formData.reportWay == 1"
                  v-model="pickTime"
                  class="auto-width"
                  type="date"
                  align="right"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                  :picker-options="pickerOptionsDay"
                  key="day"
                  @change="resetTime"
                >
                </el-date-picker>

                <el-date-picker
                  v-else
                  v-model="pickTime"
                  class="auto-width"
                  type="daterange"
                  unlink-panels
                  align="right"
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="pickerOptionsMonth"
                  key="month"
                  @change="resetTime"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="10" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <template>
          <el-tabs v-model="formData.type" @tab-click="onSearch">
            <el-tab-pane label="充值统计" name="0">
              <el-table
                :data="listData"
                border
                size="mini"
                class="list-table"
              >
                <el-table-column
                  prop="mer_name"
                  min-width="260"
                  label="企业名称"
                  fixed="left"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="saleName"
                  min-width="160"
                  label="销售名称"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="name"
                  min-width="160"
                  label="代理名称"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="money"
                  min-width="140"
                  label="充值金额"
                  align="right"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="counts"
                  min-width="140"
                  label="充值笔数"
                  show-overflow-tooltip
                />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="结算统计" name="1">
              <el-table
                :data="listData"
                border
                size="mini"
                class="list-table"
              >
                <el-table-column
                  prop="mer_name"
                  min-width="260"
                  label="企业名称"
                  fixed="left"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="saleName"
                  min-width="160"
                  label="销售名称"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="name"
                  min-width="160"
                  label="代理名称"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="money"
                  min-width="140"
                  label="结算金额"
                  align="right"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="counts"
                  min-width="140"
                  label="结算笔数"
                  show-overflow-tooltip
                />
              </el-table>
            </el-tab-pane>

            <el-button v-if="formData.type == 1" type="primary" size="mini" @click="exportExcel"><i class="el-icon-download"></i>导出结算报表</el-button>
          </el-tabs>
        </template>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import financial from '@/axios/default/financial'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { parseTime, newAlert, formatNumber } from '@/utils'

export default {
  name: 'MerchantReport',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      listData: [],
      pickTime: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())],
      pickerOptionsDay: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now()
        // }
      },
      pickerOptionsMonth: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now()
        // },
        shortcuts: [{
          text: '当月至今',
          onClick(picker) {
            const end = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
            const start = new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 3)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      formData: {
        reportWay: 2, // 1--当日， 2--当月
        type: '0', // 报表类型 0--充值， 1--结算
        createTimeStart: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()),
        createTimeEnd: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
      }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    resetTime(val) {
      this.pickTime = val
    },
    handleReportWay(val) {
      if (val == 1) {
        // 按日
        this.pickTime = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
      } else {
        // 按月
        this.pickTime = [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())]
      }
      this.onSearch()
    },
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      if (this.formData.reportWay == 1) {
        // 按日
        this.formData.createTimeStart = moment(this.pickTime).format('YYYY-MM-DD')
        this.formData.createTimeEnd = moment(this.pickTime).format('YYYY-MM-DD')
      } else {
        // 按月
        this.formData.createTimeStart = moment(this.pickTime[0]).format('YYYY-MM-DD')
        this.formData.createTimeEnd = moment(this.pickTime[1]).format('YYYY-MM-DD')
      }

      financial.merList({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          res.data.data.rows.forEach(item => {
            item.money = formatNumber(item.money)
          })
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },

    exportExcel() {
      if (this.formData.reportWay == 1) {
        // 按日
        this.formData.createTimeStart = moment(this.pickTime).format('YYYY-MM-DD')
        this.formData.createTimeEnd = moment(this.pickTime).format('YYYY-MM-DD')
      } else {
        // 按月
        this.formData.createTimeStart = moment(this.pickTime[0]).format('YYYY-MM-DD')
        this.formData.createTimeEnd = moment(this.pickTime[1]).format('YYYY-MM-DD')
      }

      financial.merListDownLoadData({
        ...this.formData
      }, `商户结算报表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },

    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}
.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
}

/deep/ .el-radio-button__inner:hover {
  color: #f56c6c;
}

/deep/ .el-radio-button__orig-radio:checked+.el-radio-button__inner {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  box-shadow: -1px 0px 0px 0px #f56c6c;
}

/deep/ .el-tabs__content {
  overflow: inherit !important;
  button {
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    margin-top: -50px;
  }
}

.auto-width {
  width: 100%;
}

</style>
