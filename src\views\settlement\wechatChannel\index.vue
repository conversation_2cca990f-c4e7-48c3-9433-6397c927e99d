<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="8">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="6">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :offset="6" :lg="2" :xs="2">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
                <el-col :lg="2" :xs="2">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" type="primary" @click="add">新增</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="levyName"
            label="代征主体"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            show-overflow-tooltip
            prop="channelName"
            width="200"
            label="通道名称"
          />
          <el-table-column
            show-overflow-tooltip
            prop="merNo"
            width="200"
            label="商户编号"
          />
          <el-table-column
            prop="merName"
            show-overflow-tooltip
            width="200"
            label="商户名称"
          />
          <el-table-column
            prop="wechatSubMchid"
            show-overflow-tooltip
            label="微信商户号"
            width="200"
          />
          <el-table-column
            prop="wechatSubAppid"
            show-overflow-tooltip
            width="200"
            label="微信appid"
          />
          <el-table-column
            prop=""
            width="110"
            label="操作"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="edit(scope.row)">修改</el-button>
              <el-button type="text" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog :title="dialogTitile" :visible.sync="dialogBoxShow" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogBoxData" :model="dialogBoxData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="代征主体名称" prop="levyId">
            <el-select v-model="dialogBoxData.levyId" placeholder="请选择" class="auto-width" @change="onLevyChange">
              <el-option
                v-for="item in levyIdArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="商户名称" prop="merId">
            <el-select v-model="dialogBoxData.merId" :disabled="!dialogBoxData.levyId" placeholder="请选择" class="auto-width" @change="onMerChange">
              <el-option
                v-for="item in merArr"
                :key="item.merId"
                :label="item.merName"
                :value="item.merId"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="dialogBoxData.merId" label="商户编号">
            <span>{{ merNo }}</span>
          </el-form-item>
          <el-form-item label="微信商户号" prop="wechatSubMchid">
            <el-input v-model="dialogBoxData.wechatSubMchid" autocomplete="off" />
          </el-form-item>
          <el-form-item label="微信appid" prop="wechatSubAppid">
            <el-input v-model="dialogBoxData.wechatSubAppid" autocomplete="off" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" @click="submit('dialogBoxData')">保存</el-button>
              <el-button @click="dialogBoxShow=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import payChannelInfo from '@/axios/default/payChannelInfo'
import levyBody from '@/axios/default/levyBody'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      buttonLoading: false,
      formData: {
        levyId: '',
        merName: '',
        levyName: ''
      },
      merNo: '', // 商户编号
      listData: [],
      dialogBoxShow: false,
      dialogBoxData: this.initData(),
      dialogType: '',
      dialogTitile: '',
      levyIdArr: [],
      merArr: [], // 当前主体下的已开通的商户
      disabled1: false,
      disabled2: false,
      calcType: { 1: '费率', 2: '按笔', 3: '费率+单笔' },
      rules: {
        levyId: [{ required: true, message: '请选择代征主体名称', trigger: ['change'] }],
        merId: [{ required: true, message: '请选择商户', trigger: ['change'] }],
        wechatSubMchid: [{ required: true, message: '微信商户号', trigger: 'blur' }],
        wechatSubAppid: [{ required: true, message: '微信appid', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  methods: {
    initData() {
      return {
        id: '',
        levyId: '',
        merId: '',
        channelId: '',
        wechatSubMchid: '',
        wechatSubAppid: ''
      }
    },

    onLevyChange(levyId) {
      payChannelInfo.findWechatPayMerchant({
        levyId
      }).then(res => {
        if (res.data.code === '0000') {
          this.merArr = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },

    onMerChange(merId) {
      const currentMer = this.merArr.filter(item => item.merId === merId)[0]
      this.merNo = currentMer.merNo
      this.$set(this.dialogBoxData, 'channelId', currentMer.channelId)
    },

    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.buttonLoading = true

          if (this.dialogType === 'add') {
            payChannelInfo.saveWechatPayMerchantRelation({
              ...this.dialogBoxData
            }).then(res => {
              if (res.data.code === '0000') {
                this.callback()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          } else {
            payChannelInfo.updateWechatPayMerchantRelation({
              ...this.dialogBoxData
            }).then(res => {
              if (res.data.code === '0000') {
                this.callback()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    callback() {
      this.buttonLoading = false
      this.dialogBoxShow = false
      this.findWechatPayMerchantRelationList()
    },
    add() {
      this.dialogTitile = '新增微信通道配置'
      this.dialogType = 'add'
      this.dialogBoxData = this.initData()
      this.dialogBoxShow = true
    },
    edit(data) {
      this.dialogTitile = '编辑微信通道配置'
      Object.assign(this.dialogBoxData, data)
      this.merNo = data.merNo
      this.onLevyChange(data.levyId)
      this.dialogType = 'edit'
      this.dialogBoxShow = true
    },
    del(data) {
      this.$confirm('是否确定删除此微信通道？', '删除微信通道配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        payChannelInfo.deleteWechatPayMerchantRelation({
          id: data.id
        }).then(res => {
          if (res.data.code === '0000') {
            this.$message({
              type: 'success',
              message: res.data.message
            })
            this.callback()
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    onSearch() {
      this.pageNum = 1
      this.findWechatPayMerchantRelationList()
    },
    findWechatPayMerchantRelationList() {
      payChannelInfo.findWechatPayMerchantRelationList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        ...this.formData
      }).then(res => {
        console.log(res.data.data)
        this.listData = res.data.data.rows
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
        this.total = res.data.data.total
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.findWechatPayMerchantRelationList()
    },
    queryLevyBodyInfos() {
      levyBody.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyIdArr = res.data.data
          if (this.levyIdArr.length === 1) {
            this.formData.levyId = this.levyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
        this.findWechatPayMerchantRelationList()
      })
    },
    changeRadio(val) {
      if (val == '1') {
        this.disabled1 = false
        this.disabled2 = true
      } else if (val == '2') {
        this.disabled1 = true
        this.disabled2 = false
      }
    },
    checkMore(rule, value, callback) {
      console.log(value)
      if (value == '') {
        return callback(new Error('请选择收费类型'))
      } else {
        if (value == '1') {
          if (this.dialogBoxData.fee == '') {
            return callback(new Error('请输入正确的费率'))
          } else {
            callback()
          }
        } else if (value == '2') {
          if (this.dialogBoxData.singleFee == '') {
            return callback(new Error('请输入正确的单笔收费'))
          } else {
            callback()
          }
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .auto-width{
    width: 100%;
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 60vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

</style>
