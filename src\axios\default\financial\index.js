// 财务统计
import request from '@/axios/default/request'
import { API_system, API_system as API_Header } from '../index'

const api = {
  list: `${API_Header}/taxDeclareDetails/list`, // 申报明细
  exportTaxDetails: `${API_Header}/taxDeclareDetails/export`, // 导出申报明细
  queryInvoiceTotal: `${API_Header}/invoiceInfo/queryInvoiceTotal`, // 发票统计

  finList: `${API_Header}/profitInfo/findProfitList`, // 财务月度报表
  excel: `${API_Header}/profitInfo/excel`, // 财务月度报表

  addMessageVO: `${API_Header}/invoiceInfo/addMessageVO`, // 开票通知短信接口
  downInvoiceTotal: `${API_Header}/invoiceInfo/downInvoiceTotal`, // 发票统计
  queryAccHistoryByPage: `${API_Header}/accountHistory/queryAccHistoryByPage`, // 账户流水

  queryFeeByBank: `${API_Header}/accounInfo/queryFeeByBank`, // 手续费余额

  merList: `${API_Header}/financialStatistics/merList`, // 商户报表
  merListDownLoadData: `${API_Header}/financialStatistics/merListDownLoadData`, // 商户结算导出
  saleList: `${API_Header}/financialStatistics/saleList`, // 销售报表
  queryMerList: `${API_Header}/financialStatistics/queryMerList` // 业务统计页面

}

const financial = {
  list: params => {
    return request.postJson(api.list, params)
  },
  exportTaxDetails: (params, filename, type) => {
    return request.downFiles(api.exportTaxDetails, params, filename, type)
  },
  queryInvoiceTotal: params => {
    return request.postJson(api.queryInvoiceTotal, params)
  },
  finList: params => {
    return request.postJson(api.finList, params)
  },
  finExcel: (params, filename, type) => {
    return request.downFiles(api.excel, params, filename, type)
  },
  addMessageVO: (params) => {
    return request.postJson(api.addMessageVO, params)
  },
  downInvoiceTotal: (params, filename, type) => {
    return request.downFiles(api.downInvoiceTotal, params, filename, type)
  },
  queryAccHistoryByPage: params => {
    return request.postJson(api.queryAccHistoryByPage, params)
  },
  merList: params => {
    return request.postJson(api.merList, params)
  },
  queryMerList: params => {
    return request.postJson(api.queryMerList,params)
  },
  queryFeeByBank: params => {
    return request.postJson(api.queryFeeByBank, params)
  },
  merListDownLoadData: (params, filename, type) => {
    return request.downFiles(api.merListDownLoadData, params, filename, type)
  },
  saleList: params => {
    return request.postJson(api.saleList, params)
  }
}

export default financial

