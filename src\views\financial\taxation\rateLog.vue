<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">

        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="变更申请时间起" class="form-items" prop="applyTimeFrom">
                <el-date-picker
                  v-model="formData.applyTimeFrom"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="变更申请时间止" class="form-items" prop="applyTimeTo">
                <el-date-picker
                  v-model="formData.applyTimeTo"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="企业名称" class="form-items" prop="merName">
                <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="变更完成时间起" class="form-items" prop="auditTimeFrom">
                <el-date-picker
                  v-model="formData.auditTimeFrom"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="变更完成时间止" class="form-items" prop="auditTimeTo">
                <el-date-picker
                  v-model="formData.auditTimeTo"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="项目负责人" class="form-items" prop="leaderName">
                <el-input v-model="formData.leaderName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>

          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="所属销售" class="form-items" prop="saleName">
                <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="代征主体" class="form-items" prop="levyName">
                <el-select v-model="formData.levyName" placeholder="请选择" class="auto-width">
                  <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                  <el-option
                    v-for="item in levyBodyIdArr"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :lg="24" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                <el-button icon="el-icon-search" type="primary" @click="feeRateDownLoadData">下载数据</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      </el-card>
      <el-card>
        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merNo"
            label="商户编号"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
            prop="merName"
            width="250"
            label="企业名称"
            show-overflow-tooltip
          />

          <el-table-column
            prop="levyName"
            label="代征主体"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
            prop="oldFeeRate"
            label="原费率(%)"
            show-overflow-tooltip
          />
          <el-table-column
            prop="newFeeRate"
            label="新费率(%)"
            show-overflow-tooltip
          />

          <el-table-column
            prop="createTime"
            label="变更时间"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="leaderName"
            label="项目负责人"
            width="100"
            show-overflow-tooltip
          />

          <el-table-column
            prop="saleName"
            label="所属销售"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="applyTime"
            label="变更申请时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="auditTime"
            label="变更完成时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createName"
            label="操作人"
            width="100"
            show-overflow-tooltip
          />

        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

  </div>
</template>
<script>
import tradeService from '@/axios/default/tradeService'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import publicApi from '@/axios/default/public'
import moment from 'moment'
export default {
  name: 'RateLog',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        // applyTimeFrom: new Date(moment(new Date()).startOf('day')),
        // applyTimeTo: new Date(moment(new Date()).endOf('day')),
        applyTimeFrom: null,
        applyTimeTo: null,
        auditTimeFrom: null,
        auditTimeTo: null,
        merName: '',
        levyName: '',
        leaderName: '',
        saleName: ''
      },
      levyBodyIdArr: [],
      channelArr: [],
      formLabelWidth: '180px',
      listData: []
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  activated() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      this.formData.applyTimeFrom = this.formData.applyTimeFrom ? moment(this.formData.applyTimeFrom).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.applyTimeTo = this.formData.applyTimeTo ? moment(this.formData.applyTimeTo).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.auditTimeFrom = this.formData.auditTimeFrom ? moment(this.formData.auditTimeFrom).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.auditTimeTo = this.formData.auditTimeTo ? moment(this.formData.auditTimeTo).format('YYYY-MM-DD HH:mm:ss') : ''
      tradeService.findFeeRateRecords({
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        ...this.formData
      }).then(res => {
        if (res.data.code == '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    feeRateDownLoadData() {
      this.formData.applyTimeFrom = this.formData.applyTimeFrom ? moment(this.formData.applyTimeFrom).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.applyTimeTo = this.formData.applyTimeTo ? moment(this.formData.applyTimeTo).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.auditTimeFrom = this.formData.auditTimeFrom ? moment(this.formData.auditTimeFrom).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.auditTimeTo = this.formData.auditTimeTo ? moment(this.formData.auditTimeTo).format('YYYY-MM-DD HH:mm:ss') : ''
      tradeService.feeRateDownLoadData({
        ...this.formData
      }, `费率日志${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    // 主体
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyBodyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
        // this.findAccountInfoList()
      })
    },
    // 通道
    queryChannelInfoByLevyId(levyBodyId) {
      this.formData.channelId = ''
      publicApi.queryChannelInfoByLevyId({ levyBodyId }).then(res => {
        if (res.data.code === '0000') {
          this.channelArr = res.data.data
          if (this.channelArr.length === 1) {
            this.formData.channelId = this.channelArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .table-head-money .el-col .table-headers{
    display: flex;
    flex-flow: nowrap column;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #eff2f6;
    .table-header-num{
      font-size: 18px;
      margin-bottom: 5px;
      color: #333;
    }
    .table-header-name{
      font-size: 12px;
      color: #999;
    }
  }
  .table-head-money .el-col:last-child .table-headers{
    border-right: none;
    .table-header-num{
      color: #F54343;
    }
  }

</style>

