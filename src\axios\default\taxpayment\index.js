// 完税
import request from '@/axios/default/request'
import { API_system } from '../index'

const api = {
  findTaxDeclareFileByPage: `${API_system}/taxDeclareFile/findTaxDeclareFileByPage`, // 完税列表
  addTaxDeclareFile: `${API_system}/taxDeclareFile/addTaxDeclareFile`, // 上传完税文件
  updateTaxDeclareFile: `${API_system}/taxDeclareFile/updateTaxDeclareFile` // 更新申报文件

}

const riskApi = {
  findTaxDeclareFileByPage: params => {
    return request.postJson(api.findTaxDeclareFileByPage, params)
  },
  addTaxDeclareFile: params => {
    return request.postJson(api.addTaxDeclareFile, params)
  },
  updateTaxDeclareFile: params => {
    return request.postJson(api.updateTaxDeclareFile, params)
  }
}

export default riskApi

