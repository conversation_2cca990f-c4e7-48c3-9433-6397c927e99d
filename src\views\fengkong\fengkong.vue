<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="商户编号" class="form-items">
                <el-input v-model="formData.merNo" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="商户名称" class="form-items">
                <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="24" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <el-card>
        <el-table
          v-loading="loading"
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merNo"
            label="商户编号"
            width="180"
          />
          <el-table-column
            prop="merName"
            label="商户名称"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="payeeName"
            label="姓名"
          />
          <el-table-column
            prop="telephone"
            label="电话"
          />
          <el-table-column
            prop="payeeIdCard"
            label="证件号"
            width="180"
          />
          <el-table-column
            prop="settMoney"
            label="结算金额(元)"
            width="100"
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import fengkong from '@/axios/default/fengkong'
export default {
  name: 'Fengkong',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        tradeTime: '',
        merName: '',
        merNo: ''
      },
      listData: [],
      loading: true
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      fengkong.freeList({
        'pageSize': this.pageSize,
        'pageNum': this.pageNum,
        ...this.formData
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
          this.loading = false
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    }
  }
}
</script>
<style scoped lang="scss">
  .auto-width{
    width: 100%;
  }
  .list-card{
    margin-bottom: 20px;
  }
  .auto-width{
    width: 100%;
  }
</style>
