<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <div class="money-title table-top">
          结算人数：{{ tradeNum || 0 }}人 结算金额：{{ settMoney || 0 }}元 服务费：{{ settPlatFee || 0 }}元
        </div>
        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column property="payeeName" label="姓名" width="100" />
          <el-table-column property="payeeIdCard" label="身份证号" width="200" />
          <el-table-column property="payeeAcc" label="银行卡号" width="200" />
          <el-table-column property="telephone" label="手机号" width="150" />
          <el-table-column property="settMoney" label="结算金额" width="150" />
          <el-table-column property="platFee" label="服务费" width="100" />
          <el-table-column property="channelName" show-overflow-tooltip label="通道名称" width="200" />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import tradeService from '@/axios/default/tradeService'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'List',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      listData: [],
      id: '',
      tradeNum: '',
      settMoney: '',
      settPlatFee: ''
    }
  },
  mounted() {
    this.initPageData()
  },
  methods: {
    initPageData() {
      this.id = this.$route.query.id
      this.queryTradeDetailByTradeId(this.id)
    },
    queryTradeDetailByTradeId(id) {
      tradeService.queryTradeDetailByTradeId({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'tradeId': id
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.tradeNum = res.data.data.tradeNum
          this.settMoney = res.data.data.settMoney
          this.settPlatFee = res.data.data.settPlatFee
          this.total = res.data.data.total
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.initPageData()
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>

