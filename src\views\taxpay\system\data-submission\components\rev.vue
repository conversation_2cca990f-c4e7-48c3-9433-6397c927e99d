<template>
  <!-- 数据表格 -->
  <el-table :data="tableData" border stripe size="small" align="center">
    <el-table-column prop="序号" label="序号" width="60" fixed="left">
      <template slot-scope="scope">
        {{ scope.$index + 1 }}
      </template>
    </el-table-column>

    <el-table-column prop="shopName" label="是否已取得登记征照" width="150">
      <el-table-column label="1" />
    </el-table-column>

    <el-table-column label="已取得登记证照">
      <el-table-column prop="province" label="名称（姓名）">
        <el-table-column label="2" />
      </el-table-column>
      <el-table-column
        prop="province"
        label="统一社会信用代码（纳税人识别号）"
      >
        <el-table-column label="3" />
      </el-table-column>
    </el-table-column>

    <el-table-column label="未取得登记征照">
      <el-table-column label="姓名">
        <el-table-column label="4" />
      </el-table-column>
      <el-table-column label="证件类型">
        <el-table-column label="5" />
      </el-table-column>
      <el-table-column label="证件号码">
        <el-table-column label="6" />
      </el-table-column>
      <el-table-column label="国家或地区">
        <el-table-column label="7" />
      </el-table-column>
    </el-table-column>

    <el-table-column label="收入来源的互联网平台名称">
      <el-table-column label="8" />
    </el-table-column>
    <el-table-column label="收入来源的店铺（用户）名称">
      <el-table-column label="9" />
    </el-table-column>
    <el-table-column label="收入来源的店铺（用户）唯一标识码">
      <el-table-column label="10" />
    </el-table-column>

    <el-table-column label="已取得登记证照的平台内经营者收入信息">
      <el-table-column label="销售货物取得的收入">
        <el-table-column label="收入总额">
          <el-table-column label="11" />
        </el-table-column>
        <el-table-column label="退款金额">
          <el-table-column label="12" />
        </el-table-column>
        <el-table-column label="收入净额">
          <el-table-column label="13=11-12" />
        </el-table-column>
      </el-table-column>
      <el-table-column label="销售无形资产取得的收入">
        <el-table-column label="收入总额">
          <el-table-column label="14" />
        </el-table-column>
        <el-table-column label="退款金额">
          <el-table-column label="15" />
        </el-table-column>
        <el-table-column label="收入净额">
          <el-table-column label="16=14-15" />
        </el-table-column>
      </el-table-column>
      <el-table-column label="销售服务取得的收入">
        <el-table-column label="收入总额">
          <el-table-column label="17" />
        </el-table-column>
        <el-table-column label="退款金额">
          <el-table-column label="18" />
        </el-table-column>
        <el-table-column label="收入净额">
          <el-table-column label="19=17-18" />
        </el-table-column>
      </el-table-column>
    </el-table-column>

    <el-table-column label="未取得登记证照的平台的经营者和从业人员收入信息">
      <el-table-column label="收入总额">
        <el-table-column label="20" />
      </el-table-column>
      <el-table-column label="退款金额">
        <el-table-column label="21" />
      </el-table-column>
      <el-table-column label="收入净额">
        <el-table-column label="小计">
          <el-table-column
            label="22=20-21=23+24+25+26+27"
          />
        </el-table-column>
        <el-table-column label="其中：">
          <el-table-column label="销售货物">
            <el-table-column label="23" />
          </el-table-column>
          <el-table-column label="运输服务">
            <el-table-column label="24" />
          </el-table-column>
          <el-table-column label="劳务报酬">
            <el-table-column label="25" />
          </el-table-column>
          <el-table-column label="稿酬">
            <el-table-column label="26" />
          </el-table-column>
          <el-table-column label="特许权使用费">
            <el-table-column label="27" />
          </el-table-column>
        </el-table-column>
      </el-table-column>
    </el-table-column>

    <el-table-column label="从事其他网络交易活动取得的收入（净额）">
      <el-table-column label="28" />
    </el-table-column>
    <el-table-column label="支付给平台的佣金、服务费合计金额">
      <el-table-column label="29" />
    </el-table-column>
    <el-table-column label="交易（订单）数量（笔）">
      <el-table-column label="30" />
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'Rev',
  data() {
    return {
      tableData: [
        {
          shopName: '张三的小店',
          shopId: 'UID123456',
          name: '张三',
          idNumber: '110101199001011234',
          reportPeriod: '2024-01',
          totalIncome: '50000.00',
          taxableIncome: '45000.00',
          exemptIncome: '5000.00',
          withheldTax: '4500.00',
          serviceFee: '500.00',
          incomeType: '经营所得',
          paymentMethod: '支付宝',
          paymentDate: '2024-01-31',
          remarks: '正常经营收入',
          status: '新增'
        }
      ]
    }
  },
  computed: {
    totalIncomeSum() {
      return this.tableData.reduce((sum, item) => {
        return sum + (parseFloat(item.totalIncome) || 0)
      }, 0)
    },
    taxableIncomeSum() {
      return this.tableData.reduce((sum, item) => {
        return sum + (parseFloat(item.taxableIncome) || 0)
      }, 0)
    },
    withheldTaxSum() {
      return this.tableData.reduce((sum, item) => {
        return sum + (parseFloat(item.withheldTax) || 0)
      }, 0)
    }
  },
  methods: {
    addRow() {
      this.tableData.push({
        shopName: '',
        shopId: '',
        name: '',
        idNumber: '',
        reportPeriod: '',
        totalIncome: '0.00',
        taxableIncome: '0.00',
        exemptIncome: '0.00',
        withheldTax: '0.00',
        serviceFee: '0.00',
        incomeType: '',
        paymentMethod: '',
        paymentDate: '',
        remarks: '',
        status: '新增'
      })
    },
    deleteRow(index) {
      this.$confirm('确认删除这一行数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tableData.splice(index, 1)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    exportData() {
      this.$message({
        type: 'success',
        message: '收入信息表导出功能待实现'
      })
    },
    saveData() {
      this.$message({
        type: 'success',
        message: '收入信息表保存成功'
      })
    },
    getData() {
      return this.tableData
    }
  }
}
</script>
