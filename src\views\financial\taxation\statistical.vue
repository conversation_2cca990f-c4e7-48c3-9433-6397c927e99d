<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="5" :xs="24">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="5" :xs="24">
                  <el-form-item label="销售名称" class="form-items">
                    <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="5" :xs="24">
                  <el-form-item label="代理名称" class="form-items">
                    <el-input v-model="formData.agentName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="5" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="4">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="exportList">下载列表</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="商户名称"
            width="250"
          />
          <el-table-column
            prop="saleName"
            label="销售名称"
            width="150"
          />
          <el-table-column
            prop="agentName"
            label="代理名称"
            width="150"
          />
          <el-table-column
            prop="levyName"
            width="250"
            label="代征主体"
          />
          <el-table-column
            prop="canInvoiceBalance"
            label="可开票金额"
          />
          <el-table-column
            prop="invoiceBalance"
            label="已申请开票金额"
          />
          <el-table-column
            prop="invoiceNum"
            label="开票数量"
          />
          <el-table-column
            prop="taxPayAmt"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="sendTips(scope.row.merId)">发送开票提醒</el-button>
            </template>
          </el-table-column>

        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import financial from '@/axios/default/financial'
import Pagination from '@/components/Pagination'
import publicApi from '@/axios/default/public'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'Statistical',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        merName: '',
        saleName:'',
        agentName:'',
        levyId: ''
      },
      listData: [],
      levyBodyIdArr: []
    }
  },

  mounted() {
    this.list()
    this.queryLevyBodyInfos()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      financial.queryInvoiceTotal({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'InvoiceInfo': {
          ...this.formData
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },
    sendTips(id) {
      financial.addMessageVO({
        merId: id
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    exportList() {
      financial.downInvoiceTotal({
        'InvoiceInfo': {
          ...this.formData
        }
      }, `发票统计${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
