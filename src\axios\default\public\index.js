// 公用api
import request from '@/axios/default/request'
import { API_system,API_account,API_message } from '../index'
import { API_system as API_Header } from '../index'


const api = {
  merAllList: `${API_system}/merchantInfo/allList`, // 商户列表（下拉框）
  queryLevyBodyInfos: `${API_system}/levyBodyInfo/queryLevyBodyInfos`, // 主体查询接口（下拉框）
  saleAllList: `${API_system}/saleInfo/allList`, // 销售列表（下拉框）
  agentAllList: `${API_system}/agentInfo/allList`, // 代理列表（下拉框）
  projectAllList: `${API_system}/projectLeader/allList`, // 项目负责人（下拉框）
  pic: `${API_system}/upload/pic`, // 上传图片
  upFiles: `${API_system}/upload/files`, // 上传商户资质/合同
  mccList: `${API_system}/mcc/list`, // 查询报税行业类型
  industry: `${API_system}/industry/list`, // 查询行业类型
  getChannelListByLevyId: `${API_Header}/merchantInfo/getChannelListByLevyId`, // 根据主体查询通道
  levyItems: `${API_Header}/levyItems/list`, // 查询征收品目列表
  queryInvoiceTypeInfo: `${API_Header}/invoiceType/queryInvoiceTypeInfo`, // 查询发票类目`
  // 查询充值
  // 企业: "queryType":"1",
  // 主体: "queryType":"2","merId":"1"
  // 渠道: "queryType":"3","levyId":"1"
  // 充值账号: "queryType":"4","merId":"1"，"channelId":"1"
  initAddRechInfo: `${API_system}/rechInfo/initAddRechInfo`, // 查询充值企业/主体/渠道/账号接口
  checkMerName: `${API_system}/merchantInfo/checkMerName`, // 重复企业名称查询
  getMerchantInfoByLevyId: `${API_system}/merchantInfo/getMerchantInfoByLevyId`, // 根据代征主体查询商户
  queryChannelInfoByLevyId: `${API_system}/payChannelInfo/queryChannelInfoByLevyId`, // 根据主体id查询相应的通道
  findChannelByMerId: `${API_system}/merchantInfo/findChannelByMerId`, // 根据主体id查询相应的通道
  findLevyBodyByDeptId: `${API_Header}/merchantInfo/findLevyBodyByDeptId`, // 查询某商户签约的代征主体
}

const publicApi = {
  merAllList: params => {
    return request.postJson(api.merAllList, params)
  },
  mccList: params => {
    return request.postJson(api.mccList, params)
  },
  industry: params => {
    return request.postJson(api.industry, params)
  },
  queryLevyBodyInfos: params => {
    return request.postJson(api.queryLevyBodyInfos, params)
  },
  saleAllList: params => {
    return request.postJson(api.saleAllList, params)
  },
  agentAllList: params => {
    return request.postJson(api.agentAllList, params)
  },
  projectAllList: params => {
    return request.postJson(api.projectAllList, params)
  },
  picUpload: params => {
    return request.postJson(api.pic, params)
  },
  upFiles: (params, config) => {
    return request.postJson(api.upFiles, params, config)
  },
  initAddRechInfo: params => {
    return request.postJson(api.initAddRechInfo, params)
  },
  levyItems: params => {
    return request.postJson(api.levyItems, params)
  },
  getChannelListByLevyId: params => {
    return request.postJson(api.getChannelListByLevyId, params)
  },
  queryInvoiceTypeInfo: params => {
    return request.postJson(api.queryInvoiceTypeInfo, params)
  },
  checkMerName: params => {
    return request.postJson(api.checkMerName, params)
  },
  getMerchantInfoByLevyId: params => {
    return request.postJson(api.getMerchantInfoByLevyId, params)
  },
  queryChannelInfoByLevyId: params => {
    return request.postJson(api.queryChannelInfoByLevyId, params)
  },
  findChannelByMerId: (params, filename, type) => {
    return request.postJson(api.findChannelByMerId, params)
  },
  findLevyBodyByDeptId: (params, filename, type) => {
    return request.postJson(api.findLevyBodyByDeptId, params)
  }
}

export default publicApi

