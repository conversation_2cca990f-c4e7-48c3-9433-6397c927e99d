<template>
  <div class="content-main">
    <!-- 代征主体金额数据表格 -->
    <div class="custom-card">
      <div class="table-header">
        <span class="table-title">代征主体金额统计</span>
      </div>
      <el-table
        :data="listData"
        border
        style="width: 100%"
        size="mini"
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        <el-table-column
          prop="levyName"
          label="代征主体"
          min-width="180"
          align="center"
        />
        <el-table-column
          prop="invoicedSum"
          label="本月开票金额"
          min-width="150"
          align="center"
        />
        <el-table-column
          prop="preInvoiceSum"
          label="其中预开票金额"
          min-width="150"
          align="center"
        />
        <el-table-column
          prop="settleSum"
          label="本月结算金额"
          min-width="150"
          align="center"
        />
        <el-table-column
          prop="canInvoiceBalanceSum"
          label="欠票"
          min-width="150"
          align="center"
        />
        <el-table-column
          prop="invoiceNoBack"
          label="待回款金额"
          min-width="150"
          align="center"
        />
        <el-table-column
          prop="rechargeSum"
          label="充值金额"
          min-width="150"
          align="center"
        />
      </el-table>

      <!-- 合计行由表格内置功能实现 -->
    </div>

    <!-- 近三月充值结算开票统计图表 -->
    <div class="custom-card">
      <div class="table-header">
        <span class="table-title">近三月充值结算开票统计</span>
      </div>
      <div ref="chartContainer" class="chart-container" />
    </div>
  </div>
</template>

<script>
import { formatNumber, newAlert } from '@/utils'
import resize from '@/components/Charts/mixins/resize'
import index from '@/axios/default/statistics/index'
// import publicApi from '@/axios/default/public'
import moment from 'moment'
import echarts from 'echarts'

export default {
  name: 'DataChart',
  mixins: [resize],
  data() {
    return {
      merTradeTotal: {},
      listData: [],
      dateMonth: moment().subtract(1, 'months').format('YYYY-MM'),
      formData: {
        levyId: ''
      },
      levyBodyIdArr: [],
      chart: null,
      chartData: []
    }
  },
  computed: {},
  mounted() {
    // this.queryLevyBodyInfos()
    this.queryMerTradePieChart('')
    this.height = this.$el.getBoundingClientRect().height
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    changeQuery() {
      this.queryMerTradePieChart(this.formData.levyId)
    },
    clickReturn() {
      this.$router.go(0)
    },
    // queryLevyBodyInfos() {
    //   publicApi.queryLevyBodyInfos().then(res => {
    //     if (res.data.code === '0000') {
    //       this.levyBodyIdArr = res.data.data
    //       if (this.levyBodyIdArr.length === 1) {
    //         this.formData.levyId = this.levyBodyIdArr[0].id
    //         this.queryMerTradePieChart(this.formData.levyId)
    //       }
    //     } else {
    //       newAlert(this.$tips, res.data.message)
    //     }
    //   })
    // },
    async queryMerTradePieChart(levyId) {
      try {
        const res = await index.queryInvoiceTotal({
          levyId
        })
        this.merTradeTotal = res.data.data

        // 格式化数据
        if (this.merTradeTotal) {
          this.listData = res.data.data.list

          // 格式化金额数据
          this.listData.forEach(item => {
            if (item.invoicedSum) item.invoicedSum = formatNumber(item.invoicedSum)
            if (item.preInvoiceSum) item.preInvoiceSum = formatNumber(item.preInvoiceSum)
            if (item.settleSum) item.settleSum = formatNumber(item.settleSum)
            if (item.canInvoiceBalanceSum) item.canInvoiceBalanceSum = formatNumber(item.canInvoiceBalanceSum)
            if (item.invoiceNoBack) item.invoiceNoBack = formatNumber(item.invoiceNoBack)
          })

          // 格式化合计数据
          if (this.merTradeTotal.invoicedAllSum) this.merTradeTotal.invoicedAllSum = formatNumber(this.merTradeTotal.invoicedAllSum)
          if (this.merTradeTotal.preInvoiceAllSum) this.merTradeTotal.preInvoiceAllSum = formatNumber(this.merTradeTotal.preInvoiceAllSum)
          if (this.merTradeTotal.settleAllSum) this.merTradeTotal.settleAllSum = formatNumber(this.merTradeTotal.settleAllSum)
          if (this.merTradeTotal.canInvoiceBalanceAllSum) this.merTradeTotal.canInvoiceBalanceAllSum = formatNumber(this.merTradeTotal.canInvoiceBalanceAllSum)
          if (this.merTradeTotal.invoiceAllNoBack) this.merTradeTotal.invoiceAllNoBack = formatNumber(this.merTradeTotal.invoiceAllNoBack)

          const data1 = res.data.data.data0
          this.initChart(data1)
        }
      } catch (error) {
        newAlert(this.$tips, '数据加载失败')
      }
    },

    // 初始化图表
    initChart(data) {
      if (!data || !this.$refs.chartContainer) return

      // 销毁之前的图表实例
      if (this.chart) {
        this.chart.dispose()
      }

      // 创建图表实例 - 添加响应式渲染
      this.chart = echarts.init(this.$refs.chartContainer, null, { renderer: 'canvas' })

      // 图表配置
      const option = {
        grid: {
          // 移动端增加边距，确保坐标轴标签显示完整
          left: 0,
          // top: 0,
          // right: '3%',
          bottom: '15%',
          // top: '10%',
          containLabel: true
        },
        legend: {
          // 移动端将图例放在底部
          orient: 'horizontal',
          left: 'right',
          top: 'top',
          itemWidth: 16,
          itemHeight: 10,
          textStyle: {
            fontSize: 12
          }
        },
        tooltip: {},
        dataset: {
          source: data
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            interval: 0,
            rotate: 0,
            fontSize: 12,
            formatter: function(value) {
              return value
            }
          }
        },
        yAxis: {
          axisLabel: {
            fontSize: 12
          }
        },
        color: ['#fdafbd', '#79d2fe', '#ffb73c'],
        series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
      }

      // 设置图表配置并渲染
      this.chart.setOption(option)
    },
    getSummaries(param) {
      const { columns } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (index === 1) {
          sums[index] = ''
          return
        }

        // 根据不同的列计算对应的合计值
        switch (column.property) {
          case 'invoicedSum':
            sums[index] = this.merTradeTotal.invoicedAllSum
            break
          case 'preInvoiceSum':
            sums[index] = this.merTradeTotal.preInvoiceAllSum
            break
          case 'settleSum':
            sums[index] = this.merTradeTotal.settleAllSum
            break
          case 'canInvoiceBalanceSum':
            sums[index] = this.merTradeTotal.canInvoiceBalanceAllSum
            break
          case 'invoiceNoBack':
            sums[index] = this.merTradeTotal.invoiceAllNoBack
            break
          case 'rechargeSum':
            sums[index] = this.merTradeTotal.rechargeAllSum
            break
          default:
            sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>

<style lang="scss" scoped>
.content-main {
  display: flex;
  flex-flow: column nowrap;
  overflow-x: auto;
  overflow-y: auto;
  height: calc(100%);
  padding-bottom: 15px;
}

.table-header {
  margin-bottom: 15px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #181F2D;
}

/* 合计行样式 */
.total-row {
  display: flex;
  background-color: #f5f7fa;
  padding: 8px 0;
  border: 1px solid #ebeef5;
  border-top: none;
  font-weight: bold;
}

.total-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 图表样式 */
.custom-card {
  margin-top: 20px;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  background-color: #fff;
}

.chart-container {
  width: 100%;
  height: 60vh;
}

</style>
