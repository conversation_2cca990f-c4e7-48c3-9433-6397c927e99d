@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Chinese Quote,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif;
}

::-webkit-scrollbar {
  width: .5rem;
  height: .5rem;
}

::-webkit-scrollbar-track {
  border-radius: 1px;
}

::-webkit-scrollbar-thumb {
  border-radius: 1px;
  background: rgba(0, 0, 0, .2);
}

label {
  font-weight: 600;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0 !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
  margin: 10px;
  background: #fff;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
  .search-item {
    margin-right: 10px;
    width: 190px;
  }
  .date-range-item {
    width: 240px;
  }
}

.table-operation {
  margin-right: 10px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.el-button:not(.is-circle,.is-round), .el-input__inner {
  border-radius: 3px !important;
}
.el-input {
  font-size: .85rem !important;
}
.el-button {
  font-weight: 500;
  margin-left: 0 !important;
  margin-right: 10px;
}
.el-submenu__title {
  font-size: 14px;
  font-weight: 600;
  color: #11142D !important;
  i {
    margin-right: 10px !important;
    margin-left: 10px;
  }
}
li.el-menu-item {
  font-weight: 600;
  color: #11142D !important;
}

.el-tooltip__popper {
  max-width: 500px !important;
  font-size: .88rem;
}
.no-perm {
  margin-top: -6px
}
.el-input__inner {
  height: 38px;
  line-height: 38px;
}
.el-menu-item.is-active {
  background: #E4E4E4 !important;
}
.el-button {
  padding: 10px 20px;
}
.el-table {
  th {
    padding: 7px 0;
  }
}
.el-table thead {
  color: #606266;
}
.el-table--group, .el-table--border {
  border: none;
}
.el-table th.is-leaf, .el-table td {
  border-top: 1px solid #dfe6ec;
}
.el-table__empty-block {
  border-left: 1px solid #dfe6ec;
  border-top: 1px solid #dfe6ec;
}
.el-table th.is-leaf, .el-table td {
  border-bottom: none;
}
.el-table th.is-leaf:first-child, .el-table td:first-child {
  border-left: 1px solid #dfe6ec;
}
.el-input-number {
  line-height: 36px;
}
.menu-wrapper.nest-menu .el-submenu__title {
  padding-left: 63px !important;
}
.vue-treeselect__placeholder, .vue-treeselect__single-value {
  line-height: 40px !important;
  height: 40px !important;
  padding-left: 10px !important;
}
.vue-treeselect__single-value {
  color: #606266 !important;
}

$xzb-btn-main-col:#E4E4E4;
$xzb-btn-sub-col:#EFF3F6;
//按钮背景颜色
.xzb-btn{
  width: 90px;
  height: 27px;
  color: #fff;
  line-height: 27px;
  padding: 0!important;
  border: none;
}
.xzb-main-btn-style{
  background: $xzb-btn-main-col;
  color: #fff;
}

.xzb-sub-btn-style{
  background: $xzb-btn-sub-col;
  color: #7D7F80;
}
//table 文字
.tb-active-red{ color: $xzb-btn-main-col}
.tb-btn-normal{ color: #777}


.table-top{
  margin-bottom: 10px;
  text-align: right;
}

.mar-right{
  margin-right: 10px;
}

/deep/ .el-table__header,
/deep/ .el-table__body,
/deep/ .el-table__footer {
  width: 100% !important;
  table-layout: fixed !important;
  .cell.el-tooltip {
    width: auto !important;
  }
}
