<template>
  <div class="imgbox">
    <img :src="url" alt="">
  </div>
</template>
<script>
export default {
  name: 'ViewInfo',
  data() {
    return {
      url: ''
    }
  },
  mounted() {
    this.url = this.$route.query.url
  }
}
</script>
<style scoped lang="scss">
  .imgbox{
    background: #999;
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    img{
      max-height: 100%;
      width: auto;
    }
  }
</style>
