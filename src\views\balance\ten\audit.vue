<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">

        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="10" :xs="24">
              <el-form-item label="订单时间" class="form-items"> <el-date-picker
                v-model="formData.date"
                class="auto-width"
                type="daterange"
                align="right"
                unlink-panels
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :picker-options="pickerOptions"
              />
              </el-form-item>
            </el-col>
            <el-col :lg="7" :xs="24">
              <el-form-item label="企业名称" class="form-items">
                <el-input v-model="formData.batchNo" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="7" :xs="24">
              <el-form-item label="打款名称" class="form-items">
                <el-input v-model="formData.batchNo" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="10" :xs="24">
              <el-form-item label="收款名称" class="form-items">
                <el-input v-model="formData.batchNo" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="14" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 40px">
            <el-col :span="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-download" plain type="primary">下载文件</el-button>
                <el-button icon="el-icon-download" plain type="primary" @click="dialogczlr=true">批量审核</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>


        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            type="selection"
            align="center"
          />
          <el-table-column
            prop="fileName"
            label="企业名称"
          />
          <el-table-column
            prop="fileSize"
            label="姓名"
          />
          <el-table-column
            prop="fileSize"
            label="身份证号"
          />
          <el-table-column
            prop="fileSize"
            label="审核状态"
          />
          <el-table-column
            prop="fileSize"
            label="交易状态"
          />
          <el-table-column
            prop="fileSize"
            label="结算金额"
          />
          <el-table-column
            prop="fileSize"
            label="商户批次号"
          />
          <el-table-column
            prop="fileSize"
            label="商户订单号"
          />
          <el-table-column
            prop="fileSize"
            label="通道名称"
          />

          <el-table-column
            prop="address"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text">搜索</el-button>
              <el-button type="text">下载文件</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!--        充值初审-->
    <el-dialog title="充值初审" :visible.sync="dialogaudit" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form :model="dialogtrialData" label-width="110px" size="mini" class="form-style">
          <el-form-item label="商户批次号">
            gsvsg68766vhs
          </el-form-item>
          <el-form-item label="结算金额">
            87367元
          </el-form-item>
          <el-form-item label="服务费">
            服务费
          </el-form-item>
          <el-form-item label="初审备注">
            服务费
          </el-form-item>
          <el-form-item label="* 备注">
            <el-input v-model="dialogtrialData.note" type="textarea" :rows="2" autocomplete="off" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary">通过</el-button>
              <el-button @click="dialogaudit=false">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import { newAlert } from '@/utils'
export default {
  name: 'Audit',
  data() {
    return {
      formData: {
        value1: '',
        date: ''
      },

      formLabelWidth: '180px',
      listData: [],
      operateState: '',
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogaudit: true,
      dialogtrialData: {}
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>

