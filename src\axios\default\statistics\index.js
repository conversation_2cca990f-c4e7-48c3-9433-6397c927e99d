import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  queryMerTradePieChart: `${API_Header}/homeData/queryMerTradePieChart`,
  queryMerTradeTotal: `${API_Header}/homeData/queryMerTradeTotal`,
  queryInvoiceTotal: `${API_Header}/homeData/queryInvoiceTotal`
}

const statistics = {

  queryMerTradePieChart: params => {
    return request.postJson(api.queryMerTradePieChart, params)
  },
  queryInvoiceTotal: params => {
    return request.postJson(api.queryInvoiceTotal, params)
  },
  queryMerTradeTotal: params => {
    return request.postJson(api.queryMerTradeTotal, params)
  }
}

export default statistics

