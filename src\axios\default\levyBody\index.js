// 代征主体
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  queryLevyBodyInfos: `${API_Header}/levyBodyInfo/queryLevyBodyInfos`, // 主体下拉框
  queryLevyBodyInfosByPage: `${API_Header}/levyBodyInfo/queryLevyBodyInfosByPage`, // 主体列表
  insertLevyBodyInfo: `${API_Header}/levyBodyInfo/insertLevyBodyInfo`, // 添加代征主体接口
  updateLevyBodyInfo: `${API_Header}/levyBodyInfo/updateLevyBodyInfo`, // 修改代征主体
  queryRegions: `${API_Header}/levyBodyInfo/queryRegions` // 查询省
}

const levyBody = {
  insertLevyBodyInfo: params => {
    return request.postJson(api.insertLevyBodyInfo, params)
  },
  queryLevyBodyInfos: params => {
    return request.postJson(api.queryLevyBodyInfos, params)
  },
  queryLevyBodyInfosByPage: params => {
    return request.postJson(api.queryLevyBodyInfosByPage, params)
  },
  updateLevyBodyInfo: params => {
    return request.postJson(api.updateLevyBodyInfo, params)
  },
  queryRegions: params => {
    return request.postJson(api.queryRegions, params)
  }
}

export default levyBody

