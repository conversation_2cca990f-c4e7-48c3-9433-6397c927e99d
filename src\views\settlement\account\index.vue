<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="6" :xs="24">
              <el-form-item label="代征主体" class="form-items">
                <el-select v-model="formData.levyBodyId" placeholder="请选择" class="auto-width">
                  <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                  <el-option v-for="item in levyBodyIdArr" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="6" :xs="24">
              <el-form-item label="企业名称" class="form-items">
                <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :lg="6" :xs="24">
              <el-form-item label="通道名称" class="form-items">
                <el-input v-model="formData.channelName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="6" :xs="24">
              <el-form-item label="账户状态" class="form-items">
                <el-select v-model="formData.accountStatus" placeholder="请选择" class="auto-width">
                  <el-option v-if="accountStatusArr.length>1" label="请选择" value="" />
                  <el-option v-for="item in accountStatusArr" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="24" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                <el-button icon="el-icon-search" type="primary" @click="findAccountInfoDownload">下载</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-table :data="listData" border size="mini" class="list-table">
          <el-table-column prop="createTime" label="创建时间" width="150" show-overflow-tooltip />
          <el-table-column prop="levyBodyName" label="代征主体" width="200" show-overflow-tooltip />
          <el-table-column prop="accountName" label="企业名称" width="200" show-overflow-tooltip />
          <el-table-column prop="channelName" label="通道名称" width="200" show-overflow-tooltip />
          <el-table-column prop="accountName" label="账户名称" width="200" show-overflow-tooltip />
          <el-table-column prop="accountNo" label="银行子账户号" width="200" show-overflow-tooltip />
          <el-table-column prop="balance" label="账户余额">
            <template slot-scope="scope">
              <countTo class="countTo totalVisit" :start-val="0" :end-val="intMomey(scope.row.balance)" :decimals="2" :duration="10" />元
            </template>
          </el-table-column>

          <el-table-column prop="frozenBalance" label="冻结金额">
            <template slot-scope="scope">
              <countTo class="countTo totalVisit" :start-val="0" :end-val="intMomey(scope.row.frozenBalance)" :decimals="2" :duration="10" />元
            </template>
          </el-table-column>

          <el-table-column prop="accountStatus" label="状态">
            <template slot-scope="scope">
              {{ accountStatus[scope.row.accountStatus] }}
            </template>
          </el-table-column>
          <el-table-column
            prop=""
            label="操作"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button v-if="((scope.row.accountStatus == 1)||(scope.row.accountStatus == 3))&&(scope.row.channelName.includes('平安银行'))" type="text" @click="updateAccount(scope.row.id,'5')">止收</el-button>
              <el-button v-if="((scope.row.accountStatus == 1)||(scope.row.accountStatus == 4))&&(scope.row.channelName.includes('平安银行'))" type="text" @click="updateAccount(scope.row.id,'4')">止付</el-button>
              <el-button v-if="(scope.row.accountStatus == 1)&&(scope.row.channelName.includes('平安银行'))" type="text" @click="updateAccount(scope.row.id,'2')">止收止付</el-button>
              <el-button v-if="((scope.row.accountStatus == 2)||(scope.row.accountStatus == 4))&&(scope.row.channelName.includes('平安银行'))" type="text" @click="updateAccount(scope.row.id,'8')">取消止收</el-button>
              <el-button v-if="((scope.row.accountStatus == 2)||(scope.row.accountStatus == 3))&&(scope.row.channelName.includes('平安银行'))" type="text" @click="updateAccount(scope.row.id,'7')">取消止付</el-button>
              <el-button v-if="(scope.row.accountStatus == 2)&&(scope.row.channelName.includes('平安银行'))" type="text" @click="updateAccount(scope.row.id,'3')">取消止收止付</el-button>
            </template>
          
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import payChannelInfo from '@/axios/default/payChannelInfo'
import Pagination from '@/components/Pagination'
import publicApi from '@/axios/default/public'
import countTo from 'vue-count-to'
import { intMomey, parseTime, newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination,
    countTo
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      levyBodyIdArr: [],
      formData: {
        levyBodyId: '',
        accountName: '',
        channelName: '',
        accountStatus:''
      },
      accountStatusArr:[
        {id:'1' ,name:'正常'},
        {id:'2',name:'止收止付'},
        {id:'3',name:'止付'},
        {id:'4',name:'止收'},
        {id:'5',name:'注销'}],
      accountStatus: { 1: '正常', 2: '冻结', 3: '冻结出账', 4: '冻结入账', 5: '已注销' },
      listData: []
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryLevyBodyInfos()
    },
    findAccountInfoList() {
      payChannelInfo.findAccountInfoList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        ...this.formData
      }).then(res => {
        this.listData = res.data.data.rows
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
        this.total = res.data.data.total
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.findAccountInfoList()
    },
    intMomey(num) {
      return Number(num)
    },
    updateAccount(id, accountStatus){
      this.$confirm('是否确定修改账户状态？', '修改账户状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
    payChannelInfo.updateAccountInfo({
        id: id,
        accountStatus
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.findAccountInfoList()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      }) }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
   
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyBodyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
        this.findAccountInfoList()
      })
    },
    findAccountInfoDownload() {
      if (this.levyBodyIdArr.length === 1) {
        this.formData.levyBodyId = this.levyBodyIdArr[0].id
      }
      payChannelInfo.findAccountInfoDownload({
        ...this.formData
      }, `子账户列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card {
  margin-bottom: 20px;
}

.auto-width {
  width: 100%;
}
.list-table {
  margin: 0 0 20px 0;
  .el-button {
    padding: 0;
  }
}
</style>
