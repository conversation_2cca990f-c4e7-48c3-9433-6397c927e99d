<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <!--                    <el-input v-model="formData.levyBodyId" placeholder="请输入内容" clearable />-->
                    <el-select v-model="formData.levyBodyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="通道名称" class="form-items">
                    <el-input v-model="formData.channelName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="公户名称" class="form-items">
                    <el-input v-model="formData.bankAccountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="4" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--              <el-row>-->
              <!--                <el-col :span="24">-->
              <!--                  <el-form-item class="form-items" style="text-align:right">-->
              <!--                    <el-button icon="el-icon-plus" type="primary" @click="add">新增</el-button>-->
              <!--                  </el-form-item>-->
              <!--                </el-col>-->
              <!--              </el-row>-->
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="levyBodyName"
            label="代征主体"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            show-overflow-tooltip
            prop="channelNo"
            width="200"
            label="通道编号"
          />
          <el-table-column
            prop="channelName"
            show-overflow-tooltip
            width="200"
            label="通道名称"
          />

          <el-table-column
            prop="bankAccountName"
            show-overflow-tooltip
            label="公户名称"
            width="200"
          />

          <el-table-column
            prop="bankAccountNo"
            show-overflow-tooltip
            width="200"
            label="账号"
          />
          <el-table-column
            prop="bankName"
            show-overflow-tooltip
            label="开户银行"
            width="200"
          />
          <el-table-column
            prop="bankBranchNo"
            show-overflow-tooltip
            label="行号"
            width="200"
          />

          <el-table-column
            prop="calcType"
            label="收费类型"
          >
            <template slot-scope="scope">
              {{ calcType[scope.row.calcType] }}
            </template>
          </el-table-column>

          <el-table-column
            prop="fee"
            label="费率"
          >
            <template slot-scope="scope">
              {{ scope.row.fee ? scope.row.fee+'%' : '--' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="singleFee"
            label="手续费"
          />

          <el-table-column
            prop=""
            label="操作"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="edit(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog :title="dialogTitile" :visible.sync="dialogBoxShow" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogBoxData" :model="dialogBoxData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="代征主体名称" prop="levyBodyId">
            <el-select v-model="dialogBoxData.levyBodyId" placeholder="请选择" class="auto-width">
              <el-option
                v-for="item in levyBodyIdArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="公户名称" prop="bankAccountName">
            <el-input v-model="dialogBoxData.bankAccountName" autocomplete="off" />
          </el-form-item>
          <el-form-item label="账      号" prop="bankAccountNo">
            <el-input v-model="dialogBoxData.bankAccountNo" autocomplete="off" />
          </el-form-item>
          <el-form-item label="开户银行" prop="bankName">
            <el-input v-model="dialogBoxData.bankName" autocomplete="off" />
          </el-form-item>
          <el-form-item label="行       号" prop="bankBranchNo">
            <el-input v-model="dialogBoxData.bankBranchNo" autocomplete="off" />
          </el-form-item>
          <el-form-item label="通道名称" prop="channelName">
            <el-input v-model="dialogBoxData.channelName" autocomplete="off" />
          </el-form-item>
          <el-form-item label="通道编号" prop="channelNo">
            <el-input v-model="dialogBoxData.channelNo" autocomplete="off" />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="收费类型" prop="calcType">
                <el-radio v-model="dialogBoxData.calcType" :label="1" @change="changeRadio">按费率计算</el-radio>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="" prop="fee" label-width="0">
                <el-input v-model="dialogBoxData.fee" :disabled="disabled1">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="收费类型" prop="calcType">
                <el-radio v-model="dialogBoxData.calcType" :label="2" @change="changeRadio">按笔数计算</el-radio>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="" prop="singleFee" label-width="0">
                <el-input v-model="dialogBoxData.singleFee" :disabled="disabled2">
                  <template slot="append">元/笔</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!--          <el-form-item label="收费类型" prop="calcType">-->
          <!--            <el-row>-->
          <!--              <el-col :span="24" style="margin-bottom: 10px">-->
          <!--               -->
          <!--                -->
          <!--              </el-col>-->
          <!--              <el-col :span="24">-->
          <!--              -->
          <!--              </el-col>-->
          <!--            </el-row>-->
          <!--          </el-form-item>-->

          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" @click="submit('dialogBoxData')">保存</el-button>
              <el-button @click="dialogBoxShow=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import payChannelInfo from '@/axios/default/payChannelInfo'
import levyBody from '@/axios/default/levyBody'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      buttonLoading: false,
      formData: {
        levyBodyId: '',
        bankAccountName: '',
        channelName: ''
      },
      listData: [],
      dialogBoxShow: false,
      dialogBoxData: this.initData(),
      dialogType: '',
      dialogTitile: '',
      levyBodyIdArr: [],
      disabled1: false,
      disabled2: false,
      calcType: { 1: '费率', 2: '按笔', 3: '费率+单笔' },
      rules: {
        levyBodyId: [{ required: true, message: '请选择代征主体名称', trigger: ['change'] }],
        bankAccountName: [{ required: true, message: '请填写公户名称', trigger: 'blur' }],
        bankAccountNo: [{ required: true, message: '请填写账号', trigger: 'blur' }],
        bankName: [{ required: true, message: '请填写开户银行', trigger: 'blur' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }],
        bankBranchNo: [{ required: true, message: '请填写行号', trigger: 'blur' }],
        channelName: [{ required: true, message: '请填写通道名称', trigger: 'blur' }],
        channelNo: [{ required: true, message: '请填写通道编号', trigger: 'blur' }],
        calcType: [
          { validator: this.checkMore, required: true, trigger: ['blur'] }
        ],
        singleFee: [{ required: true, message: '请输入正确的数字', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        fee: [{ required: true, message: '请输入正确的数字', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }]

      }
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  methods: {
    initData() {
      return {
        id: '',
        levyBodyId: '',
        bankAccountName: '',
        bankAccountNo: '',
        bankName: '',
        bankBranchNo: '',
        channelName: '',
        channelNo: '',
        calcType: '',
        fee: '',
        singleFee: ''
      }
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.buttonLoading = true
          if (this.dialogType == 'add') {
            payChannelInfo.addPayChannelInfo({
              ...this.dialogBoxData
            }).then(res => {
              if (res.data.code === '0000') {
                this.callback()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          } else {
            payChannelInfo.updatePayChannelInfo({
              ...this.dialogBoxData
            }).then(res => {
              if (res.data.code === '0000') {
                this.callback()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    callback() {
      this.buttonLoading = false
      this.dialogBoxShow = false
      this.findPayChannelInfoList()
    },
    add() {
      this.dialogTitile = '新增主体通道'
      this.dialogType = 'add'
      this.dialogBoxData = this.initData()
      this.dialogBoxShow = true
    },
    edit(data) {
      this.dialogTitile = '编辑主体通道'
      // this.dialogBoxData = data
      Object.assign(this.dialogBoxData, data)
      this.dialogType = 'edit'
      this.dialogBoxShow = true
    },
    onSearch() {
      this.pageNum = 1
      this.findPayChannelInfoList()
    },
    findPayChannelInfoList() {
      payChannelInfo.findPayChannelInfoList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        ...this.formData
      }).then(res => {
        console.log(res.data.data)
        this.listData = res.data.data.rows
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
        this.total = res.data.data.total
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.findPayChannelInfoList()
    },
    queryLevyBodyInfos() {
      levyBody.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyBodyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
        this.findPayChannelInfoList()
      })
    },
    changeRadio(val) {
      if (val == '1') {
        this.disabled1 = false
        this.disabled2 = true
      } else if (val == '2') {
        this.disabled1 = true
        this.disabled2 = false
      }
    },
    checkMore(rule, value, callback) {
      if (value == '') {
        return callback(new Error('请选择收费类型'))
      } else {
        if (value == '1') {
          if (this.dialogBoxData.fee == '') {
            return callback(new Error('请输入正确的费率'))
          } else {
            callback()
          }
        } else if (value == '2') {
          if (this.dialogBoxData.singleFee == '') {
            return callback(new Error('请输入正确的单笔收费'))
          } else {
            callback()
          }
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .auto-width{
    width: 100%;
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 60vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }
</style>
