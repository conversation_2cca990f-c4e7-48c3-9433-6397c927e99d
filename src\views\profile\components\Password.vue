<template>
  <el-form ref="form" :model="p" :rules="rules" label-position="right" label-width="80px" class="form" size="mini">
    <el-form-item :label="$t('table.user.oldPassword')" prop="oldPassword">
      <el-input v-model="p.oldPassword" show-password type="password" />
    </el-form-item>
    <el-form-item :label="$t('table.user.newPassword')" prop="newPassword">
      <el-input v-model="p.newPassword" show-password type="password" />
    </el-form-item>
    <el-form-item :label="$t('table.user.confirmPassword')" prop="confirmPassword">
      <el-input v-model="p.confirmPassword" show-password type="password" />
    </el-form-item>
    <el-form-item>
      <el-button class="xzb-btn xzb-main-btn-style" :loading="buttonLoading" @click="submit">{{ $t('common.edit') }}</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  data() {
    return {
      buttonLoading: false,
      p: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      rules: {
        oldPassword: [
          { required: true, message: this.$t('rules.require'), trigger: 'blur' },
          { validator: (rule, value, callback) => {
            this.$get('merchants/user/password/check', {
              password: value
            }).then((r) => {
              if (r.data) {
                callback()
              } else {
                callback(this.$t('tips.oldPasswordIncorrect'))
              }
            }).catch(() => {
              callback()
            })
          }, trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: this.$t('rules.require'), trigger: 'blur' },
          { pattern: /^(?![A-Z]+$)(?![a-z]+$)(?!\d+$)(?![\W_]+$)\S{6,16}$/,  message: '密码至少包含数字、大写字母、小写字母至少两种，6-16位长度', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: this.$t('rules.require'), trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (this.p.newPassword !== value) {
              callback(this.$t('tips.notEqual'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.buttonLoading = true
          this.$put('merchants/user/password', {
            password: this.p.newPassword
          }).then(() => {
            this.buttonLoading = false
            this.$message({
              message: this.$t('tips.updateSuccess'),
              type: 'success'
            })
            this.$refs.form.clearValidate()
            this.$refs.form.resetFields()
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
