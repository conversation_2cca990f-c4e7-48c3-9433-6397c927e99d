<template>
  <div class="data-export-container">
    <!-- 数据导出区域 -->
    <div class="export-section">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>数据导出</span>
        </div>
        <div class="export-form">
          <el-form ref="exportForm" :model="exportForm" :rules="exportRules" label-width="120px">
            <el-form-item label="选择日期:" prop="date">
              <el-date-picker
                v-model="exportForm.date"
                type="date"
                placeholder="请选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 300px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="exportLoading" @click="exportData">
                <i class="el-icon-download" />
                导出Excel
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 项目绑定区域 -->
    <div class="project-binding-section">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>项目绑定</span>
        </div>
        <div class="binding-form">
          <el-alert
            title="提示：需要先绑定项目，才能进行表格数据上传操作"
            type="info"
            :closable="false"
            show-icon
            style="margin-bottom: 20px"
          />
          <el-form ref="bindingForm" :model="bindingForm" label-width="120px" :rules="bindingRules">
            <el-form-item label="企业名称" prop="merName">
              <el-select
                v-model="bindingForm.merName"
                class="auto-width"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="bindingMccList"
                :loading="bindingLoading"
                @change="bindingGetlevyId"
              >
                <el-option
                  v-for="(item, index) in bindingMerNameArr"
                  :key="index"
                  :label="item.merName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="代征主体" prop="levyId">
              <el-select
                v-model="bindingForm.levyId"
                placeholder="请选择代征主体"
                class="auto-width"
                @change="syncLevyIdToUpload"
              >
                <el-option v-for="item in bindingLevyIdArr" :key="item.id" :label="item.NAME" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="success" :loading="bindingLoading" @click="bindProject">
                <i class="el-icon-link" />
                绑定项目
              </el-button>
              <span v-if="isProjectBound" style="margin-left: 10px; color: #67c23a">
                <i class="el-icon-check" /> 项目已绑定
              </span>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 数据上传区域 -->
    <div class="upload-section">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>上传表格数据</span>
        </div>
        <div class="upload-form">
          <el-form ref="uploadForm" :model="uploadForm" label-width="120px" :rules="uploadRules">
            <el-form-item label="企业名称" prop="merName">
              <el-select
                v-model="uploadForm.merName"
                class="auto-width"
                filterable
                placeholder="请选择企业名称"
                :loading="loading"
                disabled
                @change="getlevyId"
              >
                <el-option v-for="(item, index) in merNameArr" :key="index" :label="item.merName" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="代征主体" prop="levyId">
              <el-select v-model="uploadForm.levyId" placeholder="请选择代征主体" class="auto-width" disabled>

                <el-option v-for="item in levyIdArr" :key="item.id" :label="item.NAME" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="结算通道" prop="channelId">
              <el-select v-model="uploadForm.channelId" placeholder="请选择结算通道" class="auto-width" @change="getMoney">
                <el-option
                  v-for="(item, index) in channelIdArr"
                  :key="index"
                  :label="item.channel_name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="选择文件:">
              <input
                ref="fileInput"
                type="file"
                accept=".xlsx"
                style="display: none;"
                @change="handleFileSelect"
              >
              <el-button size="small" type="primary" @click="$refs.fileInput.click()">
                <i class="el-icon-folder-opened" />
                选择文件
              </el-button>
              <span v-if="selectedFile" style="margin-left: 10px; color: #606266;">
                {{ selectedFile.name }}
              </span>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                只能选择xlsx文件，且不超过10MB
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="success" :loading="uploadLoading" @click="submitUpload">
                <i class="el-icon-upload" />
                提交上传
              </el-button>
              <el-button @click="resetUploadForm"> 重置表单 </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import systemApi from '@/axios/default/system'
import rechInfo from '@/axios/default/rechInfo'
export default {
  name: 'DataExport',
  data() {
    return {
      // 导出表单数据
      exportForm: {
        date: ''
      },
      exportLoading: false,

      // 项目绑定状态
      isProjectBound: false,
      bindingLoading: false,

      // 项目绑定表单数据
      bindingForm: {
        merName: '', // 企业名称(公司ID)
        levyId: '' // 代征主体
      },
      bindingMerNameArr: [], // 项目绑定企业名称数组
      bindingLevyIdArr: [], // 项目绑定代征主体数组

      // 上传表单数据
      uploadForm: {
        merName: '', // 企业名称(公司ID)
        levyId: '', // 代征主体
        channelId: '', // 结算通道
        file: null
      },
      uploadLoading: false,
      selectedFile: null,
      loading: false,
      merNameArr: [], // 企业名称数组
      levyIdArr: [], // 代征主体数组
      channelIdArr: [], // 结算通道数组

      // 表单验证规则
      exportRules: {
        date: [
          { required: true, message: '请选择导出日期', trigger: 'change' }
        ]
      },
      bindingRules: {
        merName: [
          { required: true, message: '请选择企业名称', trigger: 'change' }
        ],
        levyId: [
          { required: true, message: '请选择代征主体', trigger: 'change' }
        ]
      },
      uploadRules: {
        merName: [
          { required: true, message: '请选择企业名称', trigger: 'change' }
        ],
        levyId: [
          { required: true, message: '请选择代征主体', trigger: 'change' }
        ],
        channelId: [
          { required: true, message: '请选择结算通道', trigger: 'change' }
        ],
        file: [
          { required: true, message: '请选择上传文件', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    // 不再需要防抖搜索方法，直接在bindingMccList中处理
  },
  mounted() {
    // 组件挂载后的初始化操作
    // 初始化企业名称列表（同时用于项目绑定和上传表单）
    this.initAddRechInfo({ queryType: '1', merName: '' }, 'bindingMerNameArr')

    // 检查本地存储的项目绑定状态
    this.checkProjectBindingStatus()
  },
  methods: {
    // 导出数据
    async exportData() {
      // 验证表单
      const valid = await this.$refs.exportForm.validate().catch(() => false)
      if (!valid) {
        return
      }

      this.exportLoading = true
      let url = null
      let link = null

      try {
        // 下载参数
        const downloadParams = {
          date: this.exportForm.date
        }

        // 调用数据导出API进行文件下载
        const response = await systemApi.downTradeOrderDetailsForTq(
          downloadParams
        )

        // 处理文件下载
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        url = window.URL.createObjectURL(blob)
        link = document.createElement('a')
        link.href = url
        link.download = `交易订单明细_${this.exportForm.date}.xlsx`
        document.body.appendChild(link)
        link.click()

        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败，请重试')
      } finally {
        // 确保资源清理
        if (link && link.parentNode) {
          document.body.removeChild(link)
        }
        if (url) {
          window.URL.revokeObjectURL(url)
        }
        this.exportLoading = false
      }
    },

    // 绑定项目
    async bindProject() {
      // 验证表单
      const valid = await this.$refs.bindingForm.validate().catch(() => false)
      if (!valid) {
        return
      }

      this.bindingLoading = true
      try {
        const params = {
          merchantId: this.bindingForm.merName,
          levyId: this.bindingForm.levyId
        }
        const result = await systemApi.findProjectByMerLev(params)
        console.log('项目绑定成功:', result)
        this.isProjectBound = true
        // 将绑定状态保存到本地存储
        localStorage.setItem(
          'projectBound',
          JSON.stringify({
            merchantId: this.bindingForm.merName,
            levyId: this.bindingForm.levyId,
            timestamp: Date.now()
          })
        )
        this.$message.success('项目绑定成功！')
      } catch (error) {
        this.$message.error('项目绑定失败，请重试')
      } finally {
        this.bindingLoading = false
      }
    },

    // 项目绑定 - 企业名称搜索
    bindingMccList(query) {
      if (query !== '') {
        this.bindingLoading = true
        this.initAddRechInfo({ queryType: '1', merName: query }, 'bindingMerNameArr')
      } else {
        this.bindingMerNameArr = []
      }
    },

    // 企业名称搜索
    mccList(query) {
      this.debouncedUploadSearch(query)
    },

    // 项目绑定 - 获取代征主体
    bindingGetlevyId() {
      this.bindingForm.levyId = ''
      this.$nextTick(function() {
        this.$refs.bindingForm.clearValidate()
      })
      this.initAddRechInfo(
        { queryType: '2', merId: this.bindingForm.merName },
        'bindingLevyIdArr'
      )

      // 同步到上传表单
      this.uploadForm.merName = this.bindingForm.merName
      this.uploadForm.levyId = ''
      this.uploadForm.channelId = ''
      // 同步企业名称数组
      this.merNameArr = [...this.bindingMerNameArr]
      // 获取对应的代征主体和结算通道
      this.getlevyId()
    },

    // 同步代征主体到上传表单
    syncLevyIdToUpload() {
      this.uploadForm.levyId = this.bindingForm.levyId
      this.uploadForm.channelId = ''
      // 同步代征主体数组
      this.levyIdArr = [...this.bindingLevyIdArr]
      // 获取对应的结算通道
      this.getChannel()
    },

    // 文件选择处理
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) {
        this.selectedFile = null
        this.uploadForm.file = null
        return
      }

      // 基础验证
      const isXlsx = file.name.toLowerCase().endsWith('.xlsx')
      const isLt10M = file.size / 1024 / 1024 < 10
      const isNotEmpty = file.size > 0

      if (!isNotEmpty) {
        this.$message.error('不能选择空文件!')
        this.resetFileInput()
        return
      }

      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        this.resetFileInput()
        return
      }

      if (!isXlsx) {
        this.$message.error('只能选择xlsx文件!')
        this.resetFileInput()
        return
      }

      this.selectedFile = file
      this.uploadForm.file = file
      this.$refs.uploadForm.validateField('file')
    },

    // 重置文件输入
    resetFileInput() {
      this.$refs.fileInput.value = ''
      this.selectedFile = null
      this.uploadForm.file = null
    },

    // 提交上传
    async submitUpload() {
      // 检查项目绑定状态
      if (!this.isProjectBound) {
        this.$message.warning('请先绑定项目后再上传表格数据')
        return
      }

      // 验证表单
      const valid = await this.$refs.uploadForm.validate().catch(() => false)
      if (!valid) {
        return
      }

      if (!this.selectedFile) {
        this.$message.warning('请选择要上传的文件')
        return
      }

      this.uploadLoading = true
      try {
        // 获取选中的企业名称
        const selectedMer = this.merNameArr.find(
          (item) => item.id === this.uploadForm.merName
        )
        const merName = selectedMer ? selectedMer.merName : ''

        // 验证文件是否仍然有效
        const file = this.selectedFile
        if (!file || file.size === 0) {
          this.$message.error('文件无效，请重新选择')
          return
        }

        // 创建FormData
        const formData = new FormData()
        formData.append('file', file)
        formData.append('merId', this.uploadForm.merName) // 公司ID
        formData.append('merName', merName) // 企业名称
        formData.append('levyId', this.uploadForm.levyId) // 代征主体
        formData.append('channelId', this.uploadForm.channelId) // 结算通道

        // 调用批量导入外部数据接口
        await systemApi.batchImportOfExternalData(formData)

        // 检查响应结果
        this.$message.success('上传成功！')
        this.resetUploadForm()
      } catch (error) {
        this.$message.error('上传失败，请重试')
      } finally {
        this.uploadLoading = false
      }
    },

    // 初始化充值信息
    initAddRechInfo(data, item) {
      rechInfo
        .initAddRechInfo(data)
        .then((res) => {
          if (res.data.code === '0000') {
            this.loading = false
            this.bindingLoading = false
            this[item] = res.data.data

            // 如果是项目绑定的数据，也初始化上传表单的企业数据
            if (item === 'bindingMerNameArr') {
              this.merNameArr = [...res.data.data]
            } else if (item === 'bindingLevyIdArr') {
              this.levyIdArr = [...this[item]]
            }

            if (this[item].length === 1) {
              if (item === 'levyIdArr') {
                this.uploadForm.levyId = this[item][0].id
                this.getChannel()
              } else if (item === 'channelIdArr') {
                this.uploadForm.channelId = this[item][0].id
                this.getMoney()
              } else if (item === 'bindingLevyIdArr') {
                this.bindingForm.levyId = this[item][0].id
                // 同步到上传表单
                this.uploadForm.levyId = this[item][0].id
                this.levyIdArr = [...this[item]]
                this.getChannel()
              }
            }
          } else {
            const errorMsg = res.data.message || '获取数据失败'
            this.$message.error(errorMsg)
            console.error('API返回错误:', res.data)
          }
        })
        .catch(() => {
          this.loading = false
          this.bindingLoading = false
          this.$message.error('获取数据失败，请重试')
        })
    },

    // 获取代征主体
    getlevyId() {
      this.uploadForm.levyId = ''
      this.uploadForm.channelId = ''
      this.$nextTick(function() {
        this.$refs.uploadForm.clearValidate()
      })
      this.initAddRechInfo(
        { queryType: '2', merId: this.uploadForm.merName },
        'levyIdArr'
      )
    },

    // 获取结算通道
    getChannel() {
      this.uploadForm.channelId = ''
      this.$nextTick(function() {
        this.$refs.uploadForm.clearValidate()
      })
      this.initAddRechInfo(
        {
          queryType: '3',
          merId: this.uploadForm.merName,
          levyId: this.uploadForm.levyId
        },
        'channelIdArr'
      )
    },

    // 获取金额信息
    getMoney() {
      this.$nextTick(function() {
        this.$refs.uploadForm.clearValidate()
      })
      // 注意：数据导出页面暂不需要获取金额信息功能
      // this.initAddRechInfo({ queryType: '4', merId: this.uploadForm.merName, channelId: this.uploadForm.channelId }, 'rechBankNo')
    },

    // 检查项目绑定状态
    checkProjectBindingStatus() {
      const savedBinding = localStorage.getItem('projectBound')
      if (savedBinding) {
        try {
          const bindingData = JSON.parse(savedBinding)
          // 检查绑定是否在24小时内
          const isValid =
            Date.now() - bindingData.timestamp < 24 * 60 * 60 * 1000
          if (isValid) {
            this.isProjectBound = true
            this.bindingForm.merName = bindingData.merchantId
            this.bindingForm.levyId = bindingData.levyId
            // 同步到上传表单
            this.uploadForm.merName = bindingData.merchantId
            this.uploadForm.levyId = bindingData.levyId
          } else {
            // 清除过期的绑定状态
            localStorage.removeItem('projectBound')
          }
        } catch (error) {
          console.error('解析项目绑定状态失败:', error)
          localStorage.removeItem('projectBound')
        }
      }
    },

    // 重置上传表单
    resetUploadForm() {
      this.uploadForm = {
        merName: '',
        levyId: '',
        channelId: '',
        file: null
      }
      this.selectedFile = null
      this.merNameArr = []
      this.levyIdArr = []
      this.channelIdArr = []
      this.$refs.uploadForm.resetFields()
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }

      // 同时重置项目绑定表单
      this.bindingForm = {
        merName: '',
        levyId: ''
      }
      this.bindingMerNameArr = []
      this.bindingLevyIdArr = []
      this.$refs.bindingForm.resetFields()

      // 清除项目绑定状态
      this.isProjectBound = false
      localStorage.removeItem('projectBound')
    }
  }
}
</script>

<style scoped>
.data-export-container {
  padding: 20px;
}

.export-section {
  margin-bottom: 20px;
}

.project-binding-section {
  margin-bottom: 20px;
}

.upload-section {
  margin-top: 20px;
}

.binding-form {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.export-form,
.upload-form {
  padding: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

</style>
