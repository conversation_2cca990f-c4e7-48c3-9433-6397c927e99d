// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar
$menuText:rgba(255, 255, 255, .7);
$menuTextHover: #fff;
$menuActiveText:#fff;
$subMenuActiveText:#fff;

$menuBg:#ffffff;
$menuHover:#E4E4E4;   //鼠标滑过菜单
$subMenuHover:#E4E4E4;

$subMenuActive:#E4E4E4;  //当前active
$subMenuBg:yellow;     //背景色


$sideBarWidth: 240px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  menuTextHover: $menuTextHover;
}
