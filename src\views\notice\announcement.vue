<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <!--                <el-col :lg="10" :xs="24">-->
                <!--                  <el-form-item label="发布时间" class="form-items">-->
                <!--                    <el-date-picker-->
                <!--                      v-model="pickTime"-->
                <!--                      class="auto-width"-->
                <!--                      type="datetimerange"-->
                <!--                      align="right"-->
                <!--                      unlink-panels-->
                <!--                      value-format="yyyy-MM-dd HH:mm:ss"-->
                <!--                      range-separator="-"-->
                <!--                      start-placeholder="开始时间"-->
                <!--                      end-placeholder="结束时间"-->
                <!--                      :picker-options="pickerOptions"-->
                <!--                      @change="resetTime"-->
                <!--                    />-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->
                <el-col :lg="8">
                  <el-form-item label="通知时间起" class="form-items" prop="startTime" label-width="120">
                    <el-date-picker
                      v-model="formData.startTime"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="通知时间起"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8">
                  <el-form-item label="通知时间止" class="form-items" prop="endTime" label-width="120">
                    <el-date-picker
                      v-model="formData.endTime"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="通知时间止"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="平台" class="form-items">
                    <el-select v-model="formData.receiveScopeType" placeholder="请选择" class="auto-width">
                      <el-option label="运营系统" value="0" />
                      <el-option label="商户系统" value="1" />
                      <el-option label="代理商系统" value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="通知状态" class="form-items">
                    <el-select v-model="formData.operate" placeholder="请选择" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option label="未启用" value="0" />
                      <el-option label="启用" value="1" />
                      <el-option label="已过期" value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-download" plain type="primary" @click="openbox('add')">添加公告</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="startTime"
            label="开始时间"
            width="150"
          />
          <el-table-column
            prop="endTime"
            label="过期时间"
            width="150"
          />
          <el-table-column
            prop="category"
            label="平台"
            width="100"
          />
          <el-table-column
            prop="name"
            label="通知名称"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="content"
            label="通知内容"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="operate"
            label="通知状态"
            width="80"
          />
          <el-table-column
            label="操作"
            width="110"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="openbox('edit',scope.row)">修改</el-button>
              <el-button v-if="scope.row.operate == '未启用'" type="text" @click="enable(scope.row.id)">启用</el-button>
              <el-button v-else type="text" @click="stop(scope.row.id)">停用</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="handleSizeChange"
        />
      </el-card>
    </div>

    <!--    添加公告-->
    <el-dialog :title="title" :visible.sync="dialogMain" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form
          ref="dialogMainData"
          :model="dialogMainData"
          size="mini"
          class="form-style"
          label-width="90px"
          :rules="rules"
        >

          <el-form-item label="平台" prop="receiveScopes">
            <el-select v-model="dialogMainData.receiveScopes" placeholder="请选择活动区域" class="auto-width">
              <el-option label="运营平台" value="0" />
              <el-option label="商户平台" value="1" />
              <el-option label="代理平台" value="2" />
            </el-select>
          </el-form-item>

          <el-form-item label="公告名称" prop="name">
            <el-input v-model="dialogMainData.name" placeholder="请输入内容" />
          </el-form-item>

          <el-form-item label="公告内容" prop="content">
            <el-input v-model="dialogMainData.content" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :lg="12">
              <el-form-item label="通知时间起" class="form-items" prop="startTime" label-width="120">
                <el-date-picker
                  v-model="dialogMainData.startTime"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="通知时间起"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="12">
              <el-form-item label="通知时间止" class="form-items" prop="endTime" label-width="120">
                <el-date-picker
                  v-model="dialogMainData.endTime"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="通知时间止"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!--          <el-form-item label="通知时间" prop="pickTime">-->
          <!--            <el-date-picker-->
          <!--              v-model="dialogMainData.pickTime"-->
          <!--              class="auto-width"-->
          <!--              type="datetimerange"-->
          <!--              align="right"-->
          <!--              unlink-panels-->
          <!--              value-format="yyyy-MM-dd HH:mm:ss"-->
          <!--              range-separator="-"-->
          <!--              start-placeholder="开始时间"-->
          <!--              end-placeholder="结束时间"-->
          <!--              :picker-options="pickerOptions"-->
          <!--              @change="resetTime1"-->
          <!--            />-->

          <!--          </el-form-item>-->

          <el-form-item label="" class="form-items">
            <el-button type="primary" :loading="btnLoading" @click="submit('dialogMainData')">保存</el-button>
            <el-button @click="dialogMain=false">取消</el-button>
            <!--            <el-button @click="resetForm('dialogMainData')">取消</el-button>-->
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import message from '@/axios/default/message'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Announcement',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        startTime: '',
        endTime: '',
        operate: '',
        receiveScopeType: '',
        type: '2'
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogMain: false,
      dialogMainData: this.initDialog(),
      title: '',
      dialogType: '',
      rules: {
        receiveScopes: [{ required: true, message: '请选择平台', trigger: 'change' }],
        content: [{ required: true, message: '请填写站内信内容', trigger: 'blur' }],
        pickTime: [{ required: true, message: '请选择通知时间', trigger: 'blur' }],
        name: [{ required: true, message: '请填写站内信名称', trigger: 'blur' }]
      },
      btnLoading: false
    }
  },
  computed: {
    user() {
      return this.$store.state.account.user
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      message.findNoticeByConditionByPage({
        ...this.formData,
        request: {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    resetTime1(val) {
      val = val || ['', '']
      this.dialogMainData.startTime = val[0]
      this.dialogMainData.endTime = val[1]
    },
    openbox(type, data) {
      this.dialogMain = true
      this.$nextTick(function() {
        this.resetForm('dialogMainData')
        if (type == 'add') {
          this.dialogMainData = this.initDialog()
          this.dialogType = 'add'
          this.title = '添加公告'
        } else {
          this.dialogMainData = {
            id: data.id,
            type: '2',
            name: data.name,
            content: data.content,
            receiveScopeType: '2',
            receiveScopes: data.receiveScope,
            startTime: data.startTime,
            endTime: data.endTime,
            modifyId: this.user.userId,
            pickTime: [data.startTime, data.endTime]
          }
          this.dialogType = 'edit'
          this.title = '修改公告'
        }
      })
    },
    initDialog() {
      return {
        type: '2',
        name: '',
        content: '',
        receiveScopeType: '2',
        receiveScopes: '',
        startTime: '',
        endTime: '',
        createId: ''
      }
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogType == 'add') {
            this.addMessage()
          } else {
            this.edit()
          }
        } else {
          return false
        }
      })
    },
    addMessage() {
      this.dialogMainData.createId = this.user.userId
      message.addMessageVO({
        ...this.dialogMainData
      }).then(res => {
        this.btnLoading = false
        this.$refs.dialogMainData.clearValidate()
        this.$refs.dialogMainData.resetFields()
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.dialogMain = false
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    edit() {
      message.updateMessageVO({
        ...this.dialogMainData
      }).then(res => {
        this.btnLoading = false
        this.$refs.dialogMainData.clearValidate()
        this.$refs.dialogMainData.resetFields()
        if (res.data.code == '0000') {
          this.dialogMain = false
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    enable(id) {
      message.updateMessageVO({
        id,
        operate: 1
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    stop(id) {
      message.updateMessageVO({
        id,
        operate: 0
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

.auto-width {
  width: 100%;
}

.dialog-form {
  display: flex;
  flex-flow: column nowrap;
}

.el-form-item__content {
  margin-left: 0;
}

.list-table {
  margin: 0 0 20px 0;

  .el-button {
    padding: 0;
  }
}

.dialog-scroll {
  overflow-y: auto;
  height: calc(100%);
}

.reset-dialog {
  /deep/ .el-dialog {
    height: 80vh;
    overflow: hidden;

    .el-dialog__body {
      height: calc(100% - 54px);
    }
  }

  overflow: hidden;
}

.reset-dialog-small {
  /deep/ .el-dialog {
    height: 65vh;
  }

  overflow: hidden;
}

.form-style {
  padding-right: 20px;
}

</style>

