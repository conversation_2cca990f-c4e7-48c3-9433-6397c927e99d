// 商户管理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  addMerchantInfo: `${API_Header}/merchantInfo/addMerchantInfo`, // 新增商户
  updateStatus: `${API_Header}/merchantInfo/updateStatus`, // 开通禁用商户
  audit_list: `${API_Header}/merchantInfo/audit_list`, // 商户审核列表
  list: `${API_Header}/merchantInfo/list`, // 商户列表
  // allList: `${API_Header}/merchantInfo/allList`, // 商户列表
  checkLoginName: `${API_Header}/merchantInfo/checkLoginName`, // 判断商户用户名是否唯一
  getOneMerchantInfo: `${API_Header}/merchantInfo/getOneMerchantInfo`, // 根据id获取商户信息
  delete: `${API_Header}/merchantInfo/delete`, // 删除商户
  getChannelListByLevyId: `${API_Header}/merchantInfo/getChannelListByLevyId`, // 根据主体查询通道

  files: `${API_Header}/upload/files`, // 查询报税行业类型
  updateMerchantInfo: `${API_Header}/merchantInfo/updateMerchantInfo`, // 修改
  resetPws: `${API_Header}/merchantInfo/resetPws`, // 修改商户密码
  merchantAudit: `${API_Header}/merchantInfo/merchantAudit`, // 商户初审/复审

  auth_list: `${API_Header}/merchantInfo/auth_list`, // 审批列表
  merchantAuth: `${API_Header}/merchantInfo/merchantAuth`, // 审批
  getAuthList: `${API_Header}/merchantInfo/getAuthList`, // 审批记录

  merDownLoadData: `${API_Header}/merchantInfo/merDownLoadData`, // 下载
  // levyItems: `${API_Header}/levyItems/list` // 查询征收品目列表

  // 商户充值列表
  findMerchantRechargeManager: `${API_Header}/merchantRechargeManager/findMerchantRechargeManager`, // 充值账户列表
  addMerchantRechargeManager: `${API_Header}/merchantRechargeManager/addMerchantRechargeManager`, // 新增
  updateMerchantRechargeManager: `${API_Header}/merchantRechargeManager/updateMerchantRechargeManager`, // 修改
  deleteMerchantRechargeManager: `${API_Header}/merchantRechargeManager/deleteMerchantRechargeManager`, // 删除
  excel: `${API_Header}/merchantRechargeManager/excel`, // 删除

  //集团商户列表
  queryMerchantInfoGroup:`${API_Header}/merchantInfoGroup/queryMerchantInfoGroup`,
  addMerchantInfoGroup:`${API_Header}/merchantInfoGroup/addMerchantInfoGroup`,
  updateMerchantInfoGroup:`${API_Header}/merchantInfoGroup/updateMerchantInfoGroup`,
  queryMerchantInfoGroupById:`${API_Header}/merchantInfoGroup/queryMerchantInfoGroupById`,
  delayDays:`${API_Header}/merchantInfo/delayDays`,
  queryMerchantMessageByPage: `${API_Header}/merchantMessage/queryMerchantMessageByPage`,
  addMerchantMessage: `${API_Header}/merchantMessage/addMerchantMessage`,
  updateMerchantMessage: `${API_Header}/merchantMessage/updateMerchantMessage`,
  queryMerchantMessageById: `${API_Header}/merchantMessage/queryMerchantMessageById`,
  querySecondCommissionRebateByPage: `${API_Header}/secondCommissionRebate/querySecondCommissionRebateByPage`




}

const merchants = {
  addMerchantInfo: params => {
    return request.postJson(api.addMerchantInfo, params)
  },
  updateStatus: params => {
    return request.postJson(api.updateStatus, params)
  },
  delayDays: params => {
    return request.postJson(api.delayDays, params)
  },
  audit_list: params => {
    return request.postJson(api.audit_list, params)
  },
  list: params => {
    return request.postJson(api.list, params)
  },
  // allList: params => {
  //   return request.postJson(api.allList, params)
  // },
  checkLoginName: params => {
    return request.postJson(api.checkLoginName, params)
  },
  delete: params => {
    return request.postJson(api.delete, params)
  },
  getOneMerchantInfo: params => {
    return request.postJson(api.getOneMerchantInfo, params)
  },
  getChannelListByLevyId: params => {
    return request.postJson(api.getChannelListByLevyId, params)
  },
  mccList: params => {
    return request.postJson(api.mccList, params)
  },
  files: params => {
    return request.upload(api.files, params)
  },
  updateMerchantInfo: params => {
    return request.postJson(api.updateMerchantInfo, params)
  },
  resetPws: params => {
    return request.postJson(api.resetPws, params)
  },
  merchantAudit: params => {
    return request.postJson(api.merchantAudit, params)
  },
  auth_list: params => {
    return request.postJson(api.auth_list, params)
  },
  merchantAuth: params => {
    return request.postJson(api.merchantAuth, params)
  },
  getAuthList: params => {
    return request.postJson(api.getAuthList, params)
  },
  levyItems: params => {
    return request.postJson(api.levyItems, params)
  },
  merDownLoadData: (params, filename, type) => {
    return request.downFiles(api.merDownLoadData, params, filename, type)
  },
  findMerchantRechargeManager: params => {
    return request.postJson(api.findMerchantRechargeManager, params)
  },
  addMerchantRechargeManager: params => {
    return request.postJson(api.addMerchantRechargeManager, params)
  },
  updateMerchantRechargeManager: params => {
    return request.postJson(api.updateMerchantRechargeManager, params)
  },
  deleteMerchantRechargeManager: params => {
    return request.postJson(api.deleteMerchantRechargeManager, params)
  },
  excel: (params, filename, type) => {
    return request.downFiles(api.excel, params, filename, type)
  },
  queryMerchantInfoGroup: params => {
    return request.postJson(api.queryMerchantInfoGroup, params)
  },
  addMerchantInfoGroup: params => {
    return request.postJson(api.addMerchantInfoGroup, params)
  },
  updateMerchantInfoGroup: params => {
    return request.postJson(api.updateMerchantInfoGroup, params)
  },
  queryMerchantInfoGroupById: params => {
    return request.postJson(api.queryMerchantInfoGroupById, params)
  },
  queryMerchantMessageByPage: params => {
    return request.postJson(api.queryMerchantMessageByPage, params)
  },
  addMerchantMessage: params => {
    return request.postJson(api.addMerchantMessage, params)
  },
  updateMerchantMessage: params => {
    return request.postJson(api.updateMerchantMessage, params)
  },
  queryMerchantMessageById: params => {
    return request.postJson(api.queryMerchantMessageById, params)
  },
  querySecondCommissionRebateByPage: params => {
    return request.postJson(api.querySecondCommissionRebateByPage, params)
  },
}

export default merchants
