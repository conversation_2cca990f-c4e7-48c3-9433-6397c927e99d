<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logoSmall" :src="logoSmall" class="sidebar-logo logo-small">
        <!--        <h1 v-else class="sidebar-title">{{ title }} </h1>-->
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo">
        <!--        <h1 class="sidebar-title">{{ $t('system.title') }} <span>cloud</span></h1>-->
      </router-link>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    logo() {
      return require('@/assets/logo.png')
    },
    logoSmall() {
      return require('@/assets/logo-small.png')
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 54px;
  line-height: 54px;
  background: #FFFFFF;
  text-align: center;
  overflow: hidden;
  color: #fff;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex!important;
    justify-content: center;
    align-items: center;
    & .sidebar-logo {
      width: 180px;
    }

    & .logo-small {
      width: 32px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 500;
      line-height: 50px;
      font-size: 19px;
      vertical-align: middle;
      & span {
        font-size: 12px;
        letter-spacing: 1px;
      }
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0;
    }
  }
}
</style>
