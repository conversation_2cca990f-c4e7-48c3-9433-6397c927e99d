// 结算管理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  queryTradeOrderDetailsService: `${API_Header}/tradeService/queryTradeOrderDetailsService`, // 结算列表查询接口
  queryBigOrderDetailsByPage: `${API_Header}/bigOrderDetails/queryBigOrderDetailsByPage`, // 查询10w+订单列表接口
  addTaxPaymentCertificate: `${API_Header}/bigOrderDetails/addTaxPaymentCertificate`, // 超10w订单上传完税证明
  insertTradOrderInfos: `${API_Header}/tradeBigOrderInfo/insertTradOrderInfos`, // 查询10w+订单列表接口
  auditTradeBigOrderDetails: `${API_Header}/bigOrderDetails/auditTradeBigOrderDetails`, // 10w+订单审核接口
  queryAuditDetailsByIds: `${API_Header}/bigOrderDetails/queryAuditDetailsByIds`, // 10w+审核页面加载数据接口
  queryTradeOrderInfoService: `${API_Header}/tradeService/queryTradeOrderInfoService`, // 结算初审/复审列表查询接口
  operateAuditTradeInfo: `${API_Header}/tradeService/operateAuditTradeInfo`, // 结算初审/复审列表查询接口
  queryTradeDetailByTradeId: `${API_Header}/tradeService/queryTradeDetailByTradeId`, // 结算初审/复审列表查询接口
  queryTradeAuditRecord: `${API_Header}/tradeBigOrderInfo/queryTradeAuditRecord`, // 审核记录查询接口
  queryTradOrderInfosById: `${API_Header}/tradeService/queryTradOrderInfosById`, // 审核记录查询接口
  queryExcelMoney: `${API_Header}/tradeBigOrderInfo/queryExcelMoney`, // 审核记录查询接口
  downTradeOrderDetails: `${API_Header}/tradeService/downTradeOrderDetails`, // 结算下载
  downSettReceipt: `${API_Header}/tradeService/downSettReceipt`, // 结算清单导出功能

  downBigOrderDetails: `${API_Header}/bigOrderDetails/downBigOrderDetails`, // 10w+订单导出功能
  downTradeInfo: `merchants/tradOrderInfo/downTradeInfo`, // 结算导出接口

  feeRateDownLoadData: `${API_Header}/feeRateRecord/feeRateDownLoadData`, // 费率变更下载数据
  findFeeRateRecords: `${API_Header}/feeRateRecord/findFeeRateRecords`, // 费率变更
  tradeServiceImport: `${API_Header}/tradeService/import`, // 结算电子回单上传

  queryTradeOrderDetailsServiceYd: `${API_Header}/tradeService/queryTradeOrderDetailsServiceYd` // 税务对账申报明细
}

const tradeService = {
  queryTradeOrderDetailsService: params => {
    return request.postJson(api.queryTradeOrderDetailsService, params)
  },
  queryBigOrderDetailsByPage: params => {
    return request.postJson(api.queryBigOrderDetailsByPage, params)
  },
  addTaxPaymentCertificate: params => {
    return request.postJson(api.addTaxPaymentCertificate, params)
  },
  insertTradOrderInfos: params => {
    return request.postJson(api.insertTradOrderInfos, params)
  },
  auditTradeBigOrderDetails: params => {
    return request.postJson(api.auditTradeBigOrderDetails, params)
  },
  queryAuditDetailsByIds: params => {
    return request.postJson(api.queryAuditDetailsByIds, params)
  },
  queryTradeOrderInfoService: params => {
    return request.postJson(api.queryTradeOrderInfoService, params)
  },
  operateAuditTradeInfo: params => {
    return request.postJson(api.operateAuditTradeInfo, params)
  },
  queryTradeDetailByTradeId: params => {
    return request.postJson(api.queryTradeDetailByTradeId, params)
  },
  queryTradeAuditRecord: params => {
    return request.postJson(api.queryTradeAuditRecord, params)
  },
  queryTradOrderInfosById: params => {
    return request.postJson(api.queryTradOrderInfosById, params)
  },
  queryExcelMoney: params => {
    return request.postJson(api.queryExcelMoney, params)
  },
  downBigOrderDetails: (params, filename, type) => {
    return request.downFiles(api.downBigOrderDetails, params, filename, type)
  },
  downTradeInfo: (params, filename, type) => {
    return request.downFiles(api.downTradeInfo, params, filename, type)
  },
  downTradeOrderDetails: (params, filename, type) => {
    return request.downFiles(api.downTradeOrderDetails, params, filename, type)
  },
  downSettReceipt: (params, filename, type) => {
    return request.downFiles(api.downSettReceipt, params, filename, type)
  },
  findFeeRateRecords: params => {
    return request.postJson(api.findFeeRateRecords, params)
  },
  feeRateDownLoadData: (params, filename, type) => {
    return request.downFiles(api.feeRateDownLoadData, params, filename, type)
  },
  tradeServiceImport: (params, filename, type) => {
    return request.postJson(api.tradeServiceImport, params)
  },
  queryTradeOrderDetailsServiceYd: (params, filename, type) => {
    return request.postJson(api.queryTradeOrderDetailsServiceYd, params)
  }
}

export default tradeService

