<template>
  <el-form ref="payform" :model="formData" :rules="payrules" label-position="right" label-width="120px" class="form" size="mini">
    <el-form-item label="旧支付密码" prop="oldPayPassWord">
      <el-input v-model="formData.oldPayPassWord" show-password type="password" />
    </el-form-item>
    <el-form-item label="新支付密码" prop="newPayPassWord">
      <el-input v-model="formData.newPayPassWord" show-password type="password" />
    </el-form-item>
    <el-form-item label="新支付密码" prop="confirmPassword">
      <el-input v-model="formData.confirmPassword" show-password type="password" />
    </el-form-item>
    <el-form-item>
      <el-button class="xzb-btn xzb-main-btn-style" :loading="buttonLoading" @click="submit">{{ $t('common.edit') }}</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import publics from '@/axios/default/public'
export default {
  data() {
    return {
      buttonLoading: false,
      formData: {
        oldPayPassWord: '',
        newPayPassWord: '',
        confirmPassword: ''
      },
      payrules: {
        oldPayPassWord: [
          { required: true, message: this.$t('rules.require'), trigger: 'blur' }
        ],
        newPayPassWord: [
          { required: true, message: this.$t('rules.require'), trigger: 'blur' },
          { min: 6, max: 20, message: this.$t('rules.range6to20'), trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: this.$t('rules.require'), trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (this.formData.newPayPassWord !== value) {
              callback(this.$t('tips.notEqual'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.payform.validate((valid) => {
        if (valid) {
          this.buttonLoading = true
          publics.resetPayPassword({
            passwordType: '2',
            oldPayPassWord: this.formData.oldPayPassWord,
            newPayPassWord: this.formData.newPayPassWord
          }).then(res => {
            this.buttonLoading = false
            if (res.data.code === '0000') {
              this.$message.success('操作成功')
            } else {
              this.$alert(res.data.message, '系统提示', {
                type: 'warning',
                showConfirmButton: false,
                iconClass: 'el-icon-my-alert'
              })
            }
            this.$refs.payform.clearValidate()
            this.$refs.payform.resetFields()
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
