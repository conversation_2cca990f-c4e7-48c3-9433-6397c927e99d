// 风控api
import request from '@/axios/default/request'
import { API_system } from '../index'

const api = {
  merList: `${API_system}/risk/merList`, // 商户监控
  freelancerList: `${API_system}/risk/freelancerList`, // 自由职业者监控
  downLoadFreelancerList: `${API_system}/risk/downLoadFreelancerList`, // 自由职业者监控
  findFreelancerBlacklists: `${API_system}/freelancerBlacklist/findFreelancerBlacklists`, // 董监高列表
  findFreelancerInfoBlacklists: `${API_system}/freelancerInfoBlacklist/findFreelancerBlacklists`, // 自由职业者黑名单
  addFreelancerBlacklist: `${API_system}/freelancerInfoBlacklist/addFreelancerBlacklist`, // 添加自由职业者黑名单
  findMerchantBlacklists: `${API_system}/merchantBlacklist/findMerchantBlacklists`, // 商户黑名单列表
  addMerchantBlacklist: `${API_system}/merchantBlacklist/addMerchantBlacklist`, // 添加商户黑名单
  returnvisit: `${API_system}/risk/returnvisit`, // 回访记录
  addReturnvisit: `${API_system}/risk/addReturnvisit`, // 新增回访记录
  updateReturnvisit: `${API_system}/risk/updateReturnvisit`, // 修改回访记录
  deleteReturnvisit: `${API_system}/risk/deleteReturnvisit` // 删除回访记录
}

const riskApi = {
  merList: params => {
    return request.postJson(api.merList, params)
  },
  freelancerList: params => {
    return request.postJson(api.freelancerList, params)
  },
  downLoadFreelancerList: (params, filename, type) => {
    return request.downFiles(api.downLoadFreelancerList, params, filename, type)
  },
  findFreelancerBlacklists: params => {
    return request.postJson(api.findFreelancerBlacklists, params)
  },
  findFreelancerInfoBlacklists: params => {
    return request.postJson(api.findFreelancerInfoBlacklists, params)
  },
  addFreelancerBlacklist: params => {
    return request.postJson(api.addFreelancerBlacklist, params)
  },
  findMerchantBlacklists: params => {
    return request.postJson(api.findMerchantBlacklists, params)
  },
  addMerchantBlacklist: params => {
    return request.postJson(api.addMerchantBlacklist, params)
  },
  returnvisit: params => {
    return request.postJson(api.returnvisit, params)
  },
  addReturnvisit: params => {
    return request.postJson(api.addReturnvisit, params)
  },
  updateReturnvisit: params => {
    return request.postJson(api.updateReturnvisit, params)
  },
  deleteReturnvisit: params => {
    return request.postJson(api.deleteReturnvisit, params)
  }
}

export default riskApi

