// 项目管理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  findProjectInfoList: `${API_Header}/projectInfo/findProjectInfoList`, // 项目管理列表
  addProjectInfo: `${API_Header}/projectInfo/addProjectInfo`, // 添加项目
  updateProjectInfo: `${API_Header}/projectInfo/updateProjectInfo`, // 编辑项目
  findTypeBymerIdLevyId: `${API_Header}/projectInfo/findTypeBymerIdLevyId`, // 查询商户下二级发票类目接口
  signList: `${API_Header}/signUpDetail/list`, // 报名列表
  addDeliveryFile: `${API_Header}/signUpDetail/addDeliveryFile`, // 上传报名者交付物
  proAddDeliveryFile: `${API_Header}/projectInfo/addDeliveryFile`, // 任务交付物上传
  getOneDeliveryFile: `${API_Header}/signUpDetail/getOneDeliveryFile`, // 报名者交付物详情
  getProOneDeliveryFile: `${API_Header}/projectInfo/getOneDeliveryFile`, // 查询项目交付物详情
  submitProject: `${API_Header}/projectInfo/submitProject`, // 提交项目审核
  auditProject: `${API_Header}/merchantInfo/auditProject`, // 审核项目

  batchDownLoadFile: `${API_Header}/signUpDetail/batchDownLoadFile`, // 批量下载交付物
  batchProDownLoadFile: `${API_Header}/projectInfo/batchDownLoadFile`, // 批量下载项目交付物
  signUpDetailDownLoadData: `${API_Header}/signUpDetail/signUpDetailDownLoadData`, // 报名详情下载数据
  downProjectDetail: `${API_Header}/projectInfo/findProjectInfoListExcel` // 导出
}

const proManage = {
  findProjectInfoList: params => {
    return request.postJson(api.findProjectInfoList, params)
  },
  addProjectInfo: params => {
    return request.postJson(api.addProjectInfo, params)
  },
  findTypeBymerIdLevyId: params => {
    return request.postJson(api.findTypeBymerIdLevyId, params)
  },
  updateProjectInfo: params => {
    return request.postJson(api.updateProjectInfo, params)
  },
  signList: params => {
    return request.postJson(api.signList, params)
  },
  addDeliveryFile: params => {
    return request.postJson(api.addDeliveryFile, params)
  },
  proAddDeliveryFile: params => {
    return request.postJson(api.proAddDeliveryFile, params)
  },
  getOneDeliveryFile: params => {
    return request.postJson(api.getOneDeliveryFile, params)
  },
  getProOneDeliveryFile: params => {
    return request.postJson(api.getProOneDeliveryFile, params)
  },
  submitProject: params => {
    return request.postJson(api.submitProject, params)
  },
  auditProject: params => {
    return request.postJson(api.auditProject, params)
  },
  batchDownLoadFile: (params, filename, type) => {
    return request.downFiles(api.batchDownLoadFile, params, filename, type)
  },
  batchProDownLoadFile: (params, filename, type) => {
    return request.downFiles(api.batchProDownLoadFile, params, filename, type)
  },
  signUpDetailDownLoadData: (params, filename, type) => {
    return request.downFiles(api.signUpDetailDownLoadData, params, filename, type)
  },
  downProjectDetail: (params, filename, type) => {
    return request.downFiles(api.downProjectDetail, params, filename, type)
  }
}

export default proManage

