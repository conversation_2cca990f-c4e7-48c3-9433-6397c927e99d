<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <!--                <el-col :lg="10" :xs="24">-->
                <!--                  <el-form-item label="充值创建时间" class="form-items">-->
                <!--                    <el-date-picker-->
                <!--                      v-model="pickerTime"-->
                <!--                      class="auto-width"-->
                <!--                      type="daterange"-->
                <!--                      align="right"-->
                <!--                      unlink-panels-->
                <!--                      value-format="yyyy-MM-dd"-->
                <!--                      range-separator="-"-->
                <!--                      start-placeholder="开始时间"-->
                <!--                      end-placeholder="结束时间"-->
                <!--                      :picker-options="pickerOptions"-->
                <!--                    />-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->

                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值时间起" class="form-items" prop="createTimeFrom">
                    <el-date-picker
                      v-model="formData.createTimeFrom"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值时间止" class="form-items" prop="createTimeTo">
                    <el-date-picker
                      v-model="formData.createTimeTo"
                      class="auto-width"
                      type="datetime"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :clearable="false"
                      :editable="false"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="打款名称" class="form-items">
                    <el-input v-model="formData.rechName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="收款名称" class="form-items">
                    <el-input v-model="formData.levyName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">

                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="rechName"
            label="打款名称"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="rechMoney"
            label="打款金额"
            width="100"
            show-overflow-tooltip
          />
          <!--          <el-table-column-->
          <!--            prop="channelName"-->
          <!--            label="打款渠道"-->
          <!--            show-overflow-tooltip-->
          <!--          />-->
          <el-table-column
            prop="levyName"
            label="收款名称"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="receiveBank"
            label="收款银行"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="receiveBankNo"
            label="收款账号"
            width="200"
          />
          <el-table-column
            prop="createTime"
            label="充值创建时间"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="auditState"
            label="审核状态"
          >
            <template slot-scope="scope">
              {{ auditState[scope.row.auditState] }}
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            width="80"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="dialogTrial(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>
    </div>

    <!--    充值录入-->
    <el-dialog title="充值初审" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称">
            <el-input v-model="dialogData.merName" autocomplete="off" class="auto-width" disabled />
          </el-form-item>
          <el-form-item label="代征主体">
            <el-input v-model="dialogData.levyId" autocomplete="off" class="auto-width" disabled />
          </el-form-item>
          <el-form-item label="充值通道">
            <el-input v-model="dialogData.channelId" autocomplete="off" class="auto-width" disabled />
          </el-form-item>
          <el-form-item label="充值账号">
            <el-input v-model="rechBankNo" autocomplete="off" class="auto-width" disabled />
          </el-form-item>
          <el-form-item label="充值金额">
            <el-input v-model="dialogData.rechMoney" autocomplete="off" class="auto-width" disabled />
          </el-form-item>
          <el-form-item label="备注" prop="auditRemark">
            <el-input v-model="dialogData.auditRemark" type="textarea" :rows="2" autocomplete="off" class="auto-width" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogData','1')">通过</el-button>
              <el-button :loading="btnLoading" @click="submit('dialogData','2')">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import rechInfo from '@/axios/default/rechInfo'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        merName: '',
        rechName: '',
        levyName: '',
        auditState: '1'
      },
      dialogData: {
        id: '',
        merName: '',
        levyId: '',
        channelId: '',
        rechMoney: '',
        auditRemark: ''
      },
      btnLoading: false,
      rechBankNo: '',
      dialogFormVisible: false,
      formLabelWidth: '180px',
      listData: [],
      merNameArr: [],
      levyIdArr: [],
      channelIdArr: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      auditState: { '1': '初审中', '2': '初审拒绝', '3': '复审中', '4': '审核通过', '5': '复审拒绝' },
      rules: {
        auditRemark: { required: true, message: '请填写备注', trigger: 'blur' }
      }
    }
  },
  mounted() {
    this.queryRechInfosByPage()
  },
  methods: {
    submit(formName, btnType) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          this.auditRechInfo(btnType)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    dialogTrial(data) {
      this.dialogFormVisible = true
      this.dialogData.merName = data.merName
      this.dialogData.id = data.id
      this.dialogData.levyId = data.levyName
      this.dialogData.channelId = data.channelName
      this.dialogData.rechMoney = data.rechMoney
      this.dialogData.auditRemark = data.auditRemark
      this.rechBankNo = data.rechBankNo
    },
    // 审核提交
    auditRechInfo(btnType) {
      rechInfo.auditRechInfo({
        id: this.dialogData.id,
        auditRemark: this.dialogData.auditRemark,
        btnType
      }).then(res => {
        this.btnLoading = false
        if (res.data.code === '0000') {
          this.$refs.dialogData.resetFields()
          this.dialogFormVisible = false
          this.queryRechInfosByPage()
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    onSearch() {
      this.pageNum = 1
      this.queryRechInfosByPage()
    },
    // 充值,初审,复审列表
    queryRechInfosByPage() {
      rechInfo.queryRechInfosByPage({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'rechInfo': {
          createTimeFrom: moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss'),
          createTimeTo: moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss'),
          'merName': this.formData.merName,
          'rechName': this.formData.rechName,
          'levyName': this.formData.levyName,
          'auditState': this.formData.auditState // 1:初审,3:复审, 为空时,充值列表
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryRechInfosByPage()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .pagination{
    text-align: right;
  }
</style>
