<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">

            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间起" class="form-items" prop="createTimeFrom">
                <el-date-picker
                  v-model="formData.createTimeFrom"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间止" class="form-items" prop="createTimeTo">
                <el-date-picker
                  v-model="formData.createTimeTo"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="企业名称" class="form-items">
                <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="商户批次号" class="form-items">
                <el-input v-model="formData.batchNum" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="收款名称" class="form-items">
                <el-input v-model="formData.payeeName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="24" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item class="form-items" style="text-align:right">
                <!--                    <el-button icon="el-icon-download" plain type="primary" @click="downBigOrderDetails">下载文件</el-button>-->
                <el-button icon="el-icon-download" plain type="primary" @click="multipleTrialAudit">批量审核</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            align="center"
          />
          <el-table-column
            prop="merName"
            label="企业名称"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            label="订单时间"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="payeeName"
            label="姓名"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="payeeIdCard"
            label="身份证号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="orderState"
            label="审核状态"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ orderState[scope.row.orderState] }}
            </template>
          </el-table-column>

          <!--          <el-table-column-->
          <!--            prop="tradeState"-->
          <!--            label="交易状态"-->
          <!--            show-overflow-tooltip-->
          <!--          />-->
          <el-table-column
            prop="settMoney"
            label="结算金额"
          >
            <template slot-scope="scope">
              {{ scope.row.settMoney }}元
            </template>
          </el-table-column>
          <el-table-column
            prop="batchNum"
            label="商户批次号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="merOrderNum"
            label="商户订单号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
            prop="address"
            label="操作"
            width="120"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="trialAudit(scope.row)">审核</el-button>
              <el-button type="text" @click="viewInfo(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--        充值初审-->
    <el-dialog title="结算初审" :visible.sync="dialogaudit" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogauditData" :model="dialogauditData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="商户批次号">

            <el-input v-model="dialogauditData.batchNum" disabled />
          </el-form-item>
          <el-form-item label="结算金额">
            <el-input v-model="dialogauditData.settMoney" disabled />
          </el-form-item>
          <el-form-item label="服务费">
            <el-input v-model="dialogauditData.platFree" disabled />
          </el-form-item>
          <el-form-item label="备注" prop="auditRemark">
            <el-input v-model="dialogauditData.auditRemark" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" autocomplete="off" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogauditData','1')">通过</el-button>
              <el-button :loading="btnLoading" @click="submit('dialogauditData','2')">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

    <!--    查看订单详情-->
    <el-dialog title="查看订单详情" :visible.sync="dialogtrial" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form :model="dialogtrialData" label-width="140px" size="mini" class="form-style">
          <el-form-item label="企业名称">
            <el-input v-model="dialogtrialData.merName" disabled />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="dialogtrialData.payeeName" disabled />
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="dialogtrialData.payeeIdCard" disabled />
          </el-form-item>
          <el-form-item label="银行卡号">
            <el-input v-model="dialogtrialData.payeeAcc" disabled />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="dialogtrialData.telephone" disabled />
          </el-form-item>
          <!--          <el-form-item label="收款银行">-->
          <!--            <el-input v-model="dialogtrialData.bankName" disabled />-->
          <!--          </el-form-item>-->
          <el-form-item label="结算金额">
            <el-input v-model="dialogtrialData.settMoney" disabled />
          </el-form-item>
          <el-form-item label="服务费率">
            <el-input v-model="dialogtrialData.freeRate" disabled />
          </el-form-item>
          <el-form-item label="支付通道">
            <el-input v-model="dialogtrialData.channelName" disabled />
          </el-form-item>
          <el-form-item label="商户批次号">
            <el-input v-model="dialogtrialData.batchNum" disabled />
          </el-form-item>
          <el-form-item label="商户订单号">
            <el-input v-model="dialogtrialData.merOrderNum" disabled />
          </el-form-item>
          <el-form-item label="订单创建时间">
            <el-input v-model="dialogtrialData.createTime" disabled />
          </el-form-item>
          <el-form-item label="订单完成时间">
            <el-input v-model="dialogtrialData.orderCompTime" disabled />
          </el-form-item>
          <el-form-item label="订单备注">
            <el-input v-model="dialogtrialData.remark" type="textarea" :rows="2" disabled />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button @click="dialogtrial = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import tradeService from '@/axios/default/tradeService'
import { parseTime, newAlert } from '@/utils'
import moment from 'moment'
export default {
  name: 'Trial',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        merName: '',
        payeeName: '',
        batchNum: ''
      },
      formLabelWidth: '180px',
      listData: [],
      operateState: '',
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogaudit: false,
      dialogauditData: {
        auditRemark: ''
      }, // 审核框
      dialogtrial: false,
      dialogtrialData: {}, // 查看框
      multipleSelection: [],
      orderState: { 1: '初审中', 2: '初审拒绝', 3: '复审中', 4: '审核通过', 5: '复审拒绝', 6: '系统通过' },
      rules: {
        auditRemark: [{ required: true, message: '请输入初审备注', trigger: 'blur' }]
      },
      btnLoading: false
    }
  },
  mounted() {
    this.queryBigOrderDetailsByPage()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryBigOrderDetailsByPage()
    },
    // 列表数据
    queryBigOrderDetailsByPage() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      tradeService.queryBigOrderDetailsByPage({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'bigOrder': {
          'createTimeFrom': this.formData.createTimeFrom,
          'createTimeTo': this.formData.createTimeTo,
          'merName': this.formData.merName,
          'payeeName': this.formData.payeeName,
          'batchNum': this.formData.batchNum,
          'orderState': '1'
        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
      })
    },
    // 查看详情
    viewInfo(data) {
      this.dialogtrial = true
      this.dialogtrialData = data
    },
    // 审核按钮
    trialAudit(data) {
      this.dialogaudit = true
      this.dialogauditData.id = data.id
      this.dialogauditData.batchNum = data.batchNum
      this.dialogauditData.settMoney = data.settMoney
      this.dialogauditData.platFree = data.platFree
      this.dialogauditData.auditRemark = ''
    },
    // 批量审核
    multipleTrialAudit() {
      tradeService.queryAuditDetailsByIds({
        id: this.multipleSelection.join(',')
      }).then(res => {
        if (res.data.code == '0000') {
          const { data } = res.data
          this.dialogauditData.batchNum = data.bathNo
          this.dialogauditData.id = this.multipleSelection.join(',')
          this.dialogauditData.settMoney = data.settMoney
          this.dialogauditData.platFree = data.totalPlatFree
          this.dialogauditData.auditRemark = ''
          this.dialogaudit = true
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 提交审核 type：1通过 2不通过
    submit(formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          tradeService.auditTradeBigOrderDetails({
            // batchNum:this.dialogauditData.batchNum,
            id: this.dialogauditData.id,
            auditRemark: this.dialogauditData.auditRemark,
            btnType: type
          }).then(res => {
            if (res.data.code === '0000') {
              this.dialogauditData.auditRemark = ''
              this.$message({ type: 'success', message: '操作成功' })
              this.dialogaudit = false
              this.queryBigOrderDetailsByPage()
            } else {
              newAlert(this.$tips, res.data.message)
            }
            this.btnLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    // 分页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryBigOrderDetailsByPage()
    },
    // 表格复选框
    handleSelectionChange(val) {
      this.multipleSelection = val.map(res => {
        return res.id
      })
    },
    downBigOrderDetails() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      tradeService.downBigOrderDetails({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'bigOrder': {
          'createTimeFrom': this.formData.createTimeFrom,
          'createTimeTo': this.formData.createTimeTo,
          'merName': this.formData.merName,
          'payeeName': this.formData.payeeName,
          'batchNum': this.formData.batchNum,
          'orderState': '1'
        }
      }, `10w+订单${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>

