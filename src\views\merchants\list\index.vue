<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="开通时间" class="form-items">
                    <el-date-picker v-model="formData.creatTime" class="auto-width" type="daterange" align="right" unlink-panels range-separator="-" value-format="yyyy-MM-dd" start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions" @change="resetTime" />
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="当前状态" class="form-items">
                    <el-select v-model="formData.status" placeholder="请选择" class="auto-width">
                      <el-option label="选择全部" value="" />
                      <el-option label="编辑" value="0" />
                      <el-option label="初审中" value="1" />
                      <el-option label="复审中" value="2" />
                      <el-option label="正常" value="3" />
                      <el-option label="预开" value="4" />
                      <el-option label="停用" value="5" />
                      <el-option label="渠道审批中" value="6" />
                      <el-option label="风控审批中" value="7" />
                      <el-option label="运营审批中" value="8" />
                      <el-option label="观察中" value="9" />
                      <el-option label="已拉黑" value="10" />
                      <el-option label="已加白" value="11" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-for="(item,index) in levyBodyOpt" :key="index" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="所属销售" class="form-items">
                    <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="所属代理" class="form-items">
                    <el-input v-model="formData.agentName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="10" :xs="24">
                  <el-form-item label="商户编号" class="form-items">
                    <el-input v-model="formData.merNo" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="项目负责人" class="form-items">
                    <el-input v-model="formData.leaderName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" type="primary" @click="addMerchant">新增企业</el-button>
                    <el-button icon="el-icon-download" type="success" @click="merDownLoadData">下载数据</el-button>
                    <el-button icon="el-icon-search" type="primary" :disabled="requestLoading" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table :data="tableData" border stripe size="mini" class="list-table">
          <el-table-column v-for="(item,index) in listHeader" :key="index" :width="item.width" :fixed="item.fixed" :label="item.name">
            <template slot-scope="scope">
              <div v-if="item.key == 'action'">
                <el-button v-if="scope.row.status !=='1' && scope.row.status !== '2'" v-hasPermission="['merchants:list-update']" type="text" size="mini" class="btn-edit" @click="toEdit(scope.row.id)">编辑</el-button>
                <template v-if="scope.row.status == '0'">
                  <el-popconfirm title="确定删除吗？" @onConfirm="deleteData(scope.row.id)">
                    <el-button slot="reference" v-hasPermission="['merchants:list-del']" type="text" size="mini" class="btn-del">删除</el-button>
                  </el-popconfirm>
                </template>
                <!--            <template v-if="scope.row.status=='1' || scope.row.status == '2'"></template>-->
                <template v-if="scope.row.status=='3' || scope.row.status=='4'">
                  <el-popconfirm title="确定停用？" @onConfirm="updateStatus({row:scope.row,status:'5'})">

                    <el-button slot="reference" v-hasPermission="['merchants:list-stop']" type="text" size="mini" class="btn-stop">停用</el-button>
                  </el-popconfirm>
                </template>

                <!--                <template v-if="scope.row.status=='2' || scope.row.status=='1'">-->
                <!--                  <el-button type="text" size="mini" @click="viewInfo(scope.row.id)"> 查看</el-button>-->
                <!--                </template>-->
                <el-button type="text" size="mini" @click="viewInfo(scope.row.id)"> 查看</el-button>
                <!--            <template v-if="scope.row.status=='4'">-->
                <!--              <el-button  type="text" size="mini" v-hasPermission="['merchants:list-stop']" @click="updateStatus">停用</el-button>-->
                <!--            </template>-->
                <template v-if="scope.row.status=='5'">
                  <el-popconfirm title="确定开通？" @onConfirm="updateStatus({row:scope.row,status:'1'})">

                    <el-button slot="reference" v-hasPermission="['merchants:list-stop']" type="text" size="mini" class="btn-stop">开通</el-button>
                  </el-popconfirm>
                </template>

                <el-button type="text" size="mini" class="btn-enable" @click="changePassword({row:scope.row})">修改密码</el-button>

                <template v-if="scope.row.status=='4'">
                    <el-button slot="reference" type="text" size="mini" class="btn-stop"  @click="openBox({row:scope.row})">停用时间</el-button>
                </template>
              </div>
              <div v-else>
                <span v-if="item.key == 'status'">
                  {{ liststatus[scope.row[item.key]] }}
                </span>
                <span v-else>{{ scope.row[item.key] }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    修改密码弹窗-->
    <el-dialog :title="dialogName" :visible.sync="addListDialog" width="30%">
      <el-form ref="dialogData" :model="dialogData" size="mini" class="form-style" :rules="rules">
        <el-form-item label="企业名称" prop="merName" class="form-items">
          {{ dialogData.merName }}
        </el-form-item>
        <!--<el-form-item label="登录密码" prop="password" class="form-items">-->
          <!--<el-input v-model="dialogData.password" placeholder="请输入登录密码" />-->
        <!--</el-form-item>-->
        <!--<el-form-item label="支付密码" prop="payPassword" class="form-items">-->
          <!--<el-input v-model="dialogData.payPassword" placeholder="请输入支付密码" />-->
        <!--</el-form-item>-->
        <!--<el-form-item label="是否邮件接收" prop="sendFlag" class="form-items">-->
          <!--<el-radio-group v-model="dialogData.sendFlag" @change="sendFlagChange">-->
            <!--<el-radio :label="0">否</el-radio>-->
            <!--<el-radio :label="1">是</el-radio>-->
          <!--</el-radio-group>-->
        <!--</el-form-item>-->
        <el-form-item label="收件邮箱" prop="email" class="form-items">
          <el-input v-model="dialogData.email" placeholder="请输入收件邮箱" />
        </el-form-item>

        <el-form-item>
          <div style="text-align: right">
            <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">保存</el-button>
            <el-button :loading="btnLoading" @click="closeDialog('addListDialog','dialogData')">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog :title="dialogName" :visible.sync="dialogFormVisible" width="40%" >
        <el-form ref="dialogData" :model="dialogData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称" prop="merName" class="form-items">
          {{ dialogData.merName }}
        </el-form-item>

          <!-- <el-form-item label="停用时间" prop="delayDays">
            <div style="color: red; font-size: 12px;">*输入提示：自签约税源地之日开始计算，填入正数数字即可，如不输入，则默认为15日</div>
            <el-input v-model="dialogData.delayDays" type="text" placeholder="请输入停用时间" autocomplete="off" class="auto-width" />
          </el-form-item> -->


          <el-form-item label="停用时间" class="delayDays">
            <div style="color: red; font-size: 12px;">*如不输入，则默认为15日</div>
                  <el-date-picker v-model="dialogData.delayDays" class="auto-width" value-format="yyyy-MM-dd" type="date"  placeholder="选择日期" />
          </el-form-item>
          
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" :loading="btnLoading" @click="saveStopTime('dialogData')">保存</el-button>
              <el-button :loading="btnLoading" @click="dialogFormVisible=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>
<script>
import merchants from '@/axios/default/merchants'
import levyBody from '@/axios/default/levyBody'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'MerchantsList',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      dialogFormVisible: false,
      liststatus: { 0: '编辑', 1: '初审中', 2: '复审中', 3: '正常', 4: '预开', 5: '停用', 6: '渠道审批中', 7: '风控审批中', 8: '运营审批中', 9: '观察者', 10: '已拉黑', 11: '已加白' },
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        status: '',
        merName: '',
        leaderName: '',
        saleName: '',
        agentName:'',
        levyId: '',
        merNo: '',
        contractSigning: ''
      },
      listHeader: [{
        name: '商户编号',
        width: '200',
        key: 'merNo'
      }, {
        name: '企业名称',
        width: '250',
        key: 'merName'
      }, {
        name: '当前状态',
        width: '100',
        key: 'status'
      }, {
        name: '代征主体',
        width: '300',
        key: 'levyName'
      },
      // {
      //   name: '费率',
      //   width: '300',
      //   key: 'feeRate'
      // },
      {
        name: '项目负责人',
        width: '120',
        key: 'leaderName'
      }, {
        name: '所属销售',
        width: '100',
        key: 'saleName'
      }, {
        name: '所属代理',
        width: '100',
        key: 'agentName'
      },  {
        name: '开通时间',
        width: '150',
        key: 'openTime'
      }, {
        name: '操作',
        key: 'action',
        width: '200',
        fixed: 'right'
      }],
      tableData: [],
      levyBodyOpt: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },

      dialogData: {
        merName: '', // 企业名称
        id: 0, // 商户ID
        sendFlag: 1, // 是否发送邮件
        password: '', // 密码
        payPassword: '', // 支付密码
        email: '' ,// 邮箱
        delayDays:''
      },
      addListDialog: false,
      btnLoading: false,
      loading: false,
      requestLoading: false,
      dialogType: '',
      dialogName: '',
      rules: {
        // password: [{ required: true, message: '请填写输入登录密码', trigger: 'blur' }],
        // payPassword: [{ required: true, message: '请填写输入支付密码', trigger: 'blur' }],
        email: [
          { required: false, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  mounted() {
    this.initlevyBody()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      this.requestLoading = true
      merchants.list({
        ...this.formData,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.requestLoading = false
          this.tableData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    deleteData(id) {
      merchants.delete({ id }).then(res => {
        if (res.data.code == '0000') {
          const index = this.tableData.findIndex(res => res.id == id)
          this.tableData.splice(index, 1)
          this.$message.success('操作成功')
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initlevyBody() {
      levyBody.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyOpt = res.data.data
          if (res.data.data.length > 1) {
            this.levyBodyOpt.unshift({ name: '请选择', id: '' })
          } else {
            this.formData.levyId = this.levyBodyOpt[0].id
          }
        }
        this.list()
      })
    },
    // 开通禁用
    updateStatus(data) {
      const { row, status } = data
      merchants.updateStatus({
        id: row.id,
        status
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },

    openBox(data) {
      const { row } = data
      console.log(row, '拿到商户基本数据')
      this.loading = true
      merchants.getOneMerchantInfo({
        id: row.id
      }).then(res => {
        if (res.data.code === '0000') {
          const { data } = res.data
          console.log('获取商户详情数据', data)
          this.loading = false
          this.$set(this.dialogData, 'merName', data.merName)
          this.$set(this.dialogData, 'id', data.id)
          if(data.delayDays!=null){
            this.$set(this.dialogData, 'delayDays', data.delayDays)
          } else{
            this.$set(this.dialogData, 'delayDays', '')

          }

          this.dialogName = '停用时间'
          this.dialogFormVisible = true
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
      // this.dialogData = this.initDialogData()
    },

    // 修改密码
    changePassword(data) {
      const { row } = data
      console.log(row, '拿到商户基本数据')
      this.loading = true
      merchants.getOneMerchantInfo({
        id: row.id
      }).then(res => {
        if (res.data.code === '0000') {
          const { data } = res.data
          console.log('获取商户详情数据', data)

          this.loading = false

          this.dialogType = 'edit'
          this.$set(this.dialogData, 'merName', data.merName)
          this.$set(this.dialogData, 'id', data.id)
          this.$set(this.dialogData, 'email', data.authEmail)

          this.dialogName = '修改密码'
          this.addListDialog = true
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },

    sendFlagChange(v) {
      if (v === 1) {
        this.$set(
          this.rules,
          'email',
          [
            { required: !!v, message: '请输入邮箱地址', trigger: 'blur' },
            { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
          ]
        )
      } else {
        this.$set(
          this.rules,
          'email',
          [
            { required: !!v, message: '请输入邮箱地址', trigger: 'blur' },
            { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
          ]
        )
      }
    },

    // 设置关闭时间
    saveStopTime(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
            merchants.delayDays(this.dialogData).then(res => {
              if (res.data.code === '0000') {
                this.btnLoading = false
                this.addListDialog = false
                this.$refs.dialogData.resetFields()
                this.dialogFormVisible = false
                this.list()
                this.$message({ type: 'success', message: '操作成功' })
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          
        } else {
          return false
        }
      })
    },

        // 提交
      submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogType === 'edit') {
            merchants.resetPws(this.dialogData).then(res => {
              if (res.data.code === '0000') {
                this.btnLoading = false
                this.addListDialog = false
                this.$refs.dialogData.resetFields()
                this.list()
                this.$message({ type: 'success', message: '操作成功' })
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 关闭
    closeDialog(formVisible, formName) {
      this[formVisible] = false
      this.$refs[formName].resetFields()
    },

    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    addMerchant() {
      this.$store.commit('merchants/setCreateMerchantStep', 1)
      // this.$store.commit('merchants/setMearChantsId', null)
      this.$router.push({
        path: '/merchants/actions',
        query: {
          page: 'add'
        }
      })
    },
    toEdit(id) {
      this.$store.commit('merchants/setCreateMerchantStep', 1)
      // this.$store.commit('merchants/setMearChantsId', id)
      this.$router.push({
        path: '/merchants/actions',
        query: {
          page: 'edit',
          id
        }
      })
    },
    viewInfo(id) {
      this.$store.commit('merchants/setCreateMerchantStep', 1)
      this.$router.push({
        path: '/merchants/actions',
        query: {
          page: 'view',
          id
        }
      })
    },
    merDownLoadData() {
      merchants.merDownLoadData({
        ...this.formData
      }, `商户列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card {
  margin-bottom: 20px;
}

.list-table {
  margin: 0 0 20px 0;
  .el-button {
    padding: 0;
  }
}
.auto-width {
  width: 100%;
}
</style>
