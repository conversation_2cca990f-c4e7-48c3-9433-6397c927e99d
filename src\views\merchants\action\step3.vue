<template>
  <el-form ref="step3Form" size="mini" :model="formData" label-width="140px" label-position="right" :rules="rules" :disabled="viewMerchant">
    <div class="tab-box">
      <div class="sub-title">
        <i class="el-icon-document" />
        资质信息</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="up-btn-layout table-top">

            <el-upload
              ref="upload"
              class="file-upload"
              :show-file-list="false"
              action=""
              accept=".pdf,.jpg,.jpeg,.png"
              :http-request="(params => uprequest(params,'1'))"
            >
              <el-button icon="el-icon-plus" :loading="upLoading" :disabled="upLoading">上传合同(必须为pdf文件，且文件名需含对应代征主体名称)</el-button>
            </el-upload>


            <el-upload
              ref="upload"
              class="file-upload"
              :show-file-list="false"
              action=""
              accept=".pdf,.jpg,.jpeg,.png,.zip,.rar"
              :http-request="(params => uprequest(params,'0'))"
            >
              <el-button icon="el-icon-plus" :loading="upLoading" :disabled="upLoading">上传风控资料(.zip/.rar)</el-button>
            </el-upload>



            <el-upload
              ref="upload"
              class="file-upload"
              :show-file-list="false"
              action=""
              accept=".pdf,.jpg,.jpeg,.png"
              :http-request="(params => uprequest(params,'0'))"
            >
              <el-button icon="el-icon-plus" :loading="upLoading" :disabled="upLoading">上传营业执照</el-button>
            </el-upload>
            <el-upload
              ref="upload"
              class="file-upload"
              :show-file-list="false"
              action=""
              accept=".pdf,.jpg,.jpeg,.png"
              :http-request="(params => uprequest(params,'3'))"
            >
              <el-button icon="el-icon-plus" :loading="upLoading" :disabled="upLoading">门头照</el-button>
            </el-upload>
          </div>

          <el-table
            :data="fileList"
            border
            size="mini"
            class="up-list"
            style="width: 100%"
          >
            <el-table-column
              prop="fileName"
              label="文件名称"
            >
              <template slot-scope="scope">
                {{ scope.row.fileName+scope.row.fileExt }}
              </template>
            </el-table-column>
            <el-table-column
              prop="fileSize"
              label="文件大小"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.fileSize > 1024*1024">{{ Math.ceil(scope.row.fileSize / 1024 / 1024) }}MB</div>
                <div v-else>{{ Math.ceil(scope.row.fileSize / 1024) }}KB</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="fileType"
              label="文件状态"
            >
              <template>
                已上传
              </template>
            </el-table-column>
            <el-table-column
              prop="address"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button class="resetbtn" type="text" @click="delFiles(scope.$index)">删除</el-button>
                <a v-if="id" class="el-button el-button--text resetbtn" @click="viewinfo(scope.row.filePath)">查看</a>
                <el-upload
                  ref="upload"
                  size="mini"
                  class="resetUpload"
                  :show-file-list="false"
                  action=""
                  accept=".pdf,.jpg,.jpeg,.png"
                  :http-request="(params => uprequest(params,scope.row.fileType,scope))"
                >
                  <el-button type="text" class="resetbtn">替换</el-button>
                </el-upload>
              </template>
            </el-table-column>
          </el-table>

        </el-col>
      </el-row>
    </div>
  </el-form>
</template>
<script>
import publics from '@/axios/default/public'
import { newAlert } from '@/utils'

export default {
  name: 'Step3',
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    },
    id: {
      default: ''
    },
    rules: {
      type: Object,
      default() {
        return {}
      }
    },
    viewMerchant: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      formData: {},
      upLoading: false,
      upData: {
        file: '',
        fileType: ''
      }
    }
  },
  watch: {
    data(o, n) {
      this.formData = o
      this.$store.commit('merchants/setFileList', this.formData.files || [])
      this.fileList = this.$store.state.merchants.fileList
    }
  },
  mounted() {

  },
  methods: {
    // 上传
    uprequest(param, fileType, scope) {
      const that = this
      const size = param.file.size
      const limitSize = size / 1024 / 1024 < 50
      if (!limitSize) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      that.upLoading = true
      that.upData.file = param.file
      that.upData.fileType = fileType
      this.fileListData(scope)
    },
    delFiles(index) {
      this.fileList = this.$store.state.merchants.fileList
      this.fileList.splice(index, 1)
      this.$store.commit('merchants/setFileList', this.fileList)
    },

    fileListData(scope) {
      const formData = new FormData()
      const data = this.upData
      for (const key in data) {
        formData.append(key, data[key])
      }
      publics.upFiles(formData).then(res => {
        if (res.data.code == '0000') {
          const { fileName, fileSize, fileExt, filePath, fileType } = res.data.data
          if (scope && Object.prototype.toString.call(scope.$index) == '[object Number]') {
            this.fileList = this.$store.state.merchants.fileList
            this.$set(this.fileList, scope.$index, { fileName, fileExt, filePath, fileSize, fileType, levyId: scope.row.levyId })
            this.$store.commit('merchants/setFileList', this.fileList)
          } else {
            this.fileList = this.$store.state.merchants.fileList
            this.fileList.push({
              fileName,
              fileExt,
              filePath,
              fileSize: fileSize,
              fileType: fileType
            })
            this.$store.commit('merchants/setFileList', this.fileList)
          }
          this.upLoading = false
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
          this.upLoading = false
        }
      }).catch(res => {
        this.upLoading = false
      })
    },
    viewinfo(path) {
      if (path) {
        const pattern = /.pdf|.doc/
        const pattern1 = /.zip|.rar/


        const filePath = path
        const ifpdf = pattern.test(filePath)
        const ifrar = pattern1.test(filePath)
        if (ifpdf) {
          // pdf
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = filePath
          a.target = '_blank'
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
        }else if(ifrar){
          window.open(filePath)
        } else {
          // img
          window.open(`action/viewInfo?url=${filePath}`, '_blank')
        }
      } else {
        console.log('没url')
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .up-btn-layout{
    display: flex;
    justify-content: flex-end;
  }
  .sub-title{
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 20px;
    background: #efefef;
    padding: 10px;
    margin-top: 20px;
  }
  .sub-title:first-child{
    margin-top: 0;
  }
  .up-list{
    margin: 20px 0;
  }
  .resetUpload{
    display: inline-block;
    .el-button{
      border: none;
      background: none;
      font-size: 12px;
      /deep/ i {
        display: none!important;
      }
    }
  }
  .resetbtn{
    padding: 5px 10px;
    font-size: 12px;
  }
</style>
