<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间起" class="form-items" prop="createTimeFrom">
                <el-date-picker
                  v-model="formData.createTimeFrom"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间止" class="form-items" prop="createTimeTo">
                <el-date-picker
                  v-model="formData.createTimeTo"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="企业名称" class="form-items">
                <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="商户批次号" class="form-items">
                <el-input v-model="formData.batchNum" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="收款名称" class="form-items">
                <el-input v-model="formData.payeeName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="审核状态" class="form-items">
                <el-select v-model="formData.orderState" placeholder="请选择审核状态" class="auto-width">
                  <el-option label="请选择" value="" />
                  <el-option label="初审中" value="1" />
                  <el-option label="初审拒绝" value="2" />
                  <el-option label="复审中" value="3" />
                  <el-option label="审核通过" value="4" />
                  <el-option label="复审拒绝" value="5" />
                  <el-option label="系统通过" value="6" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="结算类型" class="form-items">
                <el-select v-model="formData.settlementType" placeholder="请选择结算类型" class="auto-width">
                  <el-option label="请选择" value="" />
                  <el-option label="委托代征" value="1" />
                  <el-option label="自然人代发" value="2" />
                  <el-option label="个体户" value="3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="16" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                <el-button icon="el-icon-search" type="primary" @click="downBigOrderDetails">下载文件</el-button>
                <el-button icon="el-icon-search" type="primary" @click="orderUploader">10万以上结算单上传</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="list-card">
        <el-row>
          <el-col :span="24">
            <div class="table-headers">
              <span class="table-header-name">结算总金额：</span>
              <span class="table-header-num">{{tatalSettleMoney || 0 }}元</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="list-card">
        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="payeeName"
            label="姓名"
            show-overflow-tooltip
          />
          <el-table-column
            prop="payeeIdCard"
            label="身份证号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="orderState"
            label="审核状态"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ orderState[scope.row.orderState] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="orderState"
            label="结算类型"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ settlementType[scope.row.settlementType] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="settMoney"
            label="结算金额"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.settMoney }}元
            </template>
          </el-table-column>
          <el-table-column
            prop="batchNum"
            label="商户批次号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="merOrderNum"
            label="商户订单号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="platFree"
            label="服务费"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            label="订单创建时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="orderCompTime"
            label="订单完成时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="address"
            label="操作"
            fixed="right"
            width="260"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="viewInfo(scope.row)">查看订单详情</el-button>
              <el-button type="text" @click="taxDetail(scope.row)">查看/编辑完税证明</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog title="查看订单详情" :visible.sync="dialogOrder" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form label-width="140px" size="mini" class="form-style">
          <el-form-item label="企业名称">
            <el-input v-model="dialogOrderData.merName" disabled />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="dialogOrderData.payeeName" disabled />
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="dialogOrderData.payeeIdCard" disabled />
          </el-form-item>
          <el-form-item label="银行卡号">
            <el-input v-model="dialogOrderData.payeeAcc" disabled />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="dialogOrderData.telephone" disabled />
          </el-form-item>
          <el-form-item label="收款银行">
            <el-input v-model="dialogOrderData.bankName" disabled />
          </el-form-item>
          <el-form-item label="结算金额">
            <el-input v-model="dialogOrderData.settMoney" disabled />
          </el-form-item>
          <el-form-item label="服务费">
            <el-input v-model="dialogOrderData.platFree" disabled />
          </el-form-item>
          <el-form-item label="支付通道">
            <el-input v-model="dialogOrderData.channelName" disabled />
          </el-form-item>
          <el-form-item label="商户批次号">
            <el-input v-model="dialogOrderData.batchNum" disabled />
          </el-form-item>
          <el-form-item label="商户订单号">
            <el-input v-model="dialogOrderData.merOrderNum" disabled />
          </el-form-item>
          <el-form-item label="订单创建时间">
            <el-input v-model="dialogOrderData.createTime" disabled />
          </el-form-item>
          <el-form-item label="订单完成时间">
            <el-input v-model="dialogOrderData.orderCompTime" disabled />
          </el-form-item>
          <el-form-item label="订单备注">
            <el-input v-model="dialogOrderData.remark" type="textarea" :rows="2" disabled />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button @click="dialogOrder = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
<!--    编辑完税证明-->
    <el-dialog title="查看/编辑完税证明" :visible.sync="dialogTaxEdShow" width="30%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form size="mini" class="form-style" label-width="120">
          <div class="table-top table-header">
            <el-upload
              :disabled="taxArr.length == 1 || taxUpLoading"
              ref="upload"
              class="file-upload"
              :show-file-list="false"
              action=""
              accept=".pdf,.jpg,.jpeg,.png"
              :http-request="(params => upTaxRequest(params,'0'))"
            >
              <el-button :disabled="taxArr.length == 1" plain icon="el-icon-plus" size="mini">
                上传完税证明
              </el-button>
            </el-upload>

          </div>
          <el-table
            :data="taxArr"
            border
            size="mini"
            class="list-table"
          >
            <el-table-column
              prop="fileType"
              label="文件状态"
            >
              <template slot-scope="scope">
                已上传
              </template>
            </el-table-column>
            <el-table-column
              prop=""
              label="操作"
            >
              <template slot-scope="scope">
                <el-button type="text">
                  <el-upload
                    ref="upload"
                    class="file-upload resetUpload"
                    :show-file-list="false"
                    action=""
                    :disabled="taxUpLoading"
                    accept=".pdf,.jpg,.jpeg,.png"
                    :http-request="(params => upTaxRequest(params,scope.row.fileType,scope.$index))"
                  >
                    <a class="el-button el-button--text resetbtn">替换</a>
                  </el-upload>
                </el-button>
                <a class="el-button el-button--text resetbtn" target="_blank" :href="encodeURI(scope.row.filePath)">预览</a>
                <el-button type="text" @click="delFiles(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="btnbox">
            <el-button type="primary" size="mini" @click="submitTax">保存</el-button>
            <el-button type="" size="mini" @click="dialogTaxEdShow = false">取消</el-button>
          </div>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import tradeService from '@/axios/default/tradeService'
import { parseTime, newAlert, formatNumber } from '@/utils'
import moment from 'moment'
import publics from '@/axios/default/public'
export default {
  name: 'List',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      tatalSettleMoney: 0,
      total: 0,
      pickTime: '',
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        merName: '',
        payeeName: '',
        batchNum: '',
        orderState: '',
        settlementType: ''
      },
      formLabelWidth: '180px',
      listData: [],
      dialogOrder: false,
      dialogOrderData: {},
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      orderState: { 1: '初审中', 2: '初审拒绝', 3: '复审中', 4: '审核通过', 5: '复审拒绝', 6: '系统通过' },
      settlementType: { 1: '委托代征', 2: '自然人代发', 3: '个体户' },

      settlementId: '', // 结算订单的ID
      dialogTaxEdShow: false, // 是否需要展示上传完税证明
      taxUpLoading: false, // 是否在上传中
      taxArr: [], // 完税文件
    }
  },
  mounted() {
    this.queryBigOrderDetailsByPage()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryBigOrderDetailsByPage()
    },
    queryBigOrderDetailsByPage() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      tradeService.queryBigOrderDetailsByPage({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'bigOrder': {
          'createTimeFrom': this.formData.createTimeFrom,
          'createTimeTo': this.formData.createTimeTo,
          'merName': this.formData.merName,
          'payeeName': this.formData.payeeName,
          'batchNum': this.formData.batchNum,
          'orderState': this.formData.orderState,
          'settlementType': this.formData.settlementType
        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
        this.tatalSettleMoney = formatNumber(res.data.data.tatalSettleMoney)
      })
    },
    viewInfo(data) {
      this.dialogOrder = true
      console.log(data)
      this.dialogOrderData = data
    },
    taxDetail(args) {
      this.settlementId = args.id
      this.taxArr = []
      if (args.taxPaymentCertificate) {
        this.taxArr = [{
          filePath: args.taxPaymentCertificate,
          fileType: '0'
        }]
      }
      this.dialogTaxEdShow = true
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryBigOrderDetailsByPage()
    },
    orderUploader() {
      this.$router.push({
        path: '/balance/ten/upload'
      })
    },
    downBigOrderDetails() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      tradeService.downBigOrderDetails({
        'createTimeFrom': this.formData.createTimeFrom,
        'createTimeTo': this.formData.createTimeTo,
        'merName': this.formData.merName,
        'payeeName': this.formData.payeeName,
        'batchNum': this.formData.batchNum,
        'orderState': this.formData.orderState,
        'settlementType': this.formData.settlementType
      }, `10w+订单${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },

    upTaxRequest(param, fileType, index) {
      this.taxUpLoading = true
      const upEvent = param.file
      const size = upEvent.size
      const limitSize = size / 1024 / 1024 < 50
      if (!limitSize) {
        setTimeout(() => {
          this.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }

      const data = {}
      const formDataPro = new FormData()
      data.file = upEvent
      data.fileType = fileType

      for (const key in data) {
        formDataPro.append(key, data[key])
      }
      publics.upFiles(formDataPro).then(res => {
        if (res.data.code == '0000') {
          const { fileName, fileSize, fileExt, filePath, fileType } = res.data.data
          if (Object.prototype.toString.call(index) == '[object Number]') {
            this.$set(this.taxArr, index, { fileName, fileExt, filePath, fileSize, fileType })
          } else {
            this.taxArr.push({ fileName, fileExt, filePath, fileSize, fileType })
          }
          this.taxUpLoading = false
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
          this.taxUpLoading = false
        }
      }).catch(res => {
        this.taxUpLoading = false
      })
    },

    delFiles(index) {
      this.taxArr.splice(index, 1)
    },

    submitTax() {
      tradeService.addTaxPaymentCertificate({
        id: this.settlementId,
        path: this.taxArr[0].filePath
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
          this.dialogTaxEdShow = false
          this.queryBigOrderDetailsByPage()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .table-headers {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>

