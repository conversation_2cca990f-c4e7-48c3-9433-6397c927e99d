<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="退款时间" class="form-items"> <el-date-picker
                    v-model="pickTime"
                    class="auto-width"
                    type="daterange"
                    align="right"
                    unlink-panels
                    value-format="yyyy-MM-dd"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :picker-options="pickerOptions"
                    @change="resetTime"
                  />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-search" type="primary" @click="refundExcel">批量下载</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <!--              <el-row :gutter="20" style="margin-top: 40px">-->
              <!--                <el-col :span="24">-->
              <!--                  <el-form-item class="form-items" style="text-align:right">-->
              <!--                    <el-button icon="el-icon-download" plain type="primary" @click="refund">退款录入</el-button>-->
              <!--                  </el-form-item>-->
              <!--                </el-col>-->
              <!--              </el-row>-->
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="退款时间"
            width="150"
          />
          <el-table-column
            prop="merchantName"
            width="200"
            label="企业名称"
          />
          <el-table-column
            prop="rechargeAccountName"
            label="打款名称"
            width="200"
          />
          <el-table-column
            prop="tradeOrderNumber"
            label="商户订单号"
            width="200"
          />
          <el-table-column
            prop="refundAmount"
            label="退款金额"
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            width="200"
          />
          <el-table-column
            prop="refundType"
            label="退款类型"
          >
            <template slot-scope="scope">
              {{ refundType[scope.row.refundType] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="refundStatus"
            label="审核状态"
          >
            <template slot-scope="scope">
              {{ refundStatus[scope.row.refundStatus] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="userName"
            label="申请人"
          />
          <el-table-column
            prop="remark"
            label="备注"
            show-overflow-tooltip
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    退款录入-->
    <el-dialog title="退款录入" :visible.sync="dialogBox1" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogBox1Data" :model="dialogBox1Data" size="mini" class="form-style" label-width="100px" :rules="rules">

          <el-form-item label="企业名称" prop="merchantId">
            <el-select
              v-model="dialogBox1Data.merchantId"
              class="auto-width"
              filterable
              remote
              placeholder="请输入关键词"
              :remote-method="mccList"
              :loading="loading"
              @change="getlevyId"
            >
              <el-option v-for="(item,index) in merNameArr" :key="index" :label="item.merName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="18">
              <el-form-item label="商户订单号" prop="tradeOrderNumber">
                <el-input v-model="dialogBox1Data.tradeOrderNumber" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" size="mini" @click="findTradeOrderByOrderNumber">查询</el-button>
            </el-col>
          </el-row>
          <el-form-item label="订单金额">
            {{ orderMoney }}元
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dialogBox1Data.remark" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>

          <el-form-item label="" class="form-items">
            <el-button type="primary" @click="addRefundOrderInfo('dialogBox1Data')">提交审核</el-button>
            <el-button @click="dialogBox1=false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import finance from '@/axios/default/finance'
import Pagination from '@/components/Pagination'
import rechInfo from '@/axios/default/rechInfo'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'List',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        accountName: ''
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogBox1: false,
      dialogBox1Data: this.initDialog(),
      merNameArr: [],
      loading: false,
      orderMoney: 0,
      rules: {
        merchantId: [{ required: true, message: '请填写企业名称', trigger: 'blur' }],
        remark: [{ required: true, message: '请填写备注', trigger: 'blur' }],
        tradeOrderNumber: [{ required: true, message: '请填写订单号', trigger: 'blur' }]
      },
      refundType: { 1: '充值退款', 2: '结算退款' },
      refundStatus: { 1: '待审核', 2: '初审通过', 3: '初审拒绝', 4: '复审拒绝', 5: '完成' }
    }
  },
  mounted() {
    this.list()
    this.initAddRechInfo({ queryType: '1', merName: '' }, 'merNameArr')
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      finance.refundOrderInfoList({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    refundExcel() {
      finance.refundExcel({
        ...this.formData
      }, `退款列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {
        // this.$message.success('操作成功')
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    refund() {
      this.dialogBox1 = true
      this.orderMoney = 0
      this.dialogBox1Data = this.initDialog()
    },
    initDialog() {
      return {
        merchantId: '',
        merchantName: '',
        tradeOrderNumber: '',
        remark: ''
      }
    },
    // 添加退款
    addRefundOrderInfo(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const merName = this.merNameArr.find(res => res.id == this.dialogBox1Data.merchantId)
          this.dialogBox1Data.merchantName = merName.merName
          finance.addRefundOrderInfo({
            ...this.dialogBox1Data
          }).then(res => {
            if (res.data.code == '0000') {
              this.dialogBox1 = false
              this.$message({ type: 'success', message: '操作成功' })
              this.list()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 查询充值
    // 企业: "queryType":"1",
    // 主体: "queryType":"2","merId":"1"
    // 渠道: "queryType":"3","levyId":"1"
    // 充值账号: "queryType":"4","merId":"1"，"channelId":"1"
    initAddRechInfo(data, item) {
      rechInfo.initAddRechInfo(data).then(res => {
        if (res.data.code === '0000') {
          this.loading = false
          this[item] = res.data.data
        }
      })
    },
    getlevyId() {
      this.initAddRechInfo({ queryType: '2', merId: this.dialogBox1Data.merchantId }, 'levyIdArr')
    },
    mccList(query) {
      if (query !== '') {
        this.loading = true
        this.initAddRechInfo({ queryType: '1', merName: query }, 'merNameArr')
      } else {
        this.merNameArr = []
      }
    },
    findTradeOrderByOrderNumber() {
      if (this.dialogBox1Data.merchantId) {
        finance.findTradeOrderByOrderNumber({
          merchantId: this.dialogBox1Data.merchantId,
          tradeOrderNumber: this.dialogBox1Data.tradeOrderNumber
        }).then(res => {
          if (res.data.code == '0000') {
            const { data } = res.data
            this.orderMoney = data.canRefundMoney
            // this.$message({type:'success',message:'操作成功'})
            // this.dialogBox1 = false
            // this.list()
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      } else {
        newAlert(this.$tips, '请先选择企业名称')
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 70vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
