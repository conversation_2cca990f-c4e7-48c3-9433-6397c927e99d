<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值时间起" class="form-items" prop="createTimeFrom">
                    <el-date-picker
                      v-model="formData.createTimeFrom"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值时间止" class="form-items" prop="createTimeTo">
                    <el-date-picker
                      v-model="formData.createTimeTo"
                      class="auto-width"
                      type="datetime"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :clearable="false"
                      :editable="false"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值完成时间起" class="form-items" prop="completeTimeStart">
                    <el-date-picker
                      v-model="formData.completeTimeStart"
                      class="auto-width"
                      type="datetime"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值完成时间止" class="form-items" prop="completeTimeEnd">
                    <el-date-picker
                      v-model="formData.completeTimeEnd"
                      class="auto-width"
                      type="datetime"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="商户编号" class="form-items">
                    <el-input v-model="formData.merNo" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item v-if="this.flag ==true" label="销售名称" class="form-items">
                    <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item v-if="this.flag ==true" label="代理名称" class="form-items">
                    <el-input v-model="formData.agentName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="项目负责人" class="form-items">
                    <el-input v-model="formData.projectName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="打款名称" class="form-items">
                    <el-input v-model="formData.rechName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="收款名称" class="form-items">
                    <el-input v-model="formData.levyName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="审核状态" class="form-items">
                    <el-select v-model="formData.auditState" placeholder="请选择交易状态" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option label="初审中" value="1" />
                      <el-option label="初审拒绝" value="2" />
                      <el-option label="复审中" value="3" />
                      <el-option label="审核通过" value="4" />
                      <el-option label="复审拒绝" value="5" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="充值类型" class="form-items">
                    <el-select v-model="formData.rechType" placeholder="请选择充值类型" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option label="线上充值" value="1" />
                      <el-option label="手动录入" value="2" />
                      <el-option label="异常充值提交" value="3" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="充值金额起" class="form-items" prop="startSettMoney">
                    <el-input v-model.number="formData.startSettMoney" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="充值金额止" class="form-items" prop="endSettMoney">
                    <el-input v-model.number="formData.endSettMoney" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width" @change="queryChannelInfoByLevyId">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="通道名称" class="form-items" prop="channelId">
                    <el-select v-model="formData.channelId" placeholder="请选择" class="auto-width">
                      <el-option v-if="channelArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in channelArr"
                        :key="item.id"
                        :label="item.channelName"
                        :value="item.id"
                      />
                    </el-select>
                    <!--                    <el-input v-model.number="formData.channelName" placeholder="请输入内容" clearable />-->
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="订单号" class="form-items" prop="rechOrderNum">
                    <el-input v-model="formData.rechOrderNum" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>

             

                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-download" plain type="primary" @click="downRechInfo">下载文件</el-button>
                    <el-button icon="el-icon-plus" plain type="primary" @click="openBox">充值录入</el-button>
                    <el-upload
                      ref="uploadImport"
                      class="upload-more"
                      action=""
                      :http-request="(params => uploadImport(params))"
                      :show-file-list="false"
                      accept=".zip"
                      :disabled="uploadLoading"
                    >
                      <el-button icon="el-icon-upload" plain type="primary" :loading="uploadLoading">充值电子回单上传</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="list-card">
        <el-row class="table-head-money">
          <el-col :span="12">
            <div class="table-headers">
              <span class="table-header-num">{{ rechData.totalRechMoney || 0 }}</span>
              <span class="table-header-name">总充值金额(元)</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="table-headers">
              <span class="table-header-num">{{ rechData.rechSuccMoney || 0 }}</span>
              <span class="table-header-name">充值成功金额(元)</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="list-card">
        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merNo"
            label="商户编号"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
            prop="merName"
            label="企业名称"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
            prop="rechOrderNum"
            label="订单号"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
            prop="channelName"
            label="通道名称"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
            prop="levyName"
            label="代征主体"
            width="200"
            show-overflow-tooltip
          />

          <el-table-column
          v-if="this.flag ==true"
            prop="saleName"
            label="销售名称"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
          v-if="this.flag ==true"
            prop="agentName"
            label="代理名称"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="projectName"
            label="项目负责人"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="rechName"
            label="打款名称"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="rechBankNo"
            label="打款账号"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="rechMoney"
            label="打款金额"
            width="200"
            show-overflow-tooltip
          />
          <!--          <el-table-column-->
          <!--            prop="channelName"-->
          <!--            label="打款渠道"-->
          <!--            show-overflow-tooltip-->
          <!--          />-->
          <el-table-column
            prop="levyName"
            width="250"
            label="收款名称"
          />
          <el-table-column
            prop="receiveBank"
            label="收款银行"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="receiveBankNo"
            label="收款账号"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="rechState"
            label="充值状态"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ rechState[scope.row.rechState] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="充值创建时间"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="rechAccountTime"
            label="充值完成时间"
            width="180"
            show-overflow-tooltip
          />

          <el-table-column
            label="审核状态"
            prop="auditState"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ auditState[scope.row.auditState] }}
            </template>
          </el-table-column>

          <el-table-column
            prop="rechType"
            label="充值类型"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ rechType[scope.row.rechType] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            label="备注"
            width="220"
            show-overflow-tooltip
          />
          <el-table-column
            width="150"
            fixed="right"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button v-if="scope.row.rechReceiptUrl" type="text">
                <a target="_blank" :href="scope.row.rechReceiptUrl">查看充值电子回单</a>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    充值录入-->
    <el-dialog title="充值录入" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称" prop="merName">
            <el-select
              v-model="dialogData.merName"
              class="auto-width"
              filterable
              remote
              placeholder="请输入关键词"
              :remote-method="mccList"
              :loading="loading"
              @change="getlevyId"
            >
              <el-option v-for="(item,index) in merNameArr" :key="index" :label="item.merName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="代征主体" prop="levyId">
            <el-select v-model="dialogData.levyId" placeholder="请选择代征主体" class="auto-width" @change="getChannel">
              <el-option v-for="(item,index) in levyIdArr" :key="item.id" :label="item.NAME" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="充值通道" prop="channelId">
            <el-select v-model="dialogData.channelId" placeholder="请选择充值通道" class="auto-width" @change="getMoney">
              <el-option v-for="(item,index) in channelIdArr" :key="index" :label="item.channel_name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="充值账号">
            <el-input v-model="rechBankNo.accountNo" autocomplete="off" class="auto-width" disabled />
          </el-form-item>
          <el-form-item label="充值金额" prop="rechMoney">
            <el-input v-model="dialogData.rechMoney" type="text" autocomplete="off" class="auto-width" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dialogData.remark" type="text" placeholder="请输入备注信息" autocomplete="off" class="auto-width" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" :loading="btnLoading" @click="submit('dialogData')">提交审核</el-button>
              <el-button :loading="btnLoading" @click="dialogFormVisible=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import rechInfo from '@/axios/default/rechInfo'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import publicApi from '@/axios/default/public'
import moment from 'moment'
export default {
  name: 'Index',
  components: {
    Pagination
  },

  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        completeTimeStart: null,
        completeTimeEnd: null,
        merName: '',
        rechName: '',
        levyName: '',
        auditState: '',
        saleName: '',
        agentName: '',
        projectName: '',
        startSettMoney: '',
        endSettMoney: '',
        levyId: '',
        channelName: '',
        merNo: '',
        channelId: '',
        rechOrderNum: ''
      },
      uploadLoading: false,
      flag:true,
      levyBodyIdArr: [],
      channelArr: [],
      dialogData: this.initDialogData(),
      loading: false,
      rechBankNo: {
        accountNo: ''
      },
      rechData: {
        totalRechMoney: 0,
        rechSuccMoney: 0
      },
      dialogFormVisible: false,
      formLabelWidth: '180px',
      listData: [],
      merNameArr: [],
      levyIdArr: [],
      channelIdArr: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      rechState: { '1': '处理中', '2': '已充值', '3': '已退款' },
      auditState: { '1': '初审中', '2': '初审拒绝', '3': '复审中', '4': '审核通过', '5': '复审拒绝' },
      rechType: { 1: '线上充值', 2: '手动录入', 3: '异常充值' },
      rules: {
        merName: { required: true, message: '请选择企业名称', trigger: 'change' },
        levyId: { required: true, message: '请选择代征主体', trigger: 'change' },
        channelId: { required: true, message: '请选择充值通道', trigger: 'change' },
        rechMoney: [{ required: true, message: '请填写充值金额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^-?[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }]
      },
      btnLoading: false
    }
  },
  mounted() {

    this.queryLevyBodyInfos()
    this.queryRechInfosByPage()
    this.initAddRechInfo({ queryType: '1', merName: '' }, 'merNameArr')
  },
  activated() {
    if (this.$route.query.pageType) {
      setTimeout(()=>{
        this.formData.merName = this.$route.query.pageType == 1 ? this.$route.query.name : '';
        this.formData.saleName = this.$route.query.pageType == 2 ? this.$route.query.name : '';
        this.formData.createTimeFrom = this.$route.query.startTime;
        this.formData.createTimeTo = this.$route.query.endTime;
        this.queryRechInfosByPage()
      },10)
    }
    const conditions = JSON.stringify(this.$route.query)
    if (conditions != '{}') {
      this.getLinkData()
    }
  },
  // beforeRouteEnter (to, from, next) {
  //   // 如果去详情页，就缓存 列表页面数据
  //   if (from.path == '/profits/detail') {
  //     next(vm => {
  //       vm.getLinkData()
  //     });
  //   }else{
  //     next()
  //   }
  // },
  methods: {
    moment,
    getLinkData() {
      const data = this.$route.query
      this.formData.levyId = Number(data.levyId) || ''
      this.formData.merName = data.merName
      this.formData.createTimeFrom = data.createTimeFrom
      this.formData.createTimeTo = data.createTimeTo
      this.queryRechInfosByPage()
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          this.insertRechInfo()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    downRechInfo() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      this.formData.completeTimeStart = this.formData.completeTimeStart ? moment(this.formData.completeTimeStart).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.completeTimeEnd = this.formData.completeTimeEnd ? moment(this.formData.completeTimeEnd).format('YYYY-MM-DD HH:mm:ss') : ''
      rechInfo.downRechInfo({
        ...this.formData
        // 'createTimeFrom': this.formData.createTimeFrom,
        // 'createTimeTo': this.formData.createTimeTo,
        // 'merName': this.formData.merName,
        // 'rechName': this.formData.rechName,
        // 'receiveName': this.formData.receiveName,
        // 'auditState': this.formData.auditState, // 1:初审,3:复审, 为空时,充值列表
        // saleName:'',
        // agentName:'',
        // projectName:'',

      }, `充值列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },
    insertRechInfo() {
      rechInfo.checkRechRecord({
        'merId': this.dialogData.merName,
        'rechMoney': this.dialogData.rechMoney
      }).then(res => {
        if (res.data.code === '0000') {
          this.checkNext()
        } else {
          this.$confirm(`该金额${res.data.data.createTime}已充值过一笔，是否继续?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.checkNext()
          }).catch(() => {
            this.btnLoading = false
          })
        }
      })
    },
    checkNext() {
      const merName = this.merNameArr.find(res => res.id == this.dialogData.merName)
      const channelName = this.channelIdArr.find(res => res.id == this.dialogData.channelId)
      const levyName = this.levyIdArr.find(res => res.id == this.dialogData.levyId)
      rechInfo.insertRechInfo({
        'merId': this.dialogData.merName,
        'levyId': this.dialogData.levyId,
        'levyName': levyName.NAME,
        'channelId': this.dialogData.channelId,
        'rechBankNo': this.rechBankNo.accountNo,
        merName: merName.merName,
        channelName: channelName.channel_name,
        'rechMoney': this.dialogData.rechMoney,
        'remark': this.dialogData.remark
      }).then(res => {
        this.btnLoading = false
        if (res.data.code === '0000') {
          this.dialogFormVisible = false
          this.$message({ type: 'success', message: '提交成功' })
          this.resetForm('dialogData')
          this.queryRechInfosByPage()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initDialogData() {
      return {
        merName: '',
        levyId: '',
        channelId: '',
        rechMoney: '',
        remark: ''
      }
    },
    openBox() {
      this.dialogFormVisible = true
      this.$nextTick(function() {
        this.$refs['dialogData'].clearValidate()
        this.$refs['dialogData'].resetFields()
        this.rechBankNo.accountNo = ''
      })
      // this.dialogData = this.initDialogData()
    },
    onSearch() {
      this.pageNum = 1
      this.queryRechInfosByPage()
    },
    // 充值,初审,复审列表
    queryRechInfosByPage() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      this.formData.completeTimeStart = this.formData.completeTimeStart ? moment(this.formData.completeTimeStart).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.completeTimeEnd = this.formData.completeTimeEnd ? moment(this.formData.completeTimeEnd).format('YYYY-MM-DD HH:mm:ss') : ''
      rechInfo.queryRechInfosByPage({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'rechInfo': {
          ...this.formData
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
          this.rechData.totalRechMoney = res.data.data.totalRechMoney
          this.rechData.rechSuccMoney = res.data.data.rechSuccMoney
        }
      })
    },
    // 查询充值
    // 企业: "queryType":"1",
    // 主体: "queryType":"2","merId":"1"
    // 渠道: "queryType":"3","levyId":"1"
    // 充值账号: "queryType":"4","merId":"1"，"channelId":"1"
    initAddRechInfo(data, item) {
      rechInfo.initAddRechInfo(data).then(res => {
        if (res.data.code === '0000') {
          this.loading = false
          this[item] = res.data.data
          if (this[item].length === 1) {
            if (item == 'levyIdArr') {
              this.dialogData.levyId = this[item][0].id
              this.getChannel(this.formData.levyId)
            } else if (item == 'channelIdArr') {
              this.dialogData.channelId = this[item][0].id
              this.getMoney(this.formData.levyId)
            }
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    getlevyId() {
      this.dialogData.levyId = ''
      this.dialogData.channelId = ''
      this.rechBankNo.accountNo = ''
      this.$nextTick(function() {
        this.$refs.dialogData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '2', merId: this.dialogData.merName }, 'levyIdArr')
    },
    mccList(query) {
      if (query !== '') {
        this.loading = true
        this.initAddRechInfo({ queryType: '1', merName: query }, 'merNameArr')
      } else {
        this.merNameArr = []
      }
    },
    getChannel() {
      this.dialogData.channelId = ''
      this.rechBankNo.accountNo = ''
      this.$nextTick(function() {
        this.$refs.dialogData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '3', merId: this.dialogData.merName, levyId: this.dialogData.levyId }, 'channelIdArr')
    },
    getMoney() {
      this.rechBankNo.accountNo = ''
      this.$nextTick(function() {
        this.$refs.dialogData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '4', merId: this.dialogData.merName, channelId: this.dialogData.channelId }, 'rechBankNo')
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryRechInfosByPage()
    },
    resetForm(formName) {
      // this.pickTime = this.initSearchTime()
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
            this.flag =false
            this.queryChannelInfoByLevyId(this.formData.levyId)
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    queryChannelInfoByLevyId(levyBodyId) {
      this.formData.channelId = ''
      publicApi.queryChannelInfoByLevyId({ levyBodyId }).then(res => {
        if (res.data.code === '0000') {
          this.channelArr = res.data.data
          if (this.channelArr.length === 1) {
            this.formData.channelId = this.channelArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    uploadImport(param) {
      const that = this
      const size = param.file.size
      const isLtM = size / 1024 / 1024 < 50
      if (!isLtM) {
        setTimeout(function() {
          // that.$message.error('上传大小不能超过 15MB!')
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      this.uploadLoading = true
      const formData = new FormData()
      formData.append('file', param.file)
      rechInfo.rechInfoImport(formData).then(res => {
        this.uploadLoading = false
        if (res.data.code === '0000') {
          this.$message.success('操作成功')
          this.queryRechInfosByPage()
        } else {
          this.$message.error(res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .pagination{
    text-align: right;
  }

  .table-head-money .el-col .table-headers{
    display: flex;
    flex-flow: nowrap column;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #eff2f6;
    .table-header-num{
      font-size: 18px;
      margin-bottom: 5px;
      color: #333;
    }
    .table-header-name{
      font-size: 12px;
      color: #999;
    }
  }
  .table-head-money .el-col:last-child .table-headers{
    border-right: none;
    .table-header-num{
      color: #F54343;
    }
  }
  .upload-more{
    display: inline-block;
  }
</style>
