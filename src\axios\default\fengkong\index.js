// 代理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  queryMerTradeRatioByPage: `${API_Header}/merTradeRatio/queryMerTradeRatioByPage`, // 风控列表
  queryRiskOrderDetailsByPage: `${API_Header}/tradeService/queryRiskOrderDetailsByPage`, // 自由职业者预警列表
  queryFreeTradeWarnByPage: `${API_Header}/freeTradeWarn/queryFreeTradeWarnByPage` // 自由职业者预警列表
}

const fengkong = {
  merList: params => {
    return request.postJson(api.queryMerTradeRatioByPage, params)
  },
  freeList: params => {
    return request.postJson(api.queryRiskOrderDetailsByPage, params)
  },
  freeWarnList: params => {
    return request.postJson(api.queryFreeTradeWarnByPage, params)
  }
}

export default fengkong

