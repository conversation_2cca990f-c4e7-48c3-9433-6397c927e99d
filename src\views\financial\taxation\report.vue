<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="统计开始时间" class="form-items" prop="startCreateTime">
                <el-date-picker
                  v-model="formData.startCreateTime"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="统计结束时间" class="form-items" prop="endCreateTime">
                <el-date-picker
                  v-model="formData.endCreateTime"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="商户编号" class="form-items">
                <el-input v-model="formData.merchantNo" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="商户名称" class="form-items">
                <el-input v-model="formData.merchantName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="所属销售名称" class="form-items">
                <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="所属代理商名称" class="form-items">
                <el-input v-model="formData.agentName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="代征主体" class="form-items">

                <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                  <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                  <el-option
                    v-for="item in levyBodyIdArr"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>

              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 40px">
            <el-col :span="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="exportList">下载财务月度报表</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantNo"
            width="200"
            label="商户编号"
          />
          <el-table-column
            prop="merchantName"
            width="250"
            label="商户名称"
          />
          <el-table-column
            prop="saleName"
            width="100"
            label="销售名称"
          />
          <el-table-column
            prop="agentName"
            width="200"
            label="代理商名称"
          />
          <el-table-column
            prop="levyName"
            width="200"
            label="代征主体"
          />
          <el-table-column
            prop="channelName"
            width="200"
            label="通道名称"
          />
          <el-table-column
            prop="feeRateType"
            width="100"
            label="扣除方式"
          >
            <template slot-scope="scope">
              {{ scope.row.feeRateType == '3' ? '外扣' :'内扣' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="feeRate"
            width="100"
            label="费率"
          />
          <el-table-column
            prop="rechToday"
            label="今日充值金额"
            width="150"
          />
          <el-table-column
            prop="rechMonth"
            width="150"
            label="本月充值金额"
          />
          <el-table-column
            prop="rechYear"
            width="150"
            label="累计充值金额"
          />
          <el-table-column
            prop="settMoneyToday"
            width="150"
            label="今日结算金额"
          />
          <el-table-column
            prop="settMoneyMonth"
            width="150"
            label="本月结算金额"
          />
          <el-table-column
            prop="settMoneyYear"
            width="150"
            label="累计结算金额"
            show-overflow-tooltip
          />
          <el-table-column
            prop="platFeeToday"
            width="150"
            label="今日服务费"
            show-overflow-tooltip
          />
          <el-table-column
            prop="platFeeMonth"
            width="150"
            label="本月服务费"
            show-overflow-tooltip
          />
          <el-table-column
            prop="platFeeYear"
            width="150"
            label="累计服务费"
            show-overflow-tooltip
          />
          <el-table-column
            prop="accountBalance"
            width="150"
            label="账户余额"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            width="150"
            label="创建时间"
            show-overflow-tooltip
          />
          <el-table-column
            prop="openTime"
            width="150"
            label="商户开通时间"
            show-overflow-tooltip
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import financial from '@/axios/default/financial'
import Pagination from '@/components/Pagination'
import publicApi from '@/axios/default/public'
import { newAlert } from '@/utils'
import moment from 'moment'

export default {
  name: 'Report',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        startCreateTime: new Date(moment(new Date()).startOf('day')),
        endCreateTime: new Date(moment(new Date()).endOf('day')),
        merchantNo: '',
        merchantName: '',
        saleName: '',
        agentName: '',
        levyId: ''
      },
      listData: [],
      levyBodyIdArr: []
    }
  },

  mounted() {
    this.queryLevyBodyInfos()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      this.formData.startCreateTime = moment(this.formData.startCreateTime).format('YYYY-MM-DD HH:mm:ss')
      this.formData.endCreateTime = moment(this.formData.endCreateTime).format('YYYY-MM-DD HH:mm:ss')
      financial.finList({
        ...this.formData,
        request: {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },
    exportList() {
      this.formData.startCreateTime = moment(this.formData.startCreateTime).format('YYYY-MM-DD HH:mm:ss')
      this.formData.endCreateTime = moment(this.formData.endCreateTime).format('YYYY-MM-DD HH:mm:ss')
      financial.finExcel({
        ...this.formData
      }, '财务月度报表.xls', '.xls').then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
        }
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
