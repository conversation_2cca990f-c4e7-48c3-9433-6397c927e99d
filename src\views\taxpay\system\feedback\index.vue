<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="samll">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="反馈时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="反馈状态" class="form-items" prop="signingStatus">
                    <el-select v-model="formData.status" placeholder="请选择" class="auto-width">
                      <el-option label="全部" value=""/>
                      <el-option label="未回复" :value="1"/>
                      <el-option label="已回复" :value="2"/>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>



                <el-col :lg="24" :xs="24" style="text-align: right;">
                  <el-form-item class="form-items">
                    <el-button icon="el-icon-search" type="primary" :disabled="requestLoading" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>


              </el-row>

            </el-form>
          </el-col>
        </el-row>

        <el-table :data="tableData" border stripe size="samll" class="list-table">
          <el-table-column v-for="(item,index) in listHeader" :key="index" :width="item.width" :fixed="item.fixed" :label="item.name">
            <template slot-scope="scope">
              <div v-if="item.key == 'action'">
                <el-button type="text" size="samll" class="btn-edit"  @click="toEdit(scope.row.id)" v-if="scope.row.status == '1'">回复</el-button>

                <el-button type="text" size="samll" class="btn-edit" @click="view(scope.row.id)" v-if="scope.row.status == '2'">查看</el-button>

                <!-- <template>
                  <el-popconfirm title="确定删除吗？" @onConfirm="deleteData(scope.row.id,scope.row.deleteFlag=='1')">
                    <el-button slot="reference" v-hasPermission="['merchants:list-del']" type="text" size="mini" class="btn-del">删除</el-button>
                  </el-popconfirm>
                </template> -->
              </div>
              <div v-else>
                <span v-if="item.key == 'status'">
                  {{ liststatus[scope.row[item.key]] }}
                </span>
                <span v-else>{{ scope.row[item.key] }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>


        <!--    新增意见反馈-->
        <el-dialog :title= "title" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" label-width="110px" size="mini" class="form-style" :rules="rules">

          <el-form-item label="意见反馈" prop="message">
            <el-input v-model="dialogData.message" type="textarea" disabled  placeholder="请输入您的意见反馈" autocomplete="off" class="auto-width" />
          </el-form-item> 
          
          <el-form-item label="回复内容" prop="reply">
            <el-input v-model="dialogData.reply" type="textarea" :disabled="this.dialogType == 'view'"  placeholder="请输入回复意见" autocomplete="off" class="auto-width" />
          </el-form-item> 


          <el-form-item v-if="this.dialogType !=='view'">
            <div style="text-align: right">
              <el-button type="primary" :loading="btnLoading" @click="submit()">保存</el-button>
              <el-button :loading="btnLoading" @click="dialogFormVisible=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import merchants from '@/axios/default/merchants'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'MerchantsList',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: this.initSearchTime(),
      liststatus: { 0: '编辑', 1: '未回复', 2: '已回复', 3: '正常', 4: '预开', 5: '停用', 6: '渠道审批中', 7: '风控审批中', 8: '运营审批中', 9: '观察者', 10: '已拉黑', 11: '已加白' },
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        status: '',
        merchantName:''      
      },
      title:'',
      listHeader: [
      {
        name: '公司名称',
        width: '500',
        key: 'merchantName'
      },
      
       {
        name: '反馈内容',
        width: '500',
        key: 'message'
      },
      {
        name: '回复内容',
        width: '500',
        key: 'reply'
      },
      {
        name: '反馈状态',
        width: '200',
        key: 'status'
      },
      {
        name: '反馈时间',
        width: '200',
        key: 'createTime'
      },
       {
        name: '操作',
        key: 'action',
        width: '200',
        fixed: 'right'
      }],
      tableData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },

      dialogData: {
        // merName: '', // 企业名称
        // groupMerName: '', // 集团企业名称
        // sameCompany: '' //是否同一公司
        message:''
      },
      merIdArr: [],
      merIdArr1: [],
      dialogFormVisible: false,
      addListDialog: false,
      btnLoading: false,
      loading: false,
      requestLoading: false,
      dialogType: '',
      dialogName: '',
      rules: {
        message: { required: true, message: '请填写意见反馈', trigger: 'change' }
      }
    }
  },
  mounted() {
  this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      this.formData.createTimeFrom = moment(this.pickTime[0]).format('YYYY-MM-DD')
      this.formData.createTimeTo = moment(this.pickTime[1]).format('YYYY-MM-DD')
      this.requestLoading = true
      merchants.queryMerchantMessageByPage({
        ...this.formData,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.requestLoading = false
          this.tableData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    deleteData(id,deleteFlag) {
      merchants.updateMerchantInfoGroup({ id,deleteFlag }).then(res => {
        if (res.data.code == '0000') {
          const index = this.tableData.findIndex(res => res.id == id)
          this.tableData.splice(index, 1)
          this.$message.success('操作成功')
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initSearchTime() {
        return [new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1), new Date()]
      },


    openBox() {
      this.title = '新增意见反馈'
      this.dialogType = 'add'
      this.dialogFormVisible = true
      this.dialogData = this.initDialogData()
    },
    // 开通禁用
    // updateStatus(data) {
    //   const { row, status } = data
    //   merchants.updateStatus({
    //     id: row.id,
    //     status
    //   }).then(res => {
    //     if (res.data.code == '0000') {
    //       this.$message.success('操作成功')
    //       this.list()
    //     } else {
    //       newAlert(this.$tips, res.data.message)
    //     }
    //   })
    // },


    // 提交
    submit() {
      console.log(this.dialogData)
          this.btnLoading = true
          if (this.dialogType === 'add') {
            merchants.addMerchantMessage(this.dialogData).then(res => {
              if (res.data.code === '0000') {
                this.btnLoading = false
                this.addListDialog = false
                this.$refs.dialogData.resetFields()
                this.dialogFormVisible = false
                this.list()
                this.$message({ type: 'success', message: '操作成功' })
              } else {
                this.btnLoading = false
                newAlert(this.$tips, res.data.message)
              }
            })
          } else {
            this.dialogData.status = '2'
            merchants.updateMerchantMessage({
              ...this.dialogData
            }).then(res => {
                if (res.data.code == '0000') {
                this.dialogFormVisible = false
                this.$refs.dialogData.resetFields()

                 this.btnLoading = false
                 this.list()
                 this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
          }


    },
    // 关闭
    closeDialog(formVisible, formName) {
      this[formVisible] = false
      this.$refs[formName].resetFields()
    },

    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    addMerchant() {

    },
    toEdit(id) {
      this.title = '回复意见反馈'
      this.dialogType = 'edit'
      this.dialogFormVisible = true
      //this.dialogData = this.initDialogData()
      merchants.queryMerchantMessageById({
        id
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogData = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })

    },
    view(id) {
      this.title = '查看意见反馈'
      this.dialogType = 'view'
      this.dialogFormVisible = true
      //this.dialogData = this.initDialogData()
      merchants.queryMerchantMessageById({
        id
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogData = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })

    },
    initDialogData(){
      return{
        // merName: '', // 企业名称
        // groupMerName: '', // 集团企业名称
        // sameCompany: '' //是否同一公司
        message:''
      }
    },
    mccList(query) {
      if (query !== '') {
        this.loading = true
        publics.merAllList({
          merName: query
        }).then(res => {
          this.loading = false
          this.merIdArr = res.data.data
        })
      } else {
        this.merIdArr = []
      }
    },
    mccList1(query) {
      if (query !== '') {
        this.loading = true
        publics.merAllList({
          merName: query
        }).then(res => {
          this.loading = false
          this.merIdArr1 = res.data.data
        })
      } else {
        this.merIdArr1 = []
      }
    },
    merDownLoadData() {
      merchants.merDownLoadData({
        ...this.formData
      }, `商户列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card {
  margin-bottom: 20px;
}

.list-table {
  margin: 0 0 20px 0;
  .el-button {
    padding: 0;
  }
}
.auto-width {
  width: 100%;
}
</style>
