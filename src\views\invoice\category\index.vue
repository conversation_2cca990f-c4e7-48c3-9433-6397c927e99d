<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyName" placeholder="请输入内容" class="form-items">
                      <el-option v-if="levyArr.length>1" label="请选择" value="" />
                      <el-option v-for="(item,index) in levyArr" :key="index" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="一级类目" class="form-items">
                    <el-input v-model="formData.typeName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="二级类目" class="form-items">
                    <el-input v-model="formData.typeName2" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="4" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-download" plain type="primary" @click="openAddOne">新增一级类目</el-button>
                    <el-button icon="el-icon-download" plain type="primary" @click="openAddTwo">新增二级类目</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="创建时间"
          />
          <el-table-column
            prop="levyName"
            label="代征主体"
          />
          <el-table-column
            prop="typeName1"
            label="一级类目"
          />
          <el-table-column
            prop="typeName2"
            label="二级类目"
          />
          <el-table-column
            prop="address"
            label="操作"
          >
            <template slot-scope="scope">
              <el-popconfirm title="确定删除吗？" @onConfirm="delData(scope.row.id)">
                <el-button slot="reference" type="text">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    新增一级类目-->
    <el-dialog title="新增一级类目" :visible.sync="dialogAddOne" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogAddOneData" :model="dialogAddOneData" label-width="110px" size="mini" class="form-style" :rules="rulesA">
          <el-form-item label="代征主体" prop="levySub">
            <el-select v-model="dialogAddOneData.levySub" placeholder="请选择活动区域" class="form-items">
              <el-option v-for="(item,index) in levyArr" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="一级类目" prop="typeName">
            <el-input v-model="dialogAddOneData.typeName" autocomplete="off" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogAddOneData','1')">保存</el-button>
              <el-button :loading="btnLoading" @click="dialogAddOne=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

    <!--    新增二级类目-->
    <el-dialog title="新增二级类目" :visible.sync="dialogAddTwo" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogAddTwoData" :model="dialogAddTwoData" label-width="110px" size="mini" class="form-style" :rules="rulesB">
          <el-form-item label="代征主体" prop="levySub">
            <el-select v-model="dialogAddTwoData.levySub" placeholder="请选择活动区域" class="form-items" @change="queryInvoiceTypes">
              <el-option v-for="(item,index) in levyArr" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="一级类目" prop="parentId">
            <el-select v-model="dialogAddTwoData.parentId" filterable placeholder="请选择活动区域" class="form-items">
              <el-option v-for="(item,index) in typeNameOneArr" :key="index" :label="item.typeName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="二级类目" prop="typeName">
            <el-input v-model="dialogAddTwoData.typeName" autocomplete="off" placeholder="请输入二级类目"/>
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogAddTwoData','2')">保存</el-button>
              <el-button :loading="btnLoading" @click="dialogAddTwo=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import invoiceType from '@/axios/default/invoiceType'
import levyBody from '@/axios/default/levyBody'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        levyName: '',
        typeName: '',
        typeName2: ''
      },
      formLabelWidth: '180px',
      listData: [],
      dialogAddOne: false,
      dialogAddOneData: {
        'levySub': '',
        'levyName': '',
        'typeName': '',
        'parentId': '0'
      },
      dialogAddTwo: false,
      dialogAddTwoData: {
        'levySub': '',
        'levyName': '',
        'typeName': '',
        'parentId': ''
      },
      levyArr: [],
      typeNameOneArr: [],
      rulesA: {
        levySub: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        typeName: [{ required: true, message: '请填写一级类目', trigger: 'blur' }]
      },
      rulesB: {
        levySub: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        parentId: [{ required: true, message: '请选择一级类目', trigger: 'change' }],
        typeName: [{ required: true, message: '请填写二级类目', trigger: 'blur' }]
      },
      btnLoading: false
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryInvoiceTypesByPage()
    },
    // 列表
    queryInvoiceTypesByPage() {
      invoiceType.queryInvoiceTypesByPage({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'invoiceType': {
          'levyName': this.formData.levyName,
          'typeName': this.formData.typeName,
          'typeName2': this.formData.typeName2
        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
      })
    },
    openAddOne() {
      this.dialogAddOne = true
      this.$nextTick(function() {
        this.$refs.dialogAddOneData.clearValidate()
      })
      // this.dialogAddOneData = {}
    },
    openAddTwo() {
      this.dialogAddTwo = true
      this.$nextTick(function() {
        this.$refs.dialogAddTwoData.clearValidate()
      })
      // this.dialogAddTwoData = {}
    },
    // 提交
    submit(formName, type) {
      let data
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (type == '1') {
            this.dialogAddOneData.levyName = this.levyArr.find(res => res.id == this.dialogAddOneData.levySub).name
            data = this.dialogAddOneData
          } else {
            this.dialogAddTwoData.levyName = this.levyArr.find(res => res.id == this.dialogAddTwoData.levySub).name
            data = this.dialogAddTwoData
          }
          invoiceType.insertInvoiceType(data).then(res => {
            this.btnLoading = false
            if (res.data.code == '0000') {
              this.dialogAddOne = false
              this.dialogAddTwo = false
              this.queryInvoiceTypesByPage()
              this.$refs[formName].resetFields()
              this.$message({ type: 'success', message: '操作成功' })
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          return false
        }
      })
    },
    // 删除
    delData(id) {
      invoiceType.deleteInvoiceType({ id }).then(res => {
        if (res.data.code == '0000') {
          this.queryInvoiceTypesByPage()
          this.$message({ type: 'success', message: '删除成功' })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 查询一级
    queryInvoiceTypes(id) {
      invoiceType.queryInvoiceTypes({
        'parentId': '0',
        'levySub': id
      }).then(res => {
        this.typeNameOneArr = res.data.data
        console.log(this.typeNameOneArr)
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryInvoiceTypesByPage()
    },
    // 查询主体
    queryLevyBodyInfos() {
      levyBody.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyArr = res.data.data
          if (this.levyArr.length === 1) {
            this.formData.levyName = this.levyArr[0].id
            this.dialogAddOneData.levySub = this.levyArr[0].id
            this.dialogAddTwoData.levySub = this.levyArr[0].id
          }
          // if(res.data.data.length > 1){
          //   this.levyArr.unshift({name:'全部',id:''})
          // }
        } else {
          newAlert(this.$tips, res.data.message)
        }
        this.queryInvoiceTypesByPage()
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 50vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
