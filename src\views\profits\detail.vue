<template>
  <div class="content-main">
    <div class="general-layout">
      <el-table
        :data="listData"
        border
        size="mini"
        class="list-table"
      >
        <el-table-column
          prop="merName"
          label="企业名称"
          width="200"
          show-overflow-tooltip
        />

        <el-table-column
          prop="leaderName"
          label="项目负责人"
          width="100"
          show-overflow-tooltip
        />

        <el-table-column
          prop="agentName"
          label="所属代理"
          width="100"
          show-overflow-tooltip
        />

        <el-table-column
          prop="saleName"
          label="上级代理／销售"
          width="110"
          show-overflow-tooltip
        />

        <el-table-column
          prop="levyName"
          label="代征主体"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="feeRate"
          label="服务费率"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="settMoney"
          label="结算金额"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="diffRate"
          label="差额费率"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="profitMoney"
          label="总分润金额（元）"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="money"
          label="提现金额（元）"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="操作"
          width="100"
        >
          <template slot-scope="scope">
            <a class="tb-active-red mar-right" @click="viewInfo(scope.row)">结算明细</a>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import profits from '@/axios/default/profits'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { newAlert } from '@/utils'

export default {
  name: 'Detail',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      listData: [],
      balanceType: ''
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    list() {
      profits.queryAgentProfitByBatchNum({
        batchNum: this.$route.query.batchNum,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
          this.balanceType = res.data.data.balanceType
        }
      })
    },
    viewInfo(data) {
      const linkData = { ...data }
      let path
      if (this.balanceType === '1') {
        path = '/recharge/list'
      } else {
        path = '/balance/list'
      }
      this.$router.push({
        path: path,
        query: {
          merName: linkData.merName,
          levyId: linkData.levyId,
          createTimeFrom: linkData.profitMonth ? moment(linkData.profitMonth).startOf('month').format('YYYY-MM-DD HH:mm:ss') : '',
          createTimeTo: linkData.profitMonth ? moment(linkData.profitMonth).endOf('month').format('YYYY-MM-DD HH:mm:ss') : ''
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    }
  }
}
</script>
<style scoped>
</style>

