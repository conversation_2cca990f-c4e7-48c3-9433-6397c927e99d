<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间起" class="form-items" prop="createTimeFrom">
                <el-date-picker
                  v-model="formData.createTimeFrom"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间止" class="form-items" prop="createTimeTo">
                <el-date-picker
                  v-model="formData.createTimeTo"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="24" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-card class="list-card">
          <el-row>
            <el-col :span="24">
              <div class="table-headers">
                <span class="table-header-name">结算总金额：</span>
                <span class="table-header-num">{{ payMoney || 0 }}元</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="payeeName"
            label="姓名"
            show-overflow-tooltip
          />
          <el-table-column
            prop="payeeIdCard"
            label="身份证号"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="orderState"
            label="审核状态"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ orderState[scope.row.orderState] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="settMoney"
            label="结算金额"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.settMoney }}元
            </template>
          </el-table-column>
          <el-table-column
            prop="levyName"
            label="代征主体"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            label="订单创建时间"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="address"
            label="操作"
            fixed="right"
            width="260"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="viewInfo(scope.row)">查看订单详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog title="查看订单详情" :visible.sync="dialogOrder" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form label-width="140px" size="mini" class="form-style">
          <el-form-item label="企业名称">
            <el-input v-model="dialogOrderData.merName" disabled />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="dialogOrderData.payeeName" disabled />
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="dialogOrderData.payeeIdCard" disabled />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="dialogOrderData.telephone" disabled />
          </el-form-item>
          <el-form-item label="结算金额">
            <el-input v-model="dialogOrderData.settMoney" disabled />
          </el-form-item>
          <el-form-item label="支付通道">
            <el-input v-model="dialogOrderData.channelName" disabled />
          </el-form-item>
          <el-form-item label="订单创建时间">
            <el-input v-model="dialogOrderData.createTime" disabled />
          </el-form-item>
          <el-form-item label="订单备注">
            <el-input v-model="dialogOrderData.remark" type="textarea" :rows="2" disabled />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button @click="dialogOrder = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import tradeService from '@/axios/default/tradeService'
import { formatNumber } from '@/utils'
import moment from 'moment'
export default {
  name: 'List',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      payMoney: 0,
      total: 0,

      formData: {
        createTimeFrom: new Date(moment().month(moment().month() - 1).startOf('month')),
        createTimeTo: new Date(moment().month(moment().month() - 1).endOf('month'))
      },

      listData: [],
      dialogOrder: false,
      dialogOrderData: {},
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      orderState: { 1: '初审中', 2: '初审拒绝', 3: '复审中', 4: '审核通过', 5: '复审拒绝', 6: '系统通过' },
      settlementType: { 1: '委托代征', 2: '自然人代发', 3: '个体户' },
    }
  },
  mounted() {
    this.queryTradeOrderDetailsServiceYd()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryTradeOrderDetailsServiceYd()
    },
    queryTradeOrderDetailsServiceYd() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      tradeService.queryTradeOrderDetailsServiceYd({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'trade': {
          'createTimeFrom': this.formData.createTimeFrom,
          'createTimeTo': this.formData.createTimeTo
        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
        this.payMoney = formatNumber(res.data.data.payMoney)
      })
    },
    viewInfo(data) {
      this.dialogOrder = true
      this.dialogOrderData = data
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryTradeOrderDetailsServiceYd()
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .table-headers {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>

