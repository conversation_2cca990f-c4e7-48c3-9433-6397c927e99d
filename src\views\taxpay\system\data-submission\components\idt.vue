<template>
  <!-- 数据表格 -->
  <el-table
    :data="tableData"
    border
    stripe
    class="data-table"
    size="small"
    :header-cell-style="{
      textAlign: 'center',
      whiteSpace: 'nowrap',
      overflow: 'visible',
      textOverflow: 'unset'
    }"
  >
    <el-table-column prop="序号" label="序号" width="60" fixed="left">
      <template slot-scope="scope">
        {{ scope.$index + 1 }}
      </template>
    </el-table-column>

    <el-table-column prop="shopName" label="是否已取得登记征照" width="150" show-overflow-tooltip />

    <el-table-column label="已取得登记证照" align="center">
      <el-table-column prop="province" label="名称（姓名）" show-overflow-tooltip />
      <el-table-column
        prop="province"
        label="统一社会信用代码（纳税人识别号）"
        show-overflow-tooltip
      />
      <el-table-column prop="province" label="专业服务机构标识" show-overflow-tooltip />
    </el-table-column>

    <el-table-column label="未取得登记征照" align="center">
      <el-table-column label="姓名" show-overflow-tooltip />
      <el-table-column label="证件类型" show-overflow-tooltip />
      <el-table-column label="证件号码" show-overflow-tooltip />
      <el-table-column label="国家或地区" show-overflow-tooltip />
      <el-table-column label="是否存在免报送收入信息" show-overflow-tooltip />
      <el-table-column label="免报类型" show-overflow-tooltip />
    </el-table-column>

    <el-table-column label="地址" show-overflow-tooltip />
    <el-table-column label="店铺（用户）名称" show-overflow-tooltip />
    <el-table-column label="店铺（用户）唯一标识码" show-overflow-tooltip />
    <el-table-column label="网址链接（选填）" show-overflow-tooltip />

    <el-table-column label="结算（支付）账户信息（选填）" align="center">
      <el-table-column label="开户银行（）非银行支付机构" show-overflow-tooltip />
      <el-table-column label="账户名称" show-overflow-tooltip />
      <el-table-column label="银行账号/支付账户" show-overflow-tooltip />
    </el-table-column>

    <el-table-column label="联系人姓名" show-overflow-tooltip />
    <el-table-column label="联系电话" show-overflow-tooltip />
    <el-table-column label="经营开始时间" show-overflow-tooltip />
    <el-table-column label="经营结束时间" show-overflow-tooltip />
    <el-table-column label="信息状态标识" show-overflow-tooltip />
  </el-table>
</template>

<script>
export default {
  name: 'Rev',
  data() {
    return {
      tableData: [
        {
          shopName: '张三的小店',
          shopId: 'UID123456',
          name: '张三',
          idNumber: '110101199001011234',
          reportPeriod: '2024-01',
          totalIncome: '50000.00',
          taxableIncome: '45000.00',
          exemptIncome: '5000.00',
          withheldTax: '4500.00',
          serviceFee: '500.00',
          incomeType: '经营所得',
          paymentMethod: '支付宝',
          paymentDate: '2024-01-31',
          remarks: '正常经营收入',
          status: '新增'
        }
      ]
    }
  },
  methods: {}
}
</script>
