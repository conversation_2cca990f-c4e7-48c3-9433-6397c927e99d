<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="签约时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :clearable="false"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="签约状态" class="form-items">
                    <el-select v-model="formData.signingStatus" placeholder="请选择" class="auto-width">
                      <el-option label="请选择" value="" />
                      <el-option label="签约" :value="1" />
                      <el-option label="解约" :value="2" />
                      <el-option label="已拉黑" :value="3" />
                      <el-option label="已加白" :value="4" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="姓名" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="身份证号/证件号" class="form-items">
                    <el-input v-model="formData.idCard" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="商户编号" class="form-items">
                    <el-input v-model="formData.merNo" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option v-for="item in levyBodyIdArr" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">

                <!--                <el-col :lg="6" :xs="24">-->
                <!--                  <el-form-item label="证件号" class="form-items">-->
                <!--                    <el-input v-model="formData.certNumber" placeholder="请输入内容" clearable />-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-add" type="primary" @click="freelancerDownLoadData">下载自由职业者信息</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right;display: flex">

                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增</el-button>
                    <el-button icon="el-icon-upload" plain type="primary" @click="isCheckBox">批量新增</el-button>
                    <el-button icon="el-icon-download" plain type="primary">
                      <a :href="downHosts()">批量新增模版下载</a>
                    </el-button>
                    <el-button v-hasNoPermission="['free:noShow-down']" icon="el-icon-download" plain type="primary" @click="downloadContract">批量下载合同</el-button>
                    <el-button icon="el-icon-download" plain type="primary" @click="downloadCard">下载身份证照片</el-button>
                    <el-upload ref="uploadImport" class="upload-more" action="" :http-request="(params => uploadImport(params))" :show-file-list="false" accept=".zip" :disabled="uploadLoading">
                      <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content" style="line-height: 26px;">
                          仅支持上传.zip文件格式且.zip包内文件名与格式要求如下<br>
                          身份证正面图片为：身份证号码_1.jpg<br>
                          身份证反面图片为：身份证号码_2.jpg
                        </div>
                        <el-button icon="el-icon-upload" plain type="primary" :loading="uploadLoading">上传身份证照片</el-button>
                      </el-tooltip>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table :data="listData" border size="mini" class="list-table">
          <el-table-column prop="merNo" label="商户编号" width="200" show-overflow-tooltip />
          <el-table-column prop="merName" label="企业名称" width="250" show-overflow-tooltip />
          <el-table-column prop="name" label="姓名" width="150" show-overflow-tooltip />
          <el-table-column prop="idCard" label="身份证号/证件号" width="200" show-overflow-tooltip />
          <el-table-column prop="bankCardNo" label="卡号" width="200" show-overflow-tooltip />
          <el-table-column prop="alipayNo" label="支付宝账号" width="200" show-overflow-tooltip />
          <el-table-column prop="wechatNo" label="微信号" width="200" show-overflow-tooltip />
          <el-table-column prop="mobile" label="手机号" width="150" show-overflow-tooltip />
          <el-table-column prop="isHmt" width="150" label="是否海外或港澳台用户">
            <template slot-scope="scope">
              {{ scope.row.isHmt === '1' ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="signingStatus" label="签约状态" width="150">
            <template slot-scope="scope">
              {{ signingStatus[scope.row.signingStatus] }}
            </template>
          </el-table-column>
          <el-table-column prop="signingTime" width="150" label="签约时间" show-overflow-tooltip />
          <el-table-column prop="address" label="操作" fixed="right" width="180">
            <template slot-scope="scope">
              <a class="tb-btn-normal mar-right" type="text" @click="detail(scope.row.id)">查看</a>
              <a class="tb-btn-normal mar-right" type="text" @click="edit(scope.row.id)">修改</a>
              <a class="tb-btn-normal mar-right" target="_blank" type="text" @click="viewFiles(scope.row.id)">查看合同</a>
              <el-popconfirm title="确定删除吗？" @onConfirm="delData(scope.row.id)">
                <a slot="reference" class="tb-active-red">删除</a>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" :rules="rules" :label-width="formLabelWidth" size="mini" class="form-style create-free-lancer">
          <el-form-item label="商户名称" prop="merId">
            <el-select v-model="dialogData.merId" class="auto-width" filterable clearable remote placeholder="请输入关键词" :remote-method="merAllList" :loading="loading">
              <el-option v-for="item in merIdArr" :key="item.id" :label="item.merName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="姓      名" prop="name">
            <el-input v-model="dialogData.name" autocomplete="off" />
          </el-form-item>

          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="dialogData.mobile" autocomplete="off" />
          </el-form-item>
          <el-form-item label="银行卡号" prop="bankCardNo">
            <el-input v-model="dialogData.bankCardNo" autocomplete="off" />
          </el-form-item>

          <el-form-item label="支付宝账号">
            <el-input v-model="dialogData.alipayNo" autocomplete="off" />
          </el-form-item>

          <el-form-item label="微信号">
            <el-input v-model="dialogData.wechatNo" autocomplete="off" />
          </el-form-item>

          <el-form-item label="是否海外或港澳台用户" prop="isHmt">
            <el-radio v-model="dialogData.isHmt" :label="1" @change="resetFormSub('dialogData')">是</el-radio>
            <el-radio v-model="dialogData.isHmt" :label="0" @change="resetFormSub('dialogData')">否</el-radio>
          </el-form-item>
          <el-form-item v-if="dialogData.isHmt == '0'" label="身份证号" prop="idCard">
            <el-input v-model="dialogData.idCard" autocomplete="off" />
          </el-form-item>
          <el-form-item v-if="dialogData.isHmt == '1'" label="证件号" prop="certNumber">
            <el-input v-model="dialogData.certNumber" autocomplete="off" />
          </el-form-item>
          <el-form-item label="证件正面信息" prop="">
            <el-upload
              ref="upload"
              class="idCardFrontPath"
              :class="{'hide-upload': idCardFrontPath.length>=1}"
              action=""
              accept=".jpeg,.png,.jpg"
              :file-list="idCardFrontPath"
              :limit="1"
              list-type="picture-card"
              :on-preview="onIdCardPreview"
              :on-remove="onIdCardFrontRemove"
              :http-request="(params => uprequest(params,'idCardFrontPath'))"
            >
              <i slot="default" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item label="证件背面信息" prop="">
            <el-upload
              ref="upload"
              class="idCardBackPath"
              :class="{'hide-upload': idCardBackPath.length>=1}"
              action=""
              accept=".jpeg,.png,.jpg"
              :file-list="idCardBackPath"
              list-type="picture-card"
              :on-preview="onIdCardPreview"
              :on-remove="onIdCardBackRemove"
              :http-request="(params => uprequest(params,'idCardBackPath'))"
            >
              <i slot="default" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dialogData.remark" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" :disabled="dialogTitle=='查看'" type="primary" @click="submit('dialogData')">保存</el-button>
              <el-button :loading="btnLoading" @click="dialogFormVisible = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

    <el-dialog :visible.sync="dialogIdCardVisible">
      <div class="dialogIdCardImg">
        <div class="dialogIdCardImgBtns">
          <i class="el-icon-zoom-in" @click="onIdCardScale(0.1)" />
          <i class="el-icon-zoom-out" @click="onIdCardScale(-0.1)" />
          <i class="el-icon-refresh-left" @click="onIdCardRotate(-90)" />
          <i class="el-icon-refresh-right" @click="onIdCardRotate(90)" />
        </div>
        <div class="dialogIdCardImgBox">
          <img :style="{width: idCardScale+'%', transform: 'rotate('+idCardRotate+'deg)'}" :src="dialogIdCardImageUrl" alt="">
        </div>
      </div>
    </el-dialog>

    <el-dialog title="批量新增" :visible.sync="checkBoxShow" width="30%">
      <el-form v-loading="formLoading" size="mini" class="form-style">
        <el-form-item label="商户名称" prop="merchantId">
          <el-select v-model="merId" filterable clearable remote placeholder="请输入关键词" :remote-method="merAllList" :loading="loading">
            <el-option v-for="item in merIdArr" :key="item.id" :label="item.merName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否鉴权">
          <el-radio v-model="isCheck" :label="1">鉴权</el-radio>
          <el-radio v-model="isCheck" :label="0">不鉴权</el-radio>
        </el-form-item>
        <el-form-item label="批量新增">
          <el-upload ref="uploadMore" class="upload-more" action="" :http-request="(params => uploadMore(params))" :show-file-list="false" accept=".xlsx,.xls">
            <el-button icon="el-icon-upload" plain type="primary">批量新增</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="downError">
          <el-link type="primary" style="margin-left: 70px;font-size: 12px" @click="freelancerFailDownLoadData">下载异常名单</el-link>
        </el-form-item>
      </el-form>

    </el-dialog>

  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import freelancerInfo from '@/axios/default/freelancerInfo'
import publics from '@/axios/default/public'

import { downHost, parseTime, newAlert } from '@/utils'
import moment from 'moment'

var checkPhone = (rule, value, callback) => {
  const reg = /^1[0-9]\d{9}$/
  if (reg.test(value)) {
    callback()
  } else {
    return callback(new Error('请输入正确的手机号'))
  }
}
export default {
  name: 'FreelancerInfo',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: [new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1), new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1)],
      formData: {
        merName: '',
        signingStatus: '',
        name: '',
        idCard: '',
        certNumber: '',
        signingTimeFrom: new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1),
        signingTimeTo: new Date(),
        idCardFrontPath: '',
        idCardBackPath: '',
        merNo: '',
        levyId: ''
      },
      uploadLoading: false,
      formLoading: false,
      dialogData: this.initData(),
      dialogFormVisible: false,
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      signingStatus: { 0: '未签约', 1: '已签约', 2: '解约', 3: '已拉黑', 4: '已加白' },
      merIdArr: [],
      rules: {
        merId: [{ required: true, message: '请选择商户', trigger: 'change' }],
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
        mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }, { validator: checkPhone, trigger: ['blur', 'change'] }],
        bankCardNo: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        certNumber: [{ required: true, message: '请输入证件号', trigger: 'blur' }],
        idCardFrontPath: [{ required: true, message: '请上传身份证正面照片', trigger: 'blur' }],
        idCardBackPath: [{ required: true, message: '请上传身份证背面照片', trigger: 'blur' }]
      },
      levyBodyIdArr: [],
      idCardFrontPath: [],
      idCardBackPath: [],
      dialogIdCardVisible: false,
      dialogIdCardImageUrl: '',
      idCardRotate: 0,
      idCardScale: 100,
      loading: false,
      btnLoading: false,
      checkBoxShow: false,
      isCheck: 1,
      merId: '',
      dialogTitle: '',
      requestMerId: '',
      rquestMerTime: '',
      downError: false
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
    this.freelancerInfoList()
    this.merAllList()
  },
  methods: {
    moment,
    initData() {
      this.idCardFrontPath = []
      this.idCardBackPath = []
      return {
        merId: '',
        name: '',
        idCard: '',
        isHmt: 0,
        mobile: '',
        idCardFrontPath: '',
        idCardBackPath: '',
        bankCardNo: '',
        alipayNo: '',
        wechatNo: '',
        certNumber: '',
        signingStatus: '1',
        remark: ''
      }
    },
    onSearch() {
      this.pageNum = 1
      this.freelancerInfoList()
    },
    // 列表信息
    freelancerInfoList() {
      this.formData.signingTimeFrom = moment(this.pickTime[0]).format('YYYY-MM-DD')
      this.formData.signingTimeTo = moment(this.pickTime[1]).format('YYYY-MM-DD')
      freelancerInfo.list({
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        signingStatus: this.formData.signingStatus,
        name: this.formData.name,
        merName: this.formData.merName,
        idCard: this.formData.idCard,
        certNumber: this.formData.certNumber,
        signingTimeFrom: this.formData.signingTimeFrom,
        signingTimeTo: this.formData.signingTimeTo,
        merNo: this.formData.merNo,
        levyId: this.formData.levyId
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
      })
    },
    add() {
      this.dialogData = this.initData()
      this.dialogFormVisible = true
      this.dialogTitle = '新增'
      this.$nextTick(function() {
        this.resetForm('dialogData')
      })
    },
    detail(id) {
      this.dialogData = this.initData()
      this.dialogFormVisible = true
      this.dialogTitle = '查看'
      this.$nextTick(function() {
        this.resetForm('dialogData')
        this.getFreelancerInfo(id)
      })
    },
    edit(id) {
      this.dialogData = this.initData()
      this.dialogFormVisible = true
      this.dialogTitle = '编辑'
      this.$nextTick(function() {
        this.resetForm('dialogData')
        this.getFreelancerInfo(id)
      })
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogData.id) {
            this.update()
          } else {
            this.addFreelancerInfo()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    onIdCardScale(scale) {
      if (scale > 0 && this.idCardScale < 200) {
        this.idCardScale = this.idCardScale + (scale * 100)
      } else if (scale < 0 && this.idCardScale > 30) {
        this.idCardScale = this.idCardScale + (scale * 100)
      }
    },
    onIdCardRotate(deg) {
      this.idCardRotate = this.idCardRotate + deg
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    },
    resetFormSub(formName) {
      this.dialogData.idCard = ''
      this.dialogData.certNumber = ''
      this.dialogData.idCardFrontPath = ''
      this.dialogData.idCardBackPath = ''
      this.idCardFrontPath = []
      this.idCardBackPath = []
    },
    freelancerFailDownLoadData() {
      freelancerInfo.freelancerFailDownLoadData({
        merId: this.requestMerId,
        createTime: this.rquestMerTime
      }, `异常列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },

    // 修改信息
    getFreelancerInfo(id) {
      freelancerInfo.getFreelancerInfo({
        id
      }).then(res => {
        this.dialogData = res.data.data
        this.idCardFrontPath = res.data.data.idCardFrontPath ? [{ name: 'idCardFrontPath', url: res.data.data.idCardFrontPath }] : []
        this.idCardBackPath = res.data.data.idCardBackPath ? [{ name: 'idCardBackPath', url: res.data.data.idCardBackPath }] : []
      })
    },
    // 添加数据
    addFreelancerInfo() {
      const formData = new FormData()
      const data = this.dialogData
      for (const key in data) {
        formData.append(key, data[key])
      }
      freelancerInfo.addFreelancerInfo(formData).then(res => {
        this.btnLoading = false
        if (res.data.code == '0000') {
          this.$refs.dialogData.resetFields()
          this.dialogFormVisible = false
          this.$message.success('操作成功')
          this.freelancerInfoList()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 修改数据
    update() {
      const formData = new FormData()
      const data = this.dialogData
      for (const key in data) {
        formData.append(key, data[key])
      }
      freelancerInfo.update(formData).then(res => {
        this.btnLoading = false
        if (res.data.code == '0000') {
          this.$refs.dialogData.resetFields()
          this.dialogFormVisible = false
          this.$message.success('操作成功')
          this.freelancerInfoList()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    delData(id) {
      freelancerInfo.delete({ id }).then(res => {
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '删除成功' })
          this.freelancerInfoList()
        }
      })
    },
    // 商户列表
    merAllList(query) {
      if (query !== '') {
        this.loading = true
        publics.merAllList({
          merName: query
        }).then(res => {
          this.loading = false
          this.merIdArr = res.data.data
        })
      } else {
        this.merIdArr = []
      }
    },
    // 分页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.freelancerInfoList()
    },
    // 上传前处理
    uprequest(param, item) {
      const that = this
      const fileType = param.file.type
      const isJPG = fileType === 'image/jpeg' || fileType === 'image/png' || fileType === 'image/jpg'
      if (!isJPG) {
        this.$message.error('身份证照片格式有误，仅支持 .jpg, .png, .jpeg 格式!')
        return
      }
      const fileSize = param.file.size
      const isLtM = fileSize / 1024 / 1024 < 5
      if (!isLtM) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 5MB!')
        }, 100)
        return
      }

      const reader = new FileReader()
      reader.readAsDataURL(param.file)
      reader.onload = function(e) {
        that[item] = e.target.result
        that.dialogData[item] = param.file
      }
    },
    onIdCardPreview(args) {
      this.dialogIdCardImageUrl = args.url
      this.dialogIdCardVisible = true
    },
    onIdCardFrontRemove() {
      this.idCardFrontPath = []
      this.$set(this.formData, 'idCardFrontPath', '')
    },
    onIdCardBackRemove() {
      this.idCardBackPath = []
      this.$set(this.formData, 'idCardBackPath', '')
    },
    uploadMore(param) {
      const that = this
      const size = param.file.size
      const isLtM = size / 1024 / 1024 < 15
      if (!isLtM) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 15MB!')
        }, 100)
        return
      }
      if (this.merId == '') {
        this.$message.error('请填写商户')
        return false
      }
      this.formLoading = true
      const formData = new FormData()
      formData.append('file', param.file)
      formData.append('isCheck', this.isCheck)
      formData.append('merId', this.merId)
      freelancerInfo.batchImportFreeLeader(formData).then(res => {
        if (res.data.code === '0000') {
          this.$message.success('操作成功')
          this.checkBoxShow = false
          this.merId = ''
        } else {
          this.downError = true
          this.requestMerId = res.data.data.merId
          this.rquestMerTime = res.data.data.createTime
          this.$alert(res.data.message, '系统提示', {
            type: 'warning',
            showConfirmButton: false,
            iconClass: 'el-icon-my-alert'
          })
        }

        this.formLoading = false
        this.freelancerInfoList()
      }).catch(e => {
        this.formLoading = false
      })
    },
    uploadImport(param) {
      const that = this
      const size = param.file.size
      const isLtM = size / 1024 / 1024 < 50
      if (!isLtM) {
        setTimeout(function() {
          // that.$message.error('上传大小不能超过 15MB!')
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      this.uploadLoading = true
      const formData = new FormData()
      formData.append('file', param.file)
      freelancerInfo.import(formData).then(res => {
        this.uploadLoading = false
        if (res.data.code === '0000') {
          this.$message.success('操作成功')
        } else {
          this.$message.error(res.data.message)
        }
      })
    },
    // 下载自由职业者信息
    freelancerDownLoadData() {
      this.formData.signingTimeFrom = moment(this.pickTime[0]).format('YYYY-MM-DD')
      this.formData.signingTimeTo = moment(this.pickTime[1]).format('YYYY-MM-DD')
      freelancerInfo.freelancerDownLoadData({
        signingStatus: this.formData.signingStatus,
        name: this.formData.name,
        merName: this.formData.merName,
        idCard: this.formData.idCard,
        certNumber: this.formData.certNumber,
        signingTimeFrom: this.formData.signingTimeFrom,
        signingTimeTo: this.formData.signingTimeTo,
        merNo: this.formData.merNo,
        levyId: this.formData.levyId
      }, `自由职业者信息${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {
        if (res) {
          this.$message.success('操作成功')
        }
      })
    },
    // 下载身份证
    downloadCard() {
      if (this.formData.merName == '') {
        this.$message.error('请先搜索企业名称进行下载')
        return false
      }
      this.formData.signingTimeFrom = moment(this.pickTime[0]).format('YYYY-MM-DD')
      this.formData.signingTimeTo = moment(this.pickTime[1]).format('YYYY-MM-DD')
      freelancerInfo.download({
        signingStatus: this.formData.signingStatus,
        name: this.formData.name,
        merName: this.formData.merName,
        idCard: this.formData.idCard,
        certNumber: this.formData.certNumber,
        signingTimeFrom: this.formData.signingTimeFrom,
        signingTimeTo: this.formData.signingTimeTo
      }, `身份证照片${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.zip`, '.zip').then(res => {
        if (res) {
          this.$message.success('操作成功')
        }
      })
    },
    // 下载合同
    downloadContract() {
      if (this.formData.merName == '') {
        this.$message.error('请先搜索企业名称进行下载')
        return false
      }
      this.formData.signingTimeFrom = moment(this.pickTime[0]).format('YYYY-MM-DD')
      this.formData.signingTimeTo = moment(this.pickTime[1]).format('YYYY-MM-DD')
      freelancerInfo.downloadContract({
        signingStatus: this.formData.signingStatus,
        name: this.formData.name,
        merName: this.formData.merName,
        idCard: this.formData.idCard,
        levyId: this.formData.levyId,
        certNumber: this.formData.certNumber,
        signingTimeFrom: this.formData.signingTimeFrom,
        signingTimeTo: this.formData.signingTimeTo
      }, `合同${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.zip`, '.zip').then(res => {
        this.$message.success('操作成功')
      })
    },
    isCheckBox() {
      this.merId = ''
      this.requestMerId = ''
      this.rquestMerTime = ''
      this.downError = false
      this.checkBoxShow = true
    },
    downHosts() {
      return downHost + '/freeLeader-template.xls'
    },
    viewFiles(id) {
      // window.open("http://www.cnblogs.com/liumengdie/","_blank")
      freelancerInfo.downloadOneContract({
        id,
        levyId: this.formData.levyId
      }, `合同${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.zip`, '.zip').then(res => {
        if (res.data.code === '0000') {
          window.open(res.data.data, '_blank')
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    queryLevyBodyInfos() {
      publics.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card {
  margin-bottom: 20px;
}

.auto-width {
  width: 100%;
}

.dialog-form {
  display: flex;
  flex-flow: column nowrap;
}
.el-form-item__content {
  margin-left: 0;
}

.list-table {
  margin: 0 0 20px 0;
  .el-button {
    padding: 0;
  }
}

.dialog-scroll {
  overflow-y: auto;
  height: calc(100%);
}

.reset-dialog {
  /deep/ .el-dialog {
    height: 80vh;
    overflow: hidden;
    .el-dialog__body {
      height: calc(100% - 54px);
    }
  }
  overflow: hidden;
}
.form-style {
  padding-right: 20px;
}
.create-free-lancer {
  .hide-upload /deep/ .el-upload--picture-card {
    display: none !important;
  }
  /deep/ .el-upload-list__item-delete {
    position: absolute;
    font-size: 16px !important;
  }
  /deep/ .el-upload-list__item-preview {
    font-size: 32px !important;
  }
}
.idCardFrontPath,
.idCardBackPath {
  i {
    font-size: 40px;
    color: #dcdfe6;
  }
}
.upload-more {
  display: inline-block;
}
.dialogIdCardImg {
  .dialogIdCardImgBtns {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
    i {
      font-size: 26px;
      font-weight: 600;
      text-align: center;
      cursor: pointer;
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .dialogIdCardImgBox {
    height: 500px;
    overflow: scroll;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
