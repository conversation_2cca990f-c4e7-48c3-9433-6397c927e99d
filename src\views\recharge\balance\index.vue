<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select
                      v-model="formData.levyId"
                      filterable
                      reserve-keyword
                      placeholder="请选择代征主体"
                      class="auto-width"
                      @change="onLevyIdSelect"
                    >
                      <el-option
                        v-for="item in levyAllArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="通道" class="form-items">
                    <el-select
                      v-model="formData.channelId"
                      filterable
                      reserve-keyword
                      placeholder="请选择通道"
                      class="auto-width"
                    >
                      <el-option
                        v-for="item in channelAllArr"
                        :key="item.id"
                        :label="item.channelName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" type="primary" plain @click="onAddMerchant">添加通知</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            max-width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="levyName"
            label="代征主体"
            max-width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="channelName"
            label="通道"
            show-overflow-tooltip
          />
          <el-table-column
            prop="canBalance"
            label="可用余额"
            max-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="sendMsgBalance"
            label="提醒阈值"
            max-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="是否提醒"
            min-width="80"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.isSendMsgBalance? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            max-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            fixed="right"
            max-width="140"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="onModifyMerchant(scope.row)">修改</el-button>
              <el-popconfirm title="确定删除吗？" style="margin-right: 10px" @onConfirm="delData(scope.row)">
                <a slot="reference" class="tb-active-red">删除</a>
              </el-popconfirm>
              <el-popconfirm v-if="scope.row.sendMsgBalance > scope.row.canBalance" title="确定发送短信通知吗？" @onConfirm="onSendShortMessage(scope.row)">
                <a slot="reference" class="tb-active-red">发送短信</a>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog title="添加/编辑通知" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="代征主体" prop="levyId">
            <el-select
              v-model="dialogData.levyId"
              filterable
              reserve-keyword
              placeholder="请选择代征主体"
              class="auto-width"
              @change="onSelectLevy"
            >
              <el-option
                v-for="item in levyArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="通道" prop="channelId">
            <el-select
              v-model="dialogData.channelId"
              filterable
              reserve-keyword
              placeholder="请选择通道"
              class="auto-width"
            >
              <el-option
                v-for="item in channelArr"
                :key="item.id"
                :label="item.channel_name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="提醒阈值" prop="sendMsgBalance">
            <el-input v-model="dialogData.sendMsgBalance" type="number" placeholder="请输入提醒阈值（保留两位小数）" autocomplete="off" class="auto-width" />
          </el-form-item>
          <el-form-item label="提醒开关">
            <el-switch
              v-model="dialogData.isSendMsgBalance"
              active-color="#ff4949"
              inactive-color="#dcdfe6"
            />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button @click="dialogFormVisible=false">取消</el-button>
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">确定</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import rechInfo from '@/axios/default/rechInfo'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'
import publicApi from '../../../axios/default/public'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      levyAllArr: [],
      channelAllArr: [],
      formData: {
        levyId: '',
        channelId: ''
      },

      dialogFormVisible: false,
      levyArr: [],
      channelArr: [],
      dialogData: {
        id: '',
        merId: '',
        merName: '',
        levyId: '',
        levyName: '',
        channelId: '',
        channelName: '',
        sendMsgBalance: '',
        isSendMsgBalance: false
      },
      rules: {
        levyId: { required: true, message: '请选择代征主体', trigger: 'change' },
        channelId: { required: true, message: '请选择通道', trigger: 'change' },
        sendMsgBalance: { required: true, message: '提醒阈值不可为空', trigger: 'blur' }
      },

      listData: [],
      btnLoading: false
    }
  },
  mounted() {
    this.findSendMsgChannel()
    this.queryLevyBodyInfos()
    this.findLevyBodyByDeptId()
  },
  methods: {
    onLevyIdSelect(v) {
      this.getChannelListsByLevyId(v)
    },
    onSearch() {
      this.pageNum = 1
      this.findSendMsgChannel()
    },
    onAddMerchant() {
      this.dialogFormVisible = true
      if (this.dialogData.levyId) {
        this.$refs.dialogData.clearValidate()
        this.$refs.dialogData.resetFields()
      }
    },
    onSelectLevy(v) {
      this.$set(this.dialogData, 'channelName', '')
      this.$set(this.dialogData, 'channelId', '')
      this.getChannelListByLevyId(v)
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          const data = JSON.parse(JSON.stringify(this.dialogData))
          const selectLevy = this.levyArr.filter(item => item.id == data.levyId)
          const selectChannel = this.channelArr.filter(item => item.id == data.channelId)
          data.isSendMsgBalance = data.isSendMsgBalance ? '1' : '0'
          data.merId = Number(this.$route.query.merId)
          data.merName = this.$route.query.merName
          data.levyName = selectLevy.length > 0 ? selectLevy[0].name : ''
          data.channelName = selectChannel.length > 0 ? selectChannel[0].channel_name : ''
          data.sendMsgBalance = Number(data.sendMsgBalance).toFixed(2)
          data.channelId = data.channelId.toString()
          data.levyId = data.levyId.toString()
          data.id = data.id.toString()
          rechInfo.addSendMsgChannel(data).then(res => {
            this.btnLoading = false
            this.$refs.dialogData.clearValidate()
            this.$refs.dialogData.resetFields()
            if (res.data.code === '0000') {
              this.dialogFormVisible = false
              this.findSendMsgChannel()
              this.$message({ type: 'success', message: '操作成功' })
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    findSendMsgChannel() {
      const currentLevy = this.levyAllArr.filter(item => item.id == this.formData.levyId)
      const currentChannel = this.channelAllArr.filter(item => item.id == this.formData.channelId)
      rechInfo.findSendMsgChannel({
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        merId: this.$route.query.merId,
        levyName: currentLevy.length > 0 ? currentLevy[0].name : '',
        channelName: currentChannel.length > 0 ? currentChannel[0].channelName : ''
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },

    onModifyMerchant(data) {
      this.dialogFormVisible = true
      this.$set(this.dialogData, 'isSendMsgBalance', data.isSendMsgBalance == 1 ? true : data.isSendMsgBalance == 0 ? false : data.isSendMsgBalance)
      this.$set(this.dialogData, 'sendMsgBalance', data.sendMsgBalance)
      this.$set(this.dialogData, 'channelName', data.channelName)
      this.$set(this.dialogData, 'channelId', data.channelId)
      this.$set(this.dialogData, 'levyName', data.levyName)
      this.$set(this.dialogData, 'levyId', data.levyId)
      this.$set(this.dialogData, 'id', data.id)
      this.getChannelListByLevyId(data.levyId)
    },
    delData(args) {
      rechInfo.deleteSendMsgChannel({
        id: args.id
      }).then(res => {
        if (res.data.code === '0000') {
          this.findSendMsgChannel()
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    onSendShortMessage(args) {
      rechInfo.sendMsgBalanceManual({
        id: args.id
      }).then(res => {
        if (res.data.code === '0000') {
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },


    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code == '0000') {
          console.log(res, '获取的所有代征主体')
          this.levyAllArr = res.data.data
        }
      })
    },
    findLevyBodyByDeptId() {
      publicApi.findLevyBodyByDeptId({
        merId: this.$route.query.merId
      }).then(res => {
        if (res.data.code == '0000') {
          console.log(res, '获取的所有代征主体')
          this.levyArr = res.data.data
        }
      })
    },
    getChannelListsByLevyId(id) {
      publicApi.getChannelListByLevyId({
        levyBodyId: id
      }).then(res => {
        if (res.data.code == '0000') {
          console.log(res, '根据主体查询通道')
          this.channelAllArr = res.data.data
        }
      })
    },
    getChannelListByLevyId(id) {
      publicApi.findChannelByMerId({
        merId: this.$route.query.merId,
        levyId: id
      }).then(res => {
        if (res.data.code == '0000') {
          console.log(res, '根据主体查询通道')
          this.channelArr = res.data.data
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.findSendMsgChannel()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .pagination{
    text-align: right;
  }
</style>
