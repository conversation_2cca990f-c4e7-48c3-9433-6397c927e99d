<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="创建时间" class="form-items">
                    <el-date-picker
                      v-model="formData.createTimeFrom"
                      class="auto-width"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="项目负责人" class="form-items">
                    <el-input v-model="formData.leaderName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="所属销售" class="form-items">
                    <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">

                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="创建时间"
            show-overflow-tooltip
          />
          <el-table-column
            prop="merName"
            label="企业名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="leaderName"
            label="项目负责人"
          />
          <el-table-column
            prop="saleName"
            label="所属销售"
          />
          <el-table-column
            prop="agentName"
            label="所属代理"
          />
          <el-table-column
            prop="status"
            label="状态"
          >
            <template slot-scope="scope">
              {{ statusArr[scope.row.status] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="viewInfo(scope.row.id)">查看</el-button>
              <el-button type="text" size="mini" @click="openbox(scope.row,scope.$index)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    初审弹框-->
    <el-dialog title="商户初审" :visible.sync="dia_trialShow" width="40%">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dia_trialData" :model="dia_trialData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称">
            <el-input v-model="dia_trialData.merName" disabled />
          </el-form-item>

          <el-form-item v-for="(item,index) in levys" :key="index" label="服务费率">
            {{ '代征主体:'+item.levyName+' 服务费率:'+item.feeRates+'%' }}
          </el-form-item>

          <el-form-item label="审核意见" prop="auditRemark">
            <el-input v-model="dia_trialData.auditRemark" type="textarea" :rows="2" autocomplete="off" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="merchantAudit('dia_trialData','1')">通过</el-button>
              <el-button :loading="btnLoading" @click="merchantAudit('dia_trialData','0')">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import merchants from '@/axios/default/merchants'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        auditStatus: '1',
        createTimeFrom: '',
        leaderName: '',
        saleName: '',
        merName: ''
      },
      dia_trialShow: false,
      dia_trialData: {},
      levys: [],
      btnLoading: false,
      listData: [],
      rules: {
        auditRemark: { required: true, message: '请填写备注', trigger: 'blur' }
      },
      statusArr: { 0: '编辑', 1: '初审中', 2: '复审中', 3: '正常', 4: '预开', 5: '停用' } // 商户状态
    }
  },
  mounted() {
    this.audit_list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.audit_list()
    },
    audit_list() {
      merchants.audit_list({
        ...this.formData,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        total: this.tatal
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
      })
    },
    // 初审
    merchantAudit(formName, auditStatus) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          const that = this
          merchants.merchantAudit({
            merId: this.dia_trialData.id,
            auditStatus,
            auditType: '0',
            auditRemark: this.dia_trialData.auditRemark
          }).then(res => {
            if (res.data.code === '0000') {
              this.dia_trialShow = false
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.audit_list()
              this.$refs[formName].clearValidate()
              this.$refs[formName].resetFields()
            } else {
              newAlert(this.$tips, res.data.message)
            }
            this.btnLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOneMerchantInfo(id) {
      merchants.getOneMerchantInfo({
        id: id.toString()
      }).then(res => {
        this.levys = res.data.data.levys
      })
    },
    // 打开初审框
    openbox(data, index) {
      this.dia_trialShow = true
      this.dia_trialData = data
      this.dia_trialData.listIndex = index
      this.getOneMerchantInfo(data.id)
    },
    viewInfo(id) {
      this.$store.commit('merchants/setCreateMerchantStep', 1)
      this.$router.push({
        path: '/merchants/actions',
        query: {
          page: 'view',
          id
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.audit_list()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }
  .pagination{
    text-align: right;
  }
</style>
