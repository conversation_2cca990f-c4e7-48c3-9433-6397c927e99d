<template>
  <el-table :data="tableData" border stripe size="small">
    <el-table-column prop="序号" label="序号" width="60" fixed="left">
      <template slot-scope="scope">
        {{ scope.$index + 1 }}
      </template>
    </el-table-column>

    <!-- 统一到第一层级的数字列 -->
    <el-table-column label="1" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>是否已取得登记证照</div>
          <div style="font-weight: bold; color: #409eff;">1</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="2" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>已取得登记证照的其他单位、个体工商户</div>
          <div>名称（姓名）</div>
          <div style="font-weight: bold; color: #409eff;">2</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="3" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>已取得登记证照的其他单位、个体工商户</div>
          <div>统一社会信用代码（纳税人识别号）</div>
          <div style="font-weight: bold; color: #409eff;">3</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="4" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>姓名</div>
          <div style="font-weight: bold; color: #409eff;">4</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="5" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>证件类型</div>
          <div style="font-weight: bold; color: #409eff;">5</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="6" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>证件号码</div>
          <div style="font-weight: bold; color: #409eff;">6</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="7" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>国家或地区</div>
          <div style="font-weight: bold; color: #409eff;">7</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="8" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>联系电话</div>
          <div style="font-weight: bold; color: #409eff;">8</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="9" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>结算（支付）账户信息（选填）</div>
          <div>开户银行/非银行支付机构</div>
          <div style="font-weight: bold; color: #409eff;">9</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="10" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>结算（支付）账户信息（选填）</div>
          <div>账户名称</div>
          <div style="font-weight: bold; color: #409eff;">10</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="11" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>结算（支付）账户信息（选填）</div>
          <div>银行账号/支付账户</div>
          <div style="font-weight: bold; color: #409eff;">11</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="15" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>已取得登记证照的其他单位、个体工商户</div>
          <div>收入总额</div>
          <div style="font-weight: bold; color: #409eff;">15</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="16" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>已取得登记证照的其他单位、个体工商户</div>
          <div>退款金额</div>
          <div style="font-weight: bold; color: #409eff;">16</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="17=15-16" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>已取得登记证照的其他单位、个体工商户</div>
          <div>收入净额</div>
          <div style="font-weight: bold; color: #409eff;">17=15-16</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="18" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>收入总额</div>
          <div style="font-weight: bold; color: #409eff;">18</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="19" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>退款金额</div>
          <div style="font-weight: bold; color: #409eff;">19</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="20=18-19=21+22" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>收入净额 - 小计</div>
          <div style="font-weight: bold; color: #409eff;">20=18-19=21+22</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="21" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>收入净额 - 其中：劳务报酬</div>
          <div style="font-weight: bold; color: #409eff;">21</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="22" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>销售服务取得的收入信息</div>
          <div>未取得登记证照的网络主播、其他自然人</div>
          <div>收入净额 - 其中：其他收入</div>
          <div style="font-weight: bold; color: #409eff;">22</div>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="23" header-align="center">
      <template slot="header">
        <div style="text-align: center;">
          <div>从事其他网络交易活动取得的收入（净额）</div>
          <div style="font-weight: bold; color: #409eff;">23</div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'Ltb',
  data() {
    return {
      tableData: [
        {
          shopName: '张三的小店',
          shopId: 'UID123456',
          transactionId: 'TXN202401010001',
          transactionDate: '2024-01-01 10:30:00',
          productName: '手机壳',
          productCategory: '实物商品',
          quantity: '2',
          unit: '个',
          unitPrice: '25.00',
          totalAmount: '50.00',
          commission: '5.00',
          actualAmount: '45.00',
          buyerName: '李四',
          buyerId: 'BUYER123',
          buyerPhone: '13900000000',
          paymentMethod: '支付宝',
          transactionStatus: '已完成',
          deliveryAddress: '北京市海淀区xxx小区',
          remarks: '正常交易',
          status: '新增'
        }
      ]
    }
  },
  methods: {}
}
</script>

<style scoped>
.transaction-info-table {
  padding: 20px;
}

.table-header {
  margin-bottom: 20px;
}

.table-header h3 {
  color: #333;
  font-size: 16px;
  margin: 0;
  text-align: center;
}

.data-table {
  width: 100%;
  margin-bottom: 20px;
}

.data-table .el-table__header-wrapper {
  background-color: #e9ecef;
}

.summary-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.summary-item .label {
  font-weight: bold;
  color: #495057;
}

.summary-item .value {
  color: #007bff;
  font-weight: bold;
}

.operation-buttons {
  text-align: center;
  padding-top: 20px;
}

.operation-buttons .el-button {
  margin: 0 10px;
}

/* 表格样式优化 */
.data-table .el-table__body-wrapper {
  overflow-x: auto;
}

.data-table .el-input__inner {
  border: none;
  background: transparent;
  padding: 0 5px;
}

.data-table .el-input__inner:focus {
  border: 1px solid #409eff;
  background: #fff;
}

.data-table .el-select {
  width: 100%;
}

.data-table .el-date-editor {
  width: 100%;
}

.data-table .el-textarea__inner {
  border: none;
  background: transparent;
  padding: 2px 5px;
  resize: none;
}

.data-table .el-textarea__inner:focus {
  border: 1px solid #409eff;
  background: #fff;
}
</style>
