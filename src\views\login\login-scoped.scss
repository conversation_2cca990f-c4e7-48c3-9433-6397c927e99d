$bg:#2d3a4b;
$dark_gray:#aaa;
$light_gray:#eee;

.login-container {
  background: url(../../assets/background2.png) 50% no-repeat;
  background-size: cover;
  width: 100%;
  height: 100vh;
  .login-info {
    position: absolute;
    left: 15%;
    top: 44%;
    margin-top: -100px;
    color: #fff;
    font-weight: 600;
    .title {
      font-size: 1.8rem;
      font-weight: 600;
    }
    .sub-title {
      font-size: 1.5rem;
      margin: .3rem 0 .7rem 1rem;
    }
    .desc {
      font-size: .96rem;
      line-height: 1.9rem;
    }
  }
  .login-form {
    position: absolute;
    top: 60%;
    left: 65%;
    transform: translate(-60%, -65%);
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 8px 30px 0px rgba(0,0,0,0.04);
    width: 370px;
    padding: 42px 50px 70px 50px;
    .code-input {
      width: 50%;
      display: inline-block;
      vertical-align: middle;
    }
    .code-image {
      width: 125px;
      display: inline-block;
      vertical-align: top;
      cursor: pointer;
      height: 40px;
      background: #F1F4FA;
      border: 1px solid #DBE7FF;
    }
    .login-type {
      text-align: right;
      display: inline-block;
      width: 100%;
    }
    .logo-wrapper {
      display: inline-block;
      margin: 10px 0;
      img {
        width: 1.9rem;
        display: inline-block;
        margin: .8rem .8rem -.8rem .8rem;
        cursor: pointer;
        &.radius {
          border-radius: 50%;
        }
      }
    }
  }
  .login-footer {
    position: fixed;
    bottom: 1rem;
    width: 100%;
    text-align: center;
    color: white;
    font-size: .9rem;
    line-height: 1rem;
    height: 1rem;
    font-weight: 600;
  }
  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .title-container {
    position: relative;

    .title {
      font-size: 20px;
      color: rgba(0,0,0,.85);
      margin: 0 auto 40px auto;
      text-align: center;
      font-weight: bold;
    }

    .set-language {
      color: #aaa;
      position: absolute;
      top: 3px;
      font-size: 18px;
      right: 0;
      cursor: pointer;
    }
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }

  @media screen and (max-width: 1100px)
  {
    .login-info {
      left: 8%;
    }
  }

  @media screen and (max-width: 970px)
  {
    .login-form {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .login-info {
      display: none;
    }
  }
}
