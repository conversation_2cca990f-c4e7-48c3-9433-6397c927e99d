<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="12" :xs="24">
              <el-form-item label="开票申请时间" class="form-items">
                <el-date-picker
                  v-model="pickTime"
                  class="auto-width"
                  type="daterange"
                  align="right"
                  unlink-panels
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="12" :xs="24">
              <el-form-item label="发票开具时间" class="form-items">
                <el-date-picker
                  v-model="pickTime1"
                  class="auto-width"
                  type="daterange"
                  align="right"
                  unlink-panels
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="开票状态" class="form-items">
                <el-select v-model="formData.auditState" placeholder="请选择开票状态" class="auto-width">
                  <el-option label="请选择" value="" />
                  <el-option label="待提交" value="0" />
                  <el-option label="初审" value="1" />
                  <el-option label="初审拒绝" value="2" />
                  <el-option label="复审" value="3" />
                  <el-option label="复审拒绝" value="4" />
                  <el-option label="开票中" value="5" />
                  <el-option label="开票审核中" value="6" />
                  <el-option label="等待邮寄" value="7" />
                  <el-option label="开票作废" value="8" />
                  <el-option label="邮寄完成" value="9" />
                  <el-option label="风控审核中" value="10" />
                  <el-option label="风控审核拒绝" value="11" />
                  <el-option label="下月审核" value="12" />

                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="是否预开" class="form-items">
                <el-select v-model="formData.invPreopen" placeholder="请选择是否预开" class="auto-width">
                  <el-option label="请选择" value="" />
                  <el-option label="是" value="1" />
                  <el-option label="否" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="企业名称" class="form-items">
                <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="代征主体" class="form-items">
                <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                  <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                  <el-option
                    v-for="item in levyBodyIdArr"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="项目负责人" class="form-items">
                <el-input v-model="formData.projectName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item  v-if="this.flag ==true" label="所属销售" class="form-items">
                <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item v-if="this.flag ==true" label="所属代理" class="form-items">
                <el-input v-model="formData.agentName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :lg="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 40px">
            <el-col :span="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="" plain type="primary" @click="toApply">开票申请</el-button>
                <!-- <el-button icon="" plain type="primary" @click="redFlushInvoice">红冲开票申请</el-button> -->
                <el-button icon="" plain type="primary" @click="toYuinvoice">预开票申请</el-button>
                <el-button icon="" plain type="primary" @click="downInvoiceInfo">开票导出</el-button>
                <el-button icon="" plain type="primary" @click="downInvoiceInfoOne">处理中数据导出</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card class="list-card">
        <el-row class="table-head-money">
          <el-col :span="8">
            <div class="table-headers">
              <span class="table-header-num">{{ invoiceData.totalInvAmount || 0 }}</span>
              <span class="table-header-name">发起开票金额(元)</span>

            </div>
          </el-col>
          <el-col :span="8">
            <div class="table-headers">
              <span class="table-header-num">{{ invoiceData.dealInvAmount || 0 }}</span>
              <span class="table-header-name">开票处理中金额(元)</span>

            </div>
          </el-col>
          <el-col :span="8">
            <div class="table-headers">
              <span class="table-header-num">{{ invoiceData.successInvAmount || 0 }}</span>
              <span class="table-header-name">开票成功金额(元)</span>

            </div>
          </el-col>
          <!-- <el-col :span="6">
            <div class="table-headers">
              <span class="table-header-num">{{ invoiceData.payMoneySuc || 0 }}</span>
              <span class="table-header-name">付款成功(元)</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="table-headers">
              <span class="table-header-num">{{ invoiceData.payMoneyFai || 0 }}</span>
              <span class="table-header-name">付款失败(元)</span>
            </div>
          </el-col> -->
        </el-row>
      </el-card>
      <el-card class="list-card">
        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column label="结算批次号" width="100">
            <template slot-scope="scope">
              <div class="goProject" v-for="item in scope.row.bathNums" :key="item">
                <p @click="goProject(item)">{{ item }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="invoiceTypeName"
            label="发票类目"
            width="300"
            show-overflow-tooltip
          />
          <el-table-column
            prop="invAmount"
            label="开票金额"
          />
          <el-table-column
            prop="invQuantity"
            label="开票数量（张）"
            width="110"
          />
          <el-table-column
            prop="createTime"
            label="发票申请时间"
            width="150"
          />
          <el-table-column
            prop="projectName"
            label="项目负责人"
            width="150"
          />
          <el-table-column
            prop="auditState"
            label="开票状态"
          >
            <template slot-scope="scope">
              {{ auditState[scope.row.auditState] }}
            </template>
          </el-table-column>

          <el-table-column
            prop="levyName"
            label="开票方"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
          v-if="this.flag ==true"
            prop="saleName"
            label="所属销售"
            width="150"
          />
          <el-table-column
          v-if="this.flag ==true"
            prop="agentName"
            label="所属代理"
            width="150"
          />
          <el-table-column
            prop="uploadTime"
            label="发票开具时间"
            width="150"
          />
          <el-table-column
            prop="auditRemark"
            label="审核情况"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="appChannel"
            label="申请渠道"
          >
            <template slot-scope="scope">
              {{ scope.row.appChannel === '1' ? '商户' : '运营' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="invCategory"
            label="开票类型"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invCategory == '1'" class="invoice-ordinary">普票</span>
              <span v-if="scope.row.invCategory == '2'" class="invoice-specially">专票</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="invPreopen"
            label="是否预开"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invPreopen == '1'" style="color: #f54343">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="操作"
            width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="jumpDetail(scope.row.id)">查看详情</el-button>
              <!--              <a :href="scope.row.listAtt[0].filePath" download="aaa" v-if="scope.row.listAtt.length>0">下载</a>-->
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    开票初审-->
    <!--    <el-dialog title="开票初审" :visible.sync="dialogA" width="40%" class="reset-dialog reset-dialog-small ">-->
    <!--      <el-scrollbar class="dialog-scroll">-->
    <!--        <el-form :model="dialogAData" label-width="100px" size="mini" class="form-style ">-->
    <!--          <el-form-item label="商户名称" class="form-items">-->
    <!--            北京芬香科技有限公司-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="开票金额" class="form-items">-->
    <!--            87367元-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="开票类目" class="form-items">-->
    <!--            *现代服务*信息技术服务-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="是否预开" class="form-items">-->
    <!--            是-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="*备注" class="form-items">-->
    <!--            <el-input v-model="dialogAData.note" type="textarea" :rows="2" placeholder="请输入内容" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item>-->
    <!--            <div style="text-align: right">-->
    <!--              <el-button type="primary">通过</el-button>-->
    <!--              <el-button @click="dialogczlr=false">不通过</el-button>-->
    <!--            </div>-->
    <!--          </el-form-item>-->
    <!--        </el-form>-->
    <!--      </el-scrollbar>-->
    <!--    </el-dialog>-->

    <!--    充值初审-->
    <!--    <el-dialog title="充值初审" :visible.sync="dialogtrial" width="40%" class="reset-dialog reset-dialog-small">-->
    <!--      <el-scrollbar class="dialog-scroll">-->
    <!--        <el-form :model="dialogtrialData" label-width="110px" size="mini" class="form-style">-->
    <!--          <el-form-item label="企业名称">-->
    <!--            <el-select v-model="dialogtrialData.region" placeholder="请选择活动区域" class="form-items">-->
    <!--              <el-option label="区域一" value="shanghai" />-->
    <!--              <el-option label="区域二" value="beijing" />-->
    <!--            </el-select>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="代征主体">-->
    <!--            <el-select v-model="dialogtrialData.region" placeholder="请选择活动区域" class="form-items">-->
    <!--              <el-option label="区域一" value="shanghai" />-->
    <!--              <el-option label="区域二" value="beijing" />-->
    <!--            </el-select>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="充值通道">-->
    <!--            <el-select v-model="dialogtrialData.region" placeholder="请选择活动区域" class="form-items">-->
    <!--              <el-option label="区域一" value="shanghai" />-->
    <!--              <el-option label="区域二" value="beijing" />-->
    <!--            </el-select>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="充值账号">-->
    <!--            <div>302938734322424</div>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="充值金额">-->
    <!--            <el-input v-model="dialogtrialData.settNum" autocomplete="off" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="* 备注">-->
    <!--            <el-input type="textarea" :rows="2" v-model="dialogtrialData.note" autocomplete="off" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item>-->
    <!--            <div style="text-align: right">-->
    <!--              <el-button type="primary">通过</el-button>-->
    <!--              <el-button @click="dialogtrial=false">不通过</el-button>-->
    <!--            </div>-->
    <!--          </el-form-item>-->
    <!--        </el-form>-->
    <!--      </el-scrollbar>-->
    <!--    </el-dialog>-->

    <!--    <el-dialog title="结算初审" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">-->
    <!--      <el-scrollbar class="dialog-scroll">-->
    <!--        <el-form :model="dialogData" :label-width="formLabelWidth" size="mini" class="form-style">-->
    <!--          <el-form-item label="商户名称">-->
    <!--            <el-select v-model="dialogForm.region" placeholder="请选择活动区域" class="form-items">-->
    <!--              <el-option label="区域一" value="shanghai" />-->
    <!--              <el-option label="区域二" value="beijing" />-->
    <!--            </el-select>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="姓      名">-->
    <!--            <el-input v-model="dialogData.freeMoney" autocomplete="off" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="身份证号">-->
    <!--            <el-input v-model="dialogData.settNum" autocomplete="off" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="手机号">-->
    <!--            <el-input v-model="dialogData.note" autocomplete="off" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="银行卡号">-->
    <!--            <el-input v-model="dialogData.payPassword" autocomplete="off" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="是否海外或港澳台用户">-->
    <!--            <el-radio v-model="dialogData.radio" label="1">是</el-radio>-->
    <!--            <el-radio v-model="dialogData.radio" label="2">否</el-radio>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="证件号">-->
    <!--            <el-input v-model="dialogData.payPassword" show-password autocomplete="off" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="身份证正面信息" />-->
    <!--          <el-form-item label="身份证正面信息" />-->
    <!--          <el-form-item label="签约状态">-->
    <!--            <el-select v-model="dialogForm.region" placeholder="请选择活动区域" class="form-items">-->
    <!--              <el-option label="已签约" value="1" />-->
    <!--              <el-option label="已解约" value="2" />-->
    <!--            </el-select>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="备注">-->
    <!--            <el-input v-model="dialogForm.note" type="textarea" :rows="2" placeholder="请输入内容" />-->
    <!--          </el-form-item>-->
    <!--          <el-form-item>-->
    <!--            <div style="text-align: right">-->
    <!--              <el-button type="primary">保存</el-button>-->
    <!--              <el-button>取消</el-button>-->
    <!--            </div>-->
    <!--          </el-form-item>-->
    <!--        </el-form>-->
    <!--      </el-scrollbar>-->
    <!--    </el-dialog>-->
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import invoiceType from '@/axios/default/invoiceType'
import publicApi from '@/axios/default/public'
import { parseTime, newAlert, downloadFile } from '@/utils'
export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      pickTime1: '',
      flag:true,
      auditState: { 0: '待提交', 1: '初审', 2: '初审拒绝', 3: '复审', 4: '复审拒绝', 5: '开票中', 6: '开票审核', 7: '等待邮寄', 8: '开票作废', 9: '邮寄完成', 10: '风控审核中', 11: '风控审核拒绝', 12: '下月审核'},
      formData: {
        'createTimeFrom': '',
        'createTimeTo': '',
        'auditState': '',
        'merName': '',
        'invPreopen': '',
        'exportFlag':'',
        levyId: '',
        projectName: '',
        saleName:'',
        agentName:''
      },
      formLabelWidth: '180px',
      listData: [],
      invoiceData: {
        totalInvAmount: 0,
        successInvAmount: 0,
        failInvAmount: 0,
        dealInvAmount: 0
        },
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            const end = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
            const start = new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogA: false,
      dialogAData: {},
      dialogB: false,
      dialogBData: {},
      dialogC: false,
      dialogCData: {},
      levyBodyIdArr: []
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
    this.pageName()
    this.queryInvoiceInfo()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryInvoiceInfo()
    },
    queryInvoiceInfo() {
      invoiceType.queryInvoiceInfo({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'InvoiceInfo': {
          'createTimeFrom': this.pickTime[0],
          'createTimeTo': this.pickTime[1],
          'uploadTimeFrom': this.pickTime1[0],
          'uploadTimeTo': this.pickTime1[1],
          'auditState': this.formData.auditState,
          'merName': this.formData.merName,
          'invPreopen': this.formData.invPreopen,
          'levyId': this.formData.levyId,
          'projectName': this.formData.projectName,
          'saleName': this.formData.saleName,
          'agentName': this.formData.agentName

        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum

        this.invoiceData.totalInvAmount = res.data.data.totalInvAmount
        this.invoiceData.successInvAmount = res.data.data.successInvAmount
        this.invoiceData.dealInvAmount = res.data.data.dealInvAmount
        this.invoiceData.failInvAmount = res.data.data.failInvAmount
      })
    },
    pageName() {
      const page = this.$route.name
      if (page == '开票申请') {
        this.invPreopen = 2
      } else if (page == '预开票申请') {
        this.invPreopen = 1
      }
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryInvoiceInfo()
    },
    jumpDetail(id) {
      this.$router.push({
        path: '/invoice/detail',
        query: {
          origin: 'list',
          type: 'view',
          id
        }
      })
    },
    toApply() {
      this.$router.push({
        path: '/invoice/apply'
      })
    },
    redFlushInvoice() {
      this.$router.push({
        path: '/invoice/redFlushInvoice'
      })
    },
    toYuinvoice() {
      this.$router.push({
        path: '/invoice/yuinvoice'
      })
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
            this.flag =false

          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    downInvoiceInfoOne() {
      this.formData.exportFlag = '1',
      this.downInvoiceInfo();
    },
    downInvoiceInfo() {
      invoiceType.downInvoiceInfo({
        'createTimeFrom': this.pickTime[0],
        'createTimeTo': this.pickTime[1],
        'uploadTimeFrom': this.pickTime1[0],
        'uploadTimeTo': this.pickTime1[1],
        'exportFlag':this.formData.exportFlag,

        'auditState': this.formData.auditState,
        'merName': this.formData.merName,
        'invPreopen': this.formData.invPreopen,
        'levyId': this.formData.levyId,
        'projectName': this.formData.projectName,
        'saleName': this.formData.saleName,
        'agentName': this.formData.agentName
      }, `开票列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },
    goProject(args) {
      this.$router.push({ path: '/balance/list', query: { batchNum: args }})
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .table-headers {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  /deep/ .el-form-item__content{
    margin-left: 0!important;
  }

  .table-head-money .el-col .table-headers {
    display: flex;
    flex-flow: nowrap column;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #eff2f6;
    .table-header-num {
      font-size: 18px;
      margin-bottom: 5px;
      color: #333;
    }
    .table-header-name {
      font-size: 12px;
      color: #999;
    }
  }

  .table-head-money .el-col:last-child .table-headers {
    border-right: none;
  
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .invoice-ordinary{
    color: #f29c39;
  }
  .invoice-specially{
    color: #5791fd;
  }
  .goProject {
    color: #2b66fd;
    cursor: pointer;
  }
</style>
