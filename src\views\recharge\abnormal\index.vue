<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">

                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值时间起" class="form-items" prop="createTimeFrom">
                    <el-date-picker
                      v-model="formData.createTimeFrom"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值时间止" class="form-items" prop="createTimeTo">
                    <el-date-picker
                      v-model="formData.createTimeTo"
                      class="auto-width"
                      type="datetime"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :clearable="false"
                      :editable="false"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="打款名称" class="form-items">
                    <el-input v-model="formData.rechName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="收款名称" class="form-items">
                    <el-input v-model="formData.levyName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">

                <!--                <el-col :lg="7" :xs="24">-->
                <!--                  <el-form-item label="充值状态" class="form-items">-->
                <!--                    <el-input v-model="formData.receiveName" placeholder="请输入内容" clearable />-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->
                <el-col :lg="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="rechName"
            label="打款名称"
            width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="rechMoney"
            label="打款金额"
          />
          <el-table-column
            prop="channelName"
            label="打款银行"
            width="200"
          />
          <el-table-column
            prop="levyName"
            label="收款名称"
            width="200"
          />
          <el-table-column
            prop="receiveBank"
            label="收款银行"
            width="200"
          />
          <el-table-column
            prop="receiveBankNo"
            width="200"
            label="收款账号"
          />
          <el-table-column
            prop="rechTime"
            width="150"
            label="充值时间"
          />

          <el-table-column
            prop="rechState"
            label="充值状态"
          >
            <template slot-scope="scope">
              {{ rechState[scope.row.rechState] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            width="120"
            fixed="right"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="abView(scope.row)">查看</el-button>
              <el-button v-if="scope.row.rechState == '0'" type="text" @click="abAudit(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
    <!--    异常充值审核-->
    <el-dialog title="异常充值审核" :visible.sync="dialogabnormal" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogabnormalData" :model="dialogabnormalData" label-width="120px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称">
            <el-input v-model="dialogabnormalData.merName" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="打款名称">
            <el-input v-model="dialogabnormalData.rechName" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="打款金额">
            <el-input v-model="dialogabnormalData.rechMoney" autocomplete="off" disabled>
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="项目负责人">
            <el-input v-model="dialogabnormalData.projectName" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="充值时间">
            <el-input v-model="dialogabnormalData.rechTime" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="商户订单号">
            <el-input v-model="dialogabnormalData.merOrderNumber" autocomplete="off" />
            <span style="font-size: 12px">如是自然人退款，则需要填写结算成功的订单号，否则不需要填写</span>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dialogabnormalData.remark" type="textarea" :rows="4" autocomplete="off" />
          </el-form-item>

          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" @click="pushMoeny('dialogabnormalData')">充值</el-button>
              <el-button @click="backMoney('dialogabnormalData')">退款</el-button>
              <el-button @click="dialogabnormal = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

    <!--    异常充值查看-->
    <el-dialog title="异常充值查看" :visible.sync="dialogabnormalInfo" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form label-width="120px" size="mini" class="form-style">
          <el-form-item label="企业名称">
            <el-input v-model="dialogabnormalData.merName" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="打款名称">
            <el-input v-model="dialogabnormalData.rechName" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="打款金额">
            <el-input v-model="dialogabnormalData.rechMoney" autocomplete="off" disabled>
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="项目负责人">
            <el-input v-model="dialogabnormalData.projectName" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="充值时间">
            <el-input v-model="dialogabnormalData.rechTime" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="打款银行">
            <el-input v-model="dialogabnormalData.channelName" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="打款账号">
            <el-input v-model="dialogabnormalData.rechBankNo" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="收款银行">
            <el-input v-model="dialogabnormalData.receiveBank" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="收款账号">
            <el-input v-model="dialogabnormalData.receiveBankNo" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="dialogabnormalData.remark" type="textarea" :rows="4" autocomplete="off" disabled />
          </el-form-item>

          <el-form-item>
            <div style="text-align: right">
              <el-button @click="dialogabnormalInfo = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import rechInfo from '@/axios/default/rechInfo'
import moment from 'moment'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        'merName': '',
        'rechName': '',
        'levyName': ''
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogabnormal: false,
      dialogabnormalData: {},
      dialogabnormalInfo: false,
      rechState: { '0': '未处理', '1': '处理中', '2': '已充值', '3': '已退款' },
      rules: {
        remark: { required: true, message: '请填写备注', trigger: 'blur' }
      }
    }
  },
  mounted() {
    this.queryRechAbNormalByPage()
  },
  methods: {
    moment,
    onSearch() {
      this.pageNum = 1
      this.queryRechAbNormalByPage()
    },
    // 列表
    queryRechAbNormalByPage() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      rechInfo.queryRechAbnormalInfo({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'abnormal': {
          'createTimeFrom': this.formData.createTimeFrom,
          'createTimeTo': this.formData.createTimeTo,
          'merName': this.formData.merName,
          'rechName': this.formData.rechName,
          'levyName': this.formData.levyName
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    // 异常查看
    abView(data) {
      this.dialogabnormalInfo = true
      this.dialogabnormalData = data
    },
    // 异常审核
    abAudit(data) {
      this.dialogabnormal = true
      this.dialogabnormalData = data
      this.dialogabnormalData.remark = ''
      this.dialogabnormalData.merOrderNumber = ''
    },
    // 充值
    pushMoeny(formName, data) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            'id': this.dialogabnormalData.id,
            'merOrderNumber': this.dialogabnormalData.merOrderNumber,
            'remark': this.dialogabnormalData.remark,
            'btnType': '1'
          }
          this.updateRechAbnomal(params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 退款
    backMoney(formName) {
      // if (this.dialogabnormalData.merOrderNumber === '') {
      //   this.$message({ type: 'error', message: '请填写商户订单号' })
      //   return false
      // }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            'id': this.dialogabnormalData.id,
            'merOrderNumber': this.dialogabnormalData.merOrderNumber,
            'remark': this.dialogabnormalData.remark,
            'btnType': '2'
          }
          this.updateRechAbnomal(params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    updateRechAbnomal(data) {
      rechInfo.updateRechAbnomal(data).then(res => {
        console.log(res.data.data)
        if (res.data.code === '0000') {
          this.dialogabnormal = false
          this.$message({ type: 'success', message: '操作成功' })
          this.queryRechAbNormalByPage()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryRechAbNormalByPage()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
</style>
