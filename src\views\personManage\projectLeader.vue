<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="姓名" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="18" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-add" type="primary" @click="addData">新增</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="name"
            label="姓名"
          />
          <el-table-column
            prop="mobile"
            label="手机号"
          />
          <el-table-column
            prop="email"
            label="邮箱"
          />
          <el-table-column
            prop="createTime"
            label="开通时间"
          />
          <el-table-column
            prop=""
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="editData(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog :title="dialogFormTitle" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="actionForm" :model="dialogData" :label-width="formLabelWidth" size="mini" class="form-style" :rules="rules">

          <el-form-item label="姓名" prop="name">
            <el-input v-model="dialogData.name" autocomplete="off" />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="dialogData.mobile" autocomplete="off" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="dialogData.email" autocomplete="off" />
          </el-form-item>

          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" @click="submitData('actionForm')">保存</el-button>
              <el-button @click="dialogFormVisible = false">取消</el-button>
            </div>
          </el-form-item>

        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import proleader from '@/axios/default/projectLeader'
// import moment from 'moment'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

var checkPhone = (rule, value, callback) => {
  const reg = /^1[0-9]\d{9}$/
  if (reg.test(value)) {
    callback()
  } else {
    return callback(new Error('请输入正确的手机号'))
  }
}
export default {
  name: 'ProjectLeader',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        name: ''
      },
      listData: [],
      dialogData: this.initdialogData(),
      actionType: '',
      dialogFormTitle: '',
      dialogFormVisible: false,
      formLabelWidth: '100px',
      rules: {
        name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
        mobile: [
          { required: true, message: '请填写手机号', trigger: 'blur' },
          { validator: checkPhone, trigger: ['blur', 'change'] }
        ],
        email: [
          { required: true, message: '请填写邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请填写正确的邮箱地址', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  mounted() {
    this.proLeaderList()
  },
  methods: {
    // moment,
    initdialogData(data) {
      if (data) {
        return {
          id: data.id,
          name: data.name,
          mobile: data.mobile,
          email: data.email
        }
      } else {
        return {
          id: '',
          name: '',
          mobile: '',
          email: ''
        }
      }
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.proLeaderList()
    },
    onSearch() {
      this.pageNum = 1
      this.proLeaderList()
    },
    // 列表数据
    proLeaderList() {
      proleader.proLeaderList({
        name: this.formData.name,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
      })
    },
    addData() {
      this.dialogFormVisible = true

      this.dialogData = this.initdialogData()
      this.$nextTick(function() {
        this.resetForm('actionForm')
      })
      this.dialogFormTitle = '新增负责人'
      this.actionType = '1' // 添加
    },
    editData(data) {
      this.dialogFormVisible = true
      this.$nextTick(function() {
        this.dialogData = this.initdialogData(data)
        this.resetForm('actionForm')
      })
      this.dialogFormTitle = '修改负责人'
      this.actionType = '2' // 添加
    },
    submitData(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.actionType == '1') {
            this.addProjectLeader()
          } else {
            this.updateProjectLeader()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addProjectLeader() {
      proleader.addProjectLeader(this.dialogData).then(res => {
        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.proLeaderList()
          this.resetForm('actionForm')
          this.$message({
            message: '操作成功',
            type: 'success'
          })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    updateProjectLeader() {
      proleader.updateProjectLeader(this.dialogData).then(res => {
        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.proLeaderList()
          this.resetForm('actionForm')
          this.$message({
            message: '操作成功',
            type: 'success'
          })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

</style>
