<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
    
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="所属月份" class="form-items">
                    <el-date-picker
                      v-model="formData.month"
                      class="auto-width"
                      value-format="yyyy-MM"
                      type="month"
                      placeholder="选择日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" :disabled="requestLoading" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table :data="tableData" border stripe size="mini" class="list-table">
          <el-table-column v-for="(item,index) in listHeader" :key="index" :width="item.width" :fixed="item.fixed" :label="item.name">
            <template slot-scope="scope">
              <div v-if="item.key == 'action'">
                <el-button type="text" size="mini" class="btn-edit" @click="toEdit(scope.row.id)">编辑</el-button>
                <template>
                  <el-popconfirm title="确定删除吗？" @onConfirm="deleteData(scope.row.id,scope.row.deleteFlag=='1')">
                    <el-button slot="reference" v-hasPermission="['merchants:list-del']" type="text" size="mini" class="btn-del">删除</el-button>
                  </el-popconfirm>
                </template>
              </div>
              <div v-else>
                <span v-if="item.key == 'status'">
                  {{ liststatus[scope.row[item.key]] }}
                </span>
                <span v-else>{{ scope.row[item.key] }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>


        <!--    新增企业-->
        <el-dialog :title= "title" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称" prop="merName">
            <el-select
              v-model="dialogData.merName"
              class="auto-width"
              filterable
              remote
              placeholder="请输入关键词"
              :remote-method="mccList1"
              :loading="loading"
            >
              <el-option v-for="(item1,index1) in merIdArr1" :key="index1" :label="item1.merName" :value="item1.merName" />
            </el-select>
          </el-form-item>

          <el-form-item label="实际费率" prop="newFeeRate">
            <el-input v-model="dialogData.newFeeRate" type="text"  placeholder="请输入实际费率" autocomplete="off" class="auto-width" />
          </el-form-item> 
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" :loading="btnLoading" @click="submit()">保存</el-button>
              <el-button :loading="btnLoading" @click="dialogFormVisible=false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import merchants from '@/axios/default/merchants'
import levyBody from '@/axios/default/levyBody'
import Pagination from '@/components/Pagination'
import publics from '@/axios/default/public'

import moment from 'moment'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'MerchantsList',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      liststatus: { 0: '编辑', 1: '初审中', 2: '复审中', 3: '正常', 4: '预开', 5: '停用', 6: '渠道审批中', 7: '风控审批中', 8: '运营审批中', 9: '观察者', 10: '已拉黑', 11: '已加白' },
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        status: '',
        merName: '',
        groupMerName: '',
        leaderName: '',
        saleName: '',
        agentName:'',
        levyId: '',
        groupMerNo: '',
        month: moment().subtract(1, 'months').format('YYYY-MM')
      },
      
      levyBodyIdArr: [],
      title:'',
      listHeader: [
      {
        name: '企业名称',
        width: '350',
        key: 'merName'
      },
      
      {
        name: '系统设置费率',
        width: '200',
        key: 'feeRate'
      },
      {
        name: '实际费率',
        width: '200',
        key: 'newFeeRate'
      },
      {
        name: '所属月份',
        width: '200',
        key: 'month'
      },
      {
        name: '代征主体',
        width: '200',
        key: 'levyName'
      },
      {
        name: '上月需要奖励金额',
        width: '200',
        key: 'lastMonthSettAmount'
      },
       {
        name: '操作',
        key: 'action',
        width: '200',
        fixed: 'right'
      }],
      tableData: [],
      levyBodyOpt: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },

      dialogData: {
        merName: '', // 企业名称
        groupMerName: '', // 集团企业名称
        sameCompany: '' //是否同一公司
      },
      merIdArr: [],
      merIdArr1: [],
      dialogFormVisible: false,
      addListDialog: false,
      btnLoading: false,
      loading: false,
      requestLoading: false,
      dialogType: '',
      dialogName: '',
      rules: {
        merName: { required: true, message: '请选择企业名称', trigger: 'change' },
        groupMerName: { required: true, message: '请选择代征主体', trigger: 'change' },
        sameCompany: { required: true, message: '请选择充值通道', trigger: 'change' }
      }
    }
  },
  mounted() {
    this.initlevyBody()
    this.mccList()
    this.mccList1()
    this.queryLevyBodyInfos()


  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      this.requestLoading = true
      merchants.querySecondCommissionRebateByPage({
        ...this.formData,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.requestLoading = false
          this.tableData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    deleteData(id,deleteFlag) {
      merchants.updateMerchantInfoGroup({ id,deleteFlag }).then(res => {
        if (res.data.code == '0000') {
          const index = this.tableData.findIndex(res => res.id == id)
          this.tableData.splice(index, 1)
          this.$message.success('操作成功')
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initlevyBody() {
      levyBody.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyOpt = res.data.data
          if (res.data.data.length > 1) {
            this.levyBodyOpt.unshift({ name: '请选择', id: '' })
          } else {
            this.formData.levyId = this.levyBodyOpt[0].id
          }
        }
        this.list()
      })
    },

    openBox() {
      this.title = '新增集团企业'
      this.dialogType = 'add'
      this.dialogFormVisible = true
      this.dialogData = this.initDialogData()
    },
    // 开通禁用
    // updateStatus(data) {
    //   const { row, status } = data
    //   merchants.updateStatus({
    //     id: row.id,
    //     status
    //   }).then(res => {
    //     if (res.data.code == '0000') {
    //       this.$message.success('操作成功')
    //       this.list()
    //     } else {
    //       newAlert(this.$tips, res.data.message)
    //     }
    //   })
    // },


    // 提交
    submit() {
      console.log(this.dialogData)
          this.btnLoading = true
          if (this.dialogType === 'add') {
            merchants.addMerchantInfoGroup(this.dialogData).then(res => {
              if (res.data.code === '0000') {
                this.btnLoading = false
                this.addListDialog = false
                this.$refs.dialogData.resetFields()
                this.dialogFormVisible = false
                this.list()
                this.$message({ type: 'success', message: '操作成功' })
              } else {
                this.btnLoading = false
                newAlert(this.$tips, res.data.message)
              }
            })
          } else {
            merchants.updateMerchantInfoGroup({...this.dialogData}).then(res => {
                if (res.data.code == '0000') {
                this.dialogFormVisible = false
                this.$refs.dialogData.resetFields()

                 this.btnLoading = false
                 this.list()
                 this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
          }


    },
    // 关闭
    closeDialog(formVisible, formName) {
      this[formVisible] = false
      this.$refs[formName].resetFields()
    },

    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    addMerchant() {

    },
    toEdit(id) {
      this.title = '编辑集团企业'
      this.dialogType = 'edit'
      this.dialogFormVisible = true
      //this.dialogData = this.initDialogData()
      merchants.queryMerchantInfoGroupById({
        id
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogData = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })

    },
    initDialogData(){
      return{
        merName: '', // 企业名称
        groupMerName: '', // 集团企业名称
        sameCompany: '' //是否同一公司
      }
    },
    mccList(query) {
      if (query !== '') {
        this.loading = true
        publics.merAllList({
          merName: query
        }).then(res => {
          this.loading = false
          this.merIdArr = res.data.data
        })
      } else {
        this.merIdArr = []
      }
    },
    mccList1(query) {
      if (query !== '') {
        this.loading = true
        publics.merAllList({
          merName: query
        }).then(res => {
          this.loading = false
          this.merIdArr1 = res.data.data
        })
      } else {
        this.merIdArr1 = []
      }
    },
    queryLevyBodyInfos() {
      publics.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    merDownLoadData() {
      merchants.merDownLoadData({
        ...this.formData
      }, `商户列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card {
  margin-bottom: 20px;
}

.list-table {
  margin: 0 0 20px 0;
  .el-button {
    padding: 0;
  }
}
.auto-width {
  width: 100%;
}
</style>
