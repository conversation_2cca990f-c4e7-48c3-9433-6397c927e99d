import db from '@/utils/localstorage'
import merchants from '@/axios/default/merchants'

const state = {
  createMerchantStep: db.get('CREAT_MER_STEP', 1),
  rowId: db.get('CREAT_MER_ID', null),
  formData: db.get('CREAT_EDIT_DATA', {}),
  saveFlag: 0,
  fileList: [] // 商户资质文件
}

const mutations = {
  setCreateMerchantStep(state, val) {
    db.save('CREAT_MER_STEP', val)
    state.createMerchantStep = val
  },
  setCreatMerchantFormData(state, data = {}) {
    state.formData = data
    db.save('CREAT_EDIT_DATA', data)
  },
  setMearChantsId(state, id) {
    state.rowId = id
    db.save('CREAT_MER_ID', id)
  },
  setSaveFlag(state, data) {
    state.saveFlag = data
    // db.save('MERCHANT_SAVE_Flag', data)
  },
  setFileList(state, data) {
    state.fileList = data
  }
}

const actions = {
  // 根据id取商户信息
  getCreatMearchant({ commit }, id) {
    return new Promise(resolve => {
      merchants.getOneMerchantInfo({ id: Number(id) }).then(res => {
        resolve(res)
      }).catch(error => {
        commit('setCreatMerchantFormData', {})
      })
    })
  },
  // 添加数据
  addMerchantInfo({ commit }, data) {
    return new Promise(resolve => {
      merchants.addMerchantInfo(data).then(res => {
        resolve(res)
      })
    })
  },
  // 修改数据
  updateMerchantInfo({ commit }, data) {
    return new Promise(resolve => {
      merchants.updateMerchantInfo(data).then(res => {
        resolve(res)
      })
    })
  },
  setFileList({ commit }, data) {
    commit('setFileList', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
