<!--<template>-->
<!--  <div class="content-main">-->
<!--    <div class="general-layout">-->
<!--      <el-card class="list-card">-->
<!--        <el-row :gutter="20">-->
<!--          <el-col :span="24">-->
<!--            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">-->
<!--              <el-row :gutter="20">-->
<!--                <el-col :lg="7" :xs="24">-->
<!--                  <el-form-item label="企业名称" class="form-items">-->
<!--                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :lg="5" :xs="24">-->
<!--                  <el-form-item label="签约状态" class="form-items">-->
<!--                    <el-select v-model="formData.signingStatus" placeholder="请选择" class="auto-width">-->
<!--                      <el-option label="签约" :value="1" />-->
<!--                      <el-option label="已签约" :value="2" />-->
<!--                    </el-select>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :lg="5" :xs="24">-->
<!--                  <el-form-item label="姓名" class="form-items">-->
<!--                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :lg="7" :xs="24">-->
<!--                  <el-form-item label="身份证号" class="form-items">-->
<!--                    <el-input v-model="formData.idCard" placeholder="请输入内容" clearable />-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--              <el-row :gutter="20">-->
<!--                <el-col :lg="10" :xs="24">-->
<!--                  <el-form-item label="签约时间" class="form-items">-->
<!--                    <el-date-picker-->
<!--                      v-model="pickTime"-->
<!--                      class="auto-width"-->
<!--                      type="daterange"-->
<!--                      align="right"-->
<!--                      unlink-panels-->
<!--                      value-format="yyyy-MM-dd"-->
<!--                      range-separator="-"-->
<!--                      start-placeholder="开始时间"-->
<!--                      end-placeholder="结束时间"-->
<!--                      :picker-options="pickerOptions"-->
<!--                    />-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :lg="6" :xs="24">-->
<!--                  <el-form-item label="证件号" class="form-items">-->
<!--                    <el-input v-model="formData.certNumber" placeholder="请输入内容" clearable />-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :lg="8" :xs="24">-->
<!--                  <el-form-item class="form-items" style="text-align:right">-->
<!--                    <el-button icon="el-icon-search" type="primary" @click="freelancerInfoList">搜索</el-button>-->
<!--                    <el-button icon="el-icon-add" type="primary" @click="freelancerDownLoadData">下载自由职业者信息</el-button>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--              <el-row :gutter="20" style="margin-top: 40px">-->
<!--                <el-col :span="24">-->
<!--                  <el-form-item class="form-items" style="text-align:right;display: flex">-->
<!--                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增</el-button>-->
<!--                    <el-button icon="el-icon-upload" plain type="primary" @click="isCheckBox">批量新增</el-button>-->
<!--                    <el-button icon="el-icon-download" plain type="primary">-->
<!--                      <a :href="downHosts()">批量新增模版下载</a>-->
<!--                    </el-button>-->
<!--                    <el-button icon="el-icon-download" plain type="primary" @click="downloadContract">批量下载合同</el-button>-->
<!--                    <el-button icon="el-icon-download" plain type="primary" @click="downloadCard">下载身份证照片</el-button>-->
<!--                    <el-upload-->
<!--                      ref="uploadImport"-->
<!--                      class="upload-more"-->
<!--                      action=""-->
<!--                      :http-request="(params => uploadImport(params))"-->
<!--                      :show-file-list="false"-->
<!--                      accept=".zip"-->
<!--                    >-->
<!--                      <el-button icon="el-icon-upload" plain type="primary">上传身份证照片</el-button>-->
<!--                    </el-upload>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--            </el-form>-->
<!--          </el-col>-->
<!--        </el-row>-->

<!--        <el-table-->
<!--          :data="listData"-->
<!--          border-->
<!--          size="mini"-->
<!--          class="list-table"-->
<!--        >-->
<!--          <el-table-column-->
<!--            prop="merName"-->
<!--            label="企业名称"-->
<!--            width="250"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="name"-->
<!--            label="姓名"-->
<!--            width="150"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="idCard"-->
<!--            label="身份证号"-->
<!--            width="200"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="bankCardNo"-->
<!--            label="卡号"-->
<!--            width="250"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="mobile"-->
<!--            label="手机号"-->
<!--            width="150"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="isHmt"-->
<!--            width="150"-->
<!--            label="是否海外或港澳台用户"-->
<!--          >-->
<!--            <template slot-scope="scope">-->
<!--              {{ scope.row.isHmt === '1' ? '是' : '否' }}-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="certNumber"-->
<!--            width="200"-->
<!--            label="证件号"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="signingStatus"-->
<!--            label="签约状态"-->
<!--            width="150"-->
<!--          >-->
<!--            <template slot-scope="scope">-->
<!--              {{ signingStatus[scope.row.signingStatus] }}-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="signingTime"-->
<!--            width="150"-->
<!--            label="签约时间"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="address"-->
<!--            label="操作"-->
<!--            fixed="right"-->
<!--            width="140"-->
<!--          >-->
<!--            <template slot-scope="scope">-->
<!--              <a class="tb-btn-normal mar-right" type="text" @click="edit(scope.row.id)">修改</a>-->
<!--              <a v-if="scope.row.filePath" class="tb-btn-normal mar-right" type="text" :href="scope.row.filePath">下载合同</a>-->
<!--              &lt;!&ndash;              <el-button type="text" v-if="scope.row.signingStatus=='2'" @click="delData(scope.row.id)">删除</el-button>&ndash;&gt;-->
<!--              <el-popconfirm title="确定删除吗？" @onConfirm="delData(scope.row.id)">-->
<!--                <a slot="reference" class="tb-active-red">删除</a>-->
<!--              </el-popconfirm>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->
<!--        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />-->
<!--      </el-card>-->
<!--    </div>-->

<!--    <el-dialog title="" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">-->
<!--      <el-scrollbar class="dialog-scroll">-->
<!--        <el-form ref="dialogData" :model="dialogData" :rules="rules" :label-width="formLabelWidth" size="mini" class="form-style">-->
<!--          <el-form-item label="商户名称" prop="merId">-->
<!--            &lt;!&ndash;            <el-select v-model="dialogData.merId" placeholder="请选择商户名称" class="auto-width">&ndash;&gt;-->
<!--            &lt;!&ndash;              <el-option v-for="(item,index) in merIdArr" :key="index" :label="item.merName" :value="item.id" />&ndash;&gt;-->
<!--            &lt;!&ndash;            </el-select>&ndash;&gt;-->
<!--            <el-select-->
<!--              v-model="dialogData.merId"-->
<!--              class="auto-width"-->
<!--              filterable-->
<!--              clearable-->
<!--              remote-->
<!--              placeholder="请输入关键词"-->
<!--              :remote-method="merAllList"-->
<!--              :loading="loading"-->
<!--            >-->
<!--              <el-option v-for="item in merIdArr" :key="item.id" :label="item.merName" :value="item.id" />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="姓      名" prop="name">-->
<!--            <el-input v-model="dialogData.name" autocomplete="off" />-->
<!--          </el-form-item>-->

<!--          <el-form-item label="手机号" prop="mobile">-->
<!--            <el-input v-model="dialogData.mobile" autocomplete="off" />-->
<!--          </el-form-item>-->
<!--          <el-form-item label="银行卡号" prop="bankCardNo">-->
<!--            <el-input v-model="dialogData.bankCardNo" autocomplete="off" />-->
<!--          </el-form-item>-->
<!--          <el-form-item label="是否海外或港澳台用户" prop="isHmt">-->
<!--            <el-radio v-model="dialogData.isHmt" :label="1" @change="resetFormSub('dialogData')">是</el-radio>-->
<!--            <el-radio v-model="dialogData.isHmt" :label="0" @change="resetFormSub('dialogData')">否</el-radio>-->
<!--          </el-form-item>-->
<!--          <el-form-item v-if="dialogData.isHmt == '0'" label="身份证号" prop="idCard">-->
<!--            <el-input v-model="dialogData.idCard" autocomplete="off" />-->
<!--          </el-form-item>-->
<!--          <el-form-item v-if="dialogData.isHmt == '1'" label="证件号" prop="certNumber">-->
<!--            <el-input v-model="dialogData.certNumber" autocomplete="off" />-->
<!--          </el-form-item>-->
<!--          <el-form-item v-if="dialogData.isHmt == '0'" label="身份证正面信息" prop="">-->
<!--            <el-upload-->
<!--              ref="upload"-->
<!--              class="idCardFrontPath"-->
<!--              :show-file-list="false"-->
<!--              action=""-->
<!--              accept=".jpeg,.jpg"-->
<!--              :http-request="(params => uprequest(params,'idCardFrontPath'))"-->
<!--            >-->
<!--              <i v-if="!dialogData.idCardFrontPath" slot="default" class="el-icon-plus" />-->
<!--              <img v-else :src="idCardFrontPath" alt="" class="preview">-->
<!--            </el-upload>-->
<!--          </el-form-item>-->
<!--          <el-form-item v-if="dialogData.isHmt == '0'" label="身份证背面信息" prop="">-->
<!--            <el-upload-->
<!--              ref="upload"-->
<!--              class="idCardBackPath"-->
<!--              :show-file-list="false"-->
<!--              action=""-->
<!--              accept=".jpeg,.jpg"-->
<!--              :http-request="(params => uprequest(params,'idCardBackPath'))"-->
<!--            >-->
<!--              <i v-if="!dialogData.idCardBackPath" slot="default" class="el-icon-plus" />-->
<!--              <img v-else :src="idCardBackPath" alt="" class="preview">-->
<!--            </el-upload>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="签约状态" prop="signingStatus">-->
<!--            <el-select v-model="dialogData.signingStatus" placeholder="请选择签约状态" class="auto-width">-->
<!--              <el-option label="未签约" value="0" />-->
<!--              <el-option label="已签约" value="1" />-->
<!--              <el-option label="解约" value="2" />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="备注" prop="remark">-->
<!--            <el-input v-model="dialogData.remark" type="textarea" :rows="2" placeholder="请输入内容" />-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <div style="text-align: right">-->
<!--              <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">保存</el-button>-->
<!--              <el-button :loading="btnLoading" @click="dialogFormVisible = false">取消</el-button>-->
<!--            </div>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
<!--      </el-scrollbar>-->
<!--    </el-dialog>-->

<!--    <el-dialog-->
<!--      title="批量新增"-->
<!--      :visible.sync="checkBoxShow"-->
<!--      width="30%"-->
<!--    >-->
<!--      <el-form size="mini" class="form-style">-->
<!--        <el-form-item label="商户名称" prop="merchantId">-->
<!--          <el-select-->
<!--            v-model="merId"-->

<!--            filterable-->
<!--            clearable-->
<!--            remote-->
<!--            placeholder="请输入关键词"-->
<!--            :remote-method="merAllList"-->
<!--            :loading="loading"-->
<!--          >-->
<!--            <el-option v-for="item in merIdArr" :key="item.id" :label="item.merName" :value="item.id" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="是否鉴权">-->
<!--          <el-radio v-model="isCheck" :label="1">鉴权</el-radio>-->
<!--          <el-radio v-model="isCheck" :label="0">不鉴权</el-radio>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="批量新增">-->
<!--          <el-upload-->
<!--            ref="uploadMore"-->
<!--            class="upload-more"-->
<!--            action=""-->
<!--            :http-request="(params => uploadMore(params))"-->
<!--            :show-file-list="false"-->
<!--            accept=".xlsx,.xls"-->
<!--          >-->
<!--            <el-button icon="el-icon-upload" plain type="primary">批量新增</el-button>-->
<!--          </el-upload>-->
<!--        </el-form-item>-->
<!--      </el-form>-->

<!--    </el-dialog>-->

<!--  </div>-->
<!--</template>-->
<!--<script>-->
<!--import Pagination from '@/components/Pagination'-->
<!--import freelancerInfo from '@/axios/default/freelancerInfo'-->
<!--import publics from '@/axios/default/public'-->
<!--import { downHost } from '@/utils'-->
<!--import moment from 'moment'-->
<!--var checkPhone = (rule, value, callback) => {-->
<!--  const reg = /^1[0-9]\d{9}$/-->
<!--  if (reg.test(value)) {-->
<!--    callback()-->
<!--  } else {-->
<!--    return callback(new Error('请输入正确的手机号'))-->
<!--  }-->
<!--}-->
<!--export default {-->
<!--  name: 'Index',-->
<!--  components: {-->
<!--    Pagination-->
<!--  },-->
<!--  data() {-->
<!--    return {-->
<!--      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,-->
<!--      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,-->
<!--      total: 0,-->
<!--      pickTime: [new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1), new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1)],-->
<!--      formData: {-->
<!--        merName: '',-->
<!--        signingStatus: '',-->
<!--        name: '',-->
<!--        idCard: '',-->
<!--        certNumber: '',-->
<!--        signingTimeFrom: new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1),-->
<!--        signingTimeTo: new Date(),-->
<!--        idCardFrontPath: '',-->
<!--        idCardBackPath: ''-->
<!--      },-->
<!--      dialogData: this.initData(),-->
<!--      dialogFormVisible: true,-->
<!--      formLabelWidth: '180px',-->
<!--      listData: [],-->
<!--      pickerOptions: {-->
<!--        shortcuts: [{-->
<!--          text: '本月',-->
<!--          onClick(picker) {-->
<!--            picker.$emit('pick', [new Date(), new Date()])-->
<!--          }-->
<!--        }, {-->
<!--          text: '今年至今',-->
<!--          onClick(picker) {-->
<!--            const end = new Date()-->
<!--            const start = new Date(new Date().getFullYear(), 0)-->
<!--            picker.$emit('pick', [start, end])-->
<!--          }-->
<!--        }, {-->
<!--          text: '最近六个月',-->
<!--          onClick(picker) {-->
<!--            const end = new Date()-->
<!--            const start = new Date()-->
<!--            start.setMonth(start.getMonth() - 6)-->
<!--            picker.$emit('pick', [start, end])-->
<!--          }-->
<!--        }]-->
<!--      },-->
<!--      signingStatus: { 0: '未签约', 1: '已签约', 2: '解约' },-->
<!--      merIdArr: [],-->
<!--      rules: {-->
<!--        merId: [{ required: true, message: '请选择商户', trigger: 'change' }],-->
<!--        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],-->
<!--        idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],-->
<!--        mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }, { validator: checkPhone, trigger: ['blur', 'change'] }],-->
<!--        bankCardNo: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],-->
<!--        certNumber: [{ required: true, message: '请输入证件号', trigger: 'blur' }],-->
<!--        signingStatus: [{ required: true, message: '请选择签约状态', trigger: 'change' }],-->
<!--        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],-->
<!--        idCardFrontPath: [{ required: true, message: '请上传身份证正面照片', trigger: 'blur' }],-->
<!--        idCardBackPath: [{ required: true, message: '请上传身份证背面照片', trigger: 'blur' }]-->
<!--      },-->
<!--      idCardFrontPath: '',-->
<!--      idCardBackPath: '',-->
<!--      loading: false,-->
<!--      btnLoading: false,-->
<!--      checkBoxShow: false,-->
<!--      isCheck: 1,-->
<!--      merId:''-->
<!--    }-->
<!--  },-->
<!--  activated() {-->
<!--    // this.allList()-->
<!--    this.freelancerInfoList()-->
<!--    this.merAllList()-->
<!--  },-->
<!--  methods: {-->
<!--    moment,-->
<!--    initData() {-->
<!--      return {-->
<!--        merId: '',-->
<!--        name: '',-->
<!--        idCard: '',-->
<!--        isHmt: 1,-->
<!--        mobile: '',-->
<!--        idCardFrontPath: '',-->
<!--        idCardBackPath: '',-->
<!--        bankCardNo: '',-->
<!--        certNumber: '',-->
<!--        signingStatus: '',-->
<!--        remark: '',-->
<!--        dialogTitle:''-->
<!--      }-->
<!--    },-->
<!--    // 列表信息-->
<!--    freelancerInfoList() {-->
<!--      this.formData.signingTimeFrom = moment(this.pickTime[0]).format('YYYY-MM-DD')-->
<!--      this.formData.signingTimeTo = moment(this.pickTime[1]).format('YYYY-MM-DD')-->
<!--      freelancerInfo.list({-->
<!--        pageSize: this.pageSize,-->
<!--        pageNum: this.pageNum,-->
<!--        signingStatus: this.formData.signingStatus,-->
<!--        name: this.formData.name,-->
<!--        idCard: this.formData.idCard,-->
<!--        certNumber: this.formData.certNumber,-->
<!--        signingTimeFrom: this.formData.signingTimeFrom,-->
<!--        signingTimeTo: this.formData.signingTimeTo-->
<!--      }).then(res => {-->
<!--        this.listData = res.data.data.rows-->
<!--        this.total = res.data.data.total-->
<!--        this.pageSize = res.data.data.pageSize-->
<!--        this.pageNum = res.data.data.pageNum-->
<!--      })-->
<!--    },-->
<!--    add() {-->
<!--      this.dialogFormVisible = true-->
<!--      this.dialogTitle = '新增'-->
<!--      this.$nextTick(function() {-->
<!--        this.dialogData = this.initData()-->
<!--        this.resetForm('dialogData')-->
<!--      })-->
<!--    },-->
<!--    edit(id) {-->
<!--      this.dialogFormVisible = true-->
<!--      this.dialogTitle = '编辑'-->
<!--      this.$nextTick(function() {-->
<!--        this.resetForm('dialogData')-->
<!--        this.dialogData = this.initData()-->
<!--        this.getFreelancerInfo(id)-->
<!--      })-->
<!--    },-->
<!--    submit(formName) {-->
<!--      this.$refs[formName].validate((valid) => {-->
<!--        if (valid) {-->
<!--          this.btnLoading = true-->
<!--          if (this.dialogData.id) {-->
<!--            this.update()-->
<!--          } else {-->
<!--            this.addFreelancerInfo()-->
<!--          }-->
<!--        } else {-->
<!--          console.log('error submit!!')-->
<!--          return false-->
<!--        }-->
<!--      })-->
<!--    },-->
<!--    resetForm(formName) {-->
<!--      this.$refs[formName].clearValidate()-->
<!--      this.$refs[formName].resetFields()-->
<!--    },-->
<!--    resetFormSub(formName) {-->
<!--      console.log('???')-->
<!--      this.dialogData.idCard = ''-->
<!--      this.dialogData.certNumber = ''-->
<!--      this.dialogData.idCardFrontPath = ''-->
<!--      this.dialogData.idCardBackPath = ''-->

<!--      this.$nextTick(res => {-->
<!--        // this.$refs[formName].clearValidate(['idCard', 'certNumber', 'idCardFrontPath', 'idCardBackPath'])-->
<!--      })-->
<!--    },-->
<!--    // 修改信息-->
<!--    getFreelancerInfo(id) {-->
<!--      freelancerInfo.getFreelancerInfo({-->
<!--        id-->
<!--      }).then(res => {-->
<!--        this.dialogData = res.data.data-->
<!--        this.idCardFrontPath = res.data.data.idCardFrontPath-->
<!--        this.idCardBackPath = res.data.data.idCardBackPath-->
<!--      })-->
<!--    },-->
<!--    // 添加数据-->
<!--    addFreelancerInfo() {-->
<!--      const formData = new FormData()-->
<!--      const data = this.dialogData-->
<!--      for (const key in data) {-->
<!--        formData.append(key, data[key])-->
<!--      }-->
<!--      freelancerInfo.addFreelancerInfo(formData).then(res => {-->
<!--        this.btnLoading = false-->
<!--        this.$refs.dialogData.clearValidate()-->
<!--        this.$refs.dialogData.resetFields()-->
<!--        if (res.data.code == '0000') {-->
<!--          this.dialogFormVisible = false-->
<!--          this.$message.success('操作成功')-->
<!--          this.freelancerInfoList()-->
<!--        } else {-->
<!--                    this.$alert(res.data.message, '系统提示', {
            type: 'warning',
            showConfirmButton: false,
            iconClass: 'el-icon-my-alert'
          })-->
<!--        }-->
<!--      })-->
<!--    },-->
<!--    // 修改数据-->
<!--    update() {-->
<!--      const formData = new FormData()-->
<!--      const data = this.dialogData-->
<!--      for (const key in data) {-->
<!--        formData.append(key, data[key])-->
<!--      }-->
<!--      freelancerInfo.update(formData).then(res => {-->
<!--        this.btnLoading = false-->
<!--        this.$refs.dialogData.clearValidate()-->
<!--        this.$refs.dialogData.resetFields()-->
<!--        if (res.data.code == '0000') {-->
<!--          this.dialogFormVisible = false-->
<!--          this.$message.success('操作成功')-->
<!--          this.freelancerInfoList()-->
<!--        } else {-->
<!--                    this.$alert(res.data.message, '系统提示', {
            type: 'warning',
            showConfirmButton: false,
            iconClass: 'el-icon-my-alert'
          })-->
<!--        }-->
<!--      })-->
<!--    },-->
<!--    delData(id) {-->
<!--      freelancerInfo.delete({ id }).then(res => {-->
<!--        if (res.data.code == '0000') {-->
<!--          this.$message({ type: 'success', message: '删除成功' })-->
<!--          this.freelancerInfoList()-->
<!--        }-->
<!--      })-->
<!--    },-->
<!--    // 商户列表-->
<!--    // allList() {-->
<!--    //   publics.merAllList({-->
<!--    //     merName: ''-->
<!--    //   }).then(res => {-->
<!--    //     this.merIdArr = res.data.data-->
<!--    //   })-->
<!--    // },-->
<!--    merAllList(query) {-->
<!--      if (query !== '') {-->
<!--        this.loading = true-->
<!--        publics.merAllList({-->
<!--          merName: query-->
<!--        }).then(res => {-->
<!--          this.loading = false-->
<!--          this.merIdArr = res.data.data-->
<!--        })-->
<!--      } else {-->
<!--        this.merIdArr = []-->
<!--      }-->
<!--    },-->
<!--    // 分页-->
<!--    handleSizeChange(data) {-->
<!--      this.pageNum = data.pageNum-->
<!--      this.pageSize = data.pageSize-->
<!--      this.freelancerInfoList()-->
<!--    },-->
<!--    // 上传前处理-->
<!--    uprequest(param, item) {-->
<!--      const that = this-->
<!--      const size = param.file.size-->
<!--      const isLt5M = size / 1024 / 1024 < 5-->
<!--      if (!isLt5M) {-->
<!--        setTimeout(function() {-->
<!--          that.$message.error('上传大小不能超过 5MB!')-->
<!--        }, 100)-->
<!--        return-->
<!--      }-->
<!--      const reader = new FileReader()-->
<!--      reader.readAsDataURL(param.file)-->
<!--      reader.onload = function(e) {-->
<!--        console.log(e.target.result)-->
<!--        that[item] = e.target.result-->
<!--        that.dialogData[item] = param.file-->
<!--      }-->
<!--    },-->
<!--    uploadMore(param) {-->
<!--      const that = this-->
<!--      const size = param.file.size-->
<!--      const isLt5M = size / 1024 / 1024 < 15-->
<!--      if (!isLt5M) {-->
<!--        setTimeout(function() {-->
<!--          that.$message.error('上传大小不能超过 15MB!')-->
<!--        }, 100)-->
<!--        return-->
<!--      }-->
<!--      if(this.merId == ''){-->
<!--        this.$message.error('请填写商户')-->
<!--        return false-->
<!--      }-->
<!--      const formData = new FormData()-->
<!--      formData.append('file', param.file)-->
<!--      formData.append('isCheck', this.isCheck)-->
<!--      formData.append('merId', this.merId)-->
<!--      freelancerInfo.batchImportFreeLeader(formData).then(res => {-->
<!--        this.checkBoxShow = false-->
<!--        this.merId = ''-->
<!--        if(res.data.code === '0000'){-->
<!--          this.$message.success('操作成功')-->
<!--        }else{-->
<!--                    this.$alert(res.data.message, '系统提示', {
            type: 'warning',
            showConfirmButton: false,
            iconClass: 'el-icon-my-alert'
          })-->
<!--        }-->
<!--        this.freelancerInfoList()-->
<!--      })-->
<!--    },-->
<!--    uploadImport(param) {-->
<!--      const that = this-->
<!--      const size = param.file.size-->
<!--      const isLt5M = size / 1024 / 1024 < 15-->
<!--      if (!isLt5M) {-->
<!--        setTimeout(function() {-->
<!--          that.$message.error('上传大小不能超过 15MB!')-->
<!--        }, 100)-->
<!--        return-->
<!--      }-->
<!--      const formData = new FormData()-->
<!--      formData.append('file', param.file)-->
<!--      freelancerInfo.import(formData).then(res => {-->
<!--        if (res) {-->
<!--          this.$message.success('操作成功')-->
<!--        }-->
<!--      })-->
<!--    },-->
<!--    // 下载自由职业者信息-->
<!--    freelancerDownLoadData() {-->
<!--      freelancerInfo.freelancerDownLoadData({-->
<!--        ...this.formData,-->
<!--        pageNum: this.pageNum,-->
<!--        pageSize: this.pageSize-->
<!--      }, '自由职业者信息.xlsx', 'xlsx').then(res => {-->
<!--        if (res) {-->
<!--          this.$message.success('操作成功')-->
<!--        }-->
<!--      })-->
<!--    },-->
<!--    // 下载身份证-->
<!--    downloadCard() {-->
<!--      if(this.formData.merName == ''){-->
<!--        this.$message.error('请先填写企业名称')-->
<!--        return false-->
<!--      }-->
<!--      freelancerInfo.download({-->
<!--        ...this.formData,-->
<!--        pageNum: this.pageNum,-->
<!--        pageSize: this.pageSize-->
<!--      }, '身份证照片.zip', 'zip').then(res => {-->
<!--        if (res) {-->
<!--          this.$message.success('操作成功')-->
<!--        }-->
<!--      })-->
<!--    },-->
<!--    // 下载合同-->
<!--    downloadContract() {-->
<!--      if(this.formData.merName == ''){-->
<!--        this.$message.error('请先填写企业名称')-->
<!--        return false-->
<!--      }-->
<!--      freelancerInfo.downloadContract({-->
<!--        ...this.formData,-->
<!--        pageNum: this.pageNum,-->
<!--        pageSize: this.pageSize-->
<!--      }, '合同.zip', 'zip').then(res => {-->
<!--        this.$message.success('操作成功')-->
<!--      })-->
<!--    },-->
<!--    isCheckBox() {-->
<!--      this.merId = ''-->
<!--      this.checkBoxShow = true-->
<!--    },-->
<!--    downHosts() {-->
<!--      return downHost + '/freeLeader-template.xls'-->
<!--    }-->
<!--  }-->
<!--}-->
<!--</script>-->
<!--<style scoped lang="scss">-->
<!--  .list-card{-->
<!--    margin-bottom: 20px;-->
<!--  }-->

<!--  .auto-width{-->
<!--    width: 100%;-->
<!--  }-->

<!--  .dialog-form{-->
<!--    display: flex;-->
<!--    flex-flow: column nowrap;-->
<!--  }-->
<!--  .el-form-item__content{-->
<!--    margin-left: 0;-->
<!--  }-->

<!--  .list-table{-->
<!--    margin: 0 0 20px 0;-->
<!--    .el-button{-->
<!--      padding: 0;-->
<!--    }-->
<!--  }-->

<!--  .dialog-scroll{-->
<!--    overflow-y: auto;-->
<!--    height: calc(100%);-->
<!--  }-->

<!--  .reset-dialog{-->
<!--    /deep/ .el-dialog{-->
<!--      height: 80vh;-->
<!--      overflow: hidden;-->
<!--      .el-dialog__body{-->
<!--        height: calc(100% - 54px);-->
<!--      }-->
<!--    }-->
<!--    overflow: hidden;-->
<!--  }-->
<!--  .form-style{-->
<!--    padding-right: 20px;-->
<!--  }-->

<!--  .idCardFrontPath,.idCardBackPath{-->
<!--    border: 1px solid #DCDFE6;-->
<!--    width: 300px;-->
<!--    height: 150px;-->
<!--    display: flex;-->
<!--    justify-content: center;-->
<!--    align-items: center;-->
<!--    overflow: hidden;-->
<!--    i{-->
<!--      font-size: 40px;-->
<!--      color: #DCDFE6;-->
<!--    }-->
<!--    .preview{-->
<!--      width: 300px;-->
<!--    }-->
<!--  }-->
<!--  .upload-more{-->
<!--    display: inline-block;-->
<!--  }-->

<!--</style>-->
