<template>
  <div class="content-main">
    <el-card>
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane
          label="平台内的经营者和从业人员身份信息报送表"
          name="identity"
        >
          <idt ref="identityTable" />
        </el-tab-pane>
        <el-tab-pane
          label="平台内的经营者和从业人员收入信息报送表"
          name="income"
        >
          <rev ref="incomeTable" />
        </el-tab-pane>
        <el-tab-pane label="网络直播涉税信息报送表" name="transaction">
          <ltb ref="transactionTable" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'DataSubmission',
  components: {
    idt: () => import('./components/idt.vue'),
    rev: () => import('./components/rev.vue'),
    ltb: () => import('./components/ltb.vue')
  },
  data() {
    return {
      activeTab: 'identity'
    }
  },
  methods: {
  }
}
</script>
