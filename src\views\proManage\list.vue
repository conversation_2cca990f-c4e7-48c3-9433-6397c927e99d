<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="11" :xs="24">
                  <el-form-item label="发布时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="datetimerange"
                      align="right"
                      unlink-panels
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="发布企业" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-for="(item,index) in levyBodyOpt" :key="index" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="项目状态" class="form-items">
                    <el-select v-model="formData.projectStatus" placeholder="请选择" class="auto-width" clearable>
                      <el-option label="全部" value="" />
                      <el-option label="进行中" value="1" />
                      <el-option label="提交审核" value="2" />
                      <el-option label="审核退回" value="-1" />
                      <el-option label="待审核" value="4" />
                      <el-option label="编辑" value="3" />
                      <el-option label="结束" value="0" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="项目名称" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="openboxtask({title:'新增项目'})">新增</el-button>
                    <el-button icon="el-icon-search" type="primary" @click="downProjectDetail">批量下载</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantName"
            width="250"
            fixed
            label="发布企业"
          />
          <el-table-column
            prop="createTime"
            width="150"
            label="开始时间"
          />
          <el-table-column
            prop="name"
            width="150"
            label="项目名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="agentName"
            label="销售名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="orderBatchNum"
            min-width="160"
            label="订单批次号"
          />
          <el-table-column
            prop="sumSettMoney"
            min-width="100"
            label="批次总金额"
          />
          <el-table-column
            prop="levyName"
            width="250"
            label="服务主体"
          />
          <el-table-column
            prop="finishTime"
            width="150"
            label="项目完成时间"
          />
          <el-table-column
            prop="projectStatus"
            width="100"
            label="项目状态"
          >
            <template slot-scope="scope">
              {{ itemState[scope.row.status] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="peopleNum"
            width="100"
            label="项目人数"
          />
          <el-table-column
            prop="merName"
            label="操作"
            fixed="right"
            min-width="260"
          >
            <template slot-scope="scope">
              <el-button v-if="scope.row.status == '0' || scope.row.status == '1'" type="text" @click="viewInfo(scope.row.id)">报名详情</el-button>
              <el-button type="text" @click="openboxtask({data:scope.row,title:['2','4'].indexOf(scope.row.status+'') !=-1?'查看项目':'编辑项目'})">{{ ["2","4"].indexOf(scope.row.status+'') !=-1?'查看':'编辑' }}</el-button>
              <el-button v-if="scope.row.status == '0' || scope.row.status == '1'" type="text" @click="openBox(scope.row.id)">交付物</el-button>
              <el-button v-if="scope.row.status == '1'" type="text" @click="overPro(scope.row.id)">结束</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
    <!--    弹框-->
    <el-dialog :title="title" :visible.sync="dialogShow" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" size="mini" label-width="120px" class="form-style" :rules="rules">
          <div :style="{pointerEvents:(title==='新增项目' || ['2','4'].indexOf(dialogData.status) ==-1?'auto':'none')}">
            <el-form-item label="发布企业" prop="merchantId">
              <el-select
                v-model="dialogData.merchantId"
                class="auto-width"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="merAllList"
                :loading="loading"
                @change="getLevyBodyId"
              >
                <el-option v-for="item in merAllListArr" :key="item.id" :label="item.merName" :value="item.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="项目截止时间" prop="pickTime">
              <el-date-picker
                v-model="dialogData.pickTime"
                class="auto-width"
                type="daterange"
                align="right"
                unlink-panels
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-value="dialogData.pickTime"
                :picker-options="pickerOptions"
                @change="diaresetTime"
              />
            </el-form-item>
            <el-form-item label="服务主体" prop="levyId">
              <el-select v-model="dialogData.levyId" placeholder="请选择" class="auto-width" clearable @change="findTypeBymerIdLevyId">
                <el-option v-for="(item) in levyBodyArr" :key="item.id" :label="item.NAME" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="dialogData.name" placeholder="请输入内容" clearable />
            </el-form-item>

            <el-form-item label="项目图片" prop="files" :rules="dialogData.levyId !== 14 ? [{required: false}]:rules.files">
              <el-upload
                ref="upload"
                action=""
                :disabled="upLoading"
                list-type="picture-card"
                accept=".jpg,.jpeg,.png"
                :file-list="dialogData.files"
                :http-request="(params => uprequest(params,0))"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item label="性别要求" prop="gender">
              <el-select v-model="dialogData.gender" placeholder="请选择" class="auto-width" clearable>
                <el-option label="不限" :value="2" />
                <el-option label="女" :value="0" />
                <el-option label="男" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item label="经验要求" prop="experience">
              <el-select v-model="dialogData.experience" placeholder="请选择" class="auto-width" clearable>
                <el-option label="不限" value="0" />
                <el-option label="1-3年" value="1" />
                <el-option label="3-5年" value="2" />
                <el-option label="5-10年" value="3" />
                <el-option label="10年以上" value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="结算标准" prop="salaryRange">
              <el-input v-model="dialogData.salaryRange" placeholder="请输入内容" clearable />
            </el-form-item>
            <el-form-item label="验收标准" prop="standard">
              <el-input v-model="dialogData.standard" placeholder="请输入内容" clearable />
            </el-form-item>
            <template>
              <el-form-item v-if="dialogData.levyId !== 14" label="项目类型" prop="type2">
                <el-select v-model="dialogData.type2" placeholder="请选择" class="auto-width" clearable @change="findTypeBymerIdLevyId">
                  <el-option v-for="(item) in invoiceArr" :key="item.invoiceTypeId" :label="item.type_name" :value="item.invoiceTypeId" />
                </el-select>
              </el-form-item>
              <el-form-item v-else label="项目类型" prop="categoryId">
                <el-select v-model="dialogData.categoryId" placeholder="请选择" class="auto-width" clearable @change="findTypeBymerIdLevyId">
                  <el-option v-for="(item) in invoiceArr" :key="item.object_id" :label="item.name" :value="item.object_id" />
                </el-select>
              </el-form-item>
            </template>
            <el-form-item label="项目描述" prop="describe">
              <el-input v-model="dialogData.describe" type="textarea" :row="2" placeholder="请输入内容" clearable />
            </el-form-item>
          </div>
          <el-form-item v-if="['2','4'].indexOf(dialogData.status+'') ==-1" label="" class="form-items">
            <template>
              <template v-if="dialogData.levyId == 14">
                <el-button :loading="btnLoading" type="primary" @click="submit('dialogData', 2)">提交审核</el-button>
              </template>
              <template v-else>
                <el-button :loading="btnLoading" type="primary" @click="submit('dialogData', 3)">保存</el-button>
                <el-button :loading="btnLoading" type="primary" @click="submit('dialogData', 2)">提交审核</el-button>
              </template>
            </template>
            <el-button @click="dialogShow = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

    <!--    编辑交付物-->
    <el-dialog title="编辑交付物" :visible.sync="dialogProShow" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form :model="dialogProData" size="mini" class="form-style" label-width="120">
          <el-form-item label="交付物说明" class="form-items">
            <el-input v-model="dialogProData.remark" type="textarea" :row="2" autocomplete="off" />
          </el-form-item>
          <div class="table-top table-header">
            <el-upload
              ref="upload"
              class="file-upload"
              :show-file-list="false"
              action=""
              :disabled="upLoading"
              accept=".pdf,.jpg,.jpeg,.png"
              :http-request="(params => uprequest(params,'0'))"
            >
              <el-button plain icon="el-icon-plus" size="mini">添加附件</el-button>
            </el-upload>
            <el-button plain size="mini" @click="batchDownLoadFile">批量下载交付</el-button>
          </div>
          <el-table
            :data="dialogProData.files"
            border
            size="mini"
            class="list-table"
          >
            <el-table-column
              prop="fileName"
              label="文件名称"
            >
              <template slot-scope="scope">
                {{ scope.row.fileName + scope.row.fileExt }}
              </template>
            </el-table-column>
            <el-table-column
              prop="fileSize"
              label="文件大小"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.fileSize > 1024*1024">{{ Math.ceil(scope.row.fileSize / 1024 / 1024) }}MB</div>
                <div v-else>{{ Math.ceil(scope.row.fileSize / 1024) }}KB</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="fileType"
              label="文件状态"
            >
              <template slot-scope="">
                已上传
              </template>
            </el-table-column>
            <el-table-column
              prop=""
              label="操作"
            >
              <template slot-scope="scope">
                <el-button type="text">
                  <el-upload
                    ref="upload"
                    class="file-upload resetUpload"
                    :show-file-list="false"
                    action=""
                    :disabled="upLoading"
                    accept=".pdf,.jpg,.jpeg,.png"
                    :http-request="(params => uprequest(params,scope.row.fileType,scope.$index))"
                  >
                    <a class="el-button el-button--text resetbtn">替换</a>
                  </el-upload>
                </el-button>
                <a class="el-button el-button--text resetbtn" target="_blank" :href="encodeURI(scope.row.filePath)">预览</a>
                <el-button type="text" @click="delFiles(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="btnbox">
            <el-button type="primary" size="mini" @click="submitPro">保存</el-button>
            <el-button type="" size="mini" @click="dialogProShow = false">取消</el-button>
          </div>
        </el-form>
      </el-scrollbar>
    </el-dialog>
    <!--    查看项目图片-->
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
import proManage from '@/axios/default/proManage'
import JFJ from '@/axios/JFJ'
import publics from '@/axios/default/public'
import levyBody from '@/axios/default/levyBody'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import moment from 'moment'
export default {
  name: 'List',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: [],
      itemState: { '-1': '审核退回', '0': '结束', '1': '进行中', '2': '提交审核', '3': '编辑', '4': '待审核' },
      formData: {
        levyId: '',
        merchantName: '',
        name: '',
        projectStatus: '',
        createTimeFrom: '',
        createTimeTo: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      listData: [],
      title: '',
      dialogShow: false,
      dialogData: {},

      dialogVisible: false,
      dialogImageUrl: '',

      merAllListArr: [],
      levyBodyArr: [],
      invoiceArr: [],
      loading: false,
      btnLoading: false,
      rules: {
        merchantId: [{ required: true, message: '请填写发布企业', trigger: 'blur' }],
        pickTime: [{ required: true, message: '请填写项目截止时间', trigger: 'blur' }],
        levyId: [{ required: true, message: '请填写服务主体', trigger: 'blur' }],
        name: [
          { required: true, message: '请填写项目名称', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const reg = new RegExp("[`~!@#$^&*()=|{}':;',/\/.<>/?~！@#￥……&*（）——|{}【】‘；：'。，？ ]")
            if (reg.test(value)) {
              return callback(new Error('项目名称不能包含空格等特殊字符'))
            } else {
              return callback()
            }
          }, trigger: ['blur', 'change'] }
        ],
        files: [{ required: true, message: '请上传项目图片', trigger: 'change' }],
        gender: [{ required: true, message: '请填选择性别要求', trigger: 'blur' }],
        experience: [{ required: true, message: '请选择经验要求', trigger: 'blur' }],
        salaryRange: [{ required: true, message: '请填写结算标准', trigger: 'blur' }],
        standard: [{ required: true, message: '请填写验收标准', trigger: 'blur' }],
        type2: [{ required: true, message: '请填写项目类型', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请填写项目类型', trigger: 'blur' }],
        describe: [{ required: true, message: '请填写项目描述', trigger: 'blur' }]
      },

      dialogProShow: false,
      dialogProData: {},
      upLoading: false,
      upData: {
        file: '',
        fileType: ''
      },
      levyBodyOpt: [] // 所有的代征主体
    }
  },
  watch: {
    '$route.fullPath': {
      handler() {
        this.list()
      }
    }
  },
  created() {
    //    this.list()
  },
  mounted() {
    this.initLevyBody()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    diaresetTime(val) {
      val = val || ['', '']
      this.dialogData.createTime = val[0]
      this.dialogData.endTime = val[1]
    },

    initLevyBody() {
      levyBody.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyOpt = res.data.data
          if (res.data.data.length > 1) {
            this.levyBodyOpt.unshift({ name: '请选择', id: '' })
          } else {
            this.formData.levyId = this.levyBodyOpt[0].id
          }
        }
        this.list()
      })
    },
    merAllList(query = '') {
      this.loading = true
      publics.merAllList({
        merName: query
      }).then(res => {
        this.loading = false
        this.merAllListArr = res.data.data
      })
    },
    getLevyBodyId(id) {
      publics.initAddRechInfo({
        'queryType': '2',
        'merId': id
      }).then(res => {
        this.levyBodyArr = res.data.data
      })
    },
    // 发票类目
    findTypeBymerIdLevyId() {
      if (this.dialogData.levyId !== 14) {
        this.HTTypeOptions()
      } else {
        this.JFJTypeOptions()
      }
    },
    HTTypeOptions() {
      proManage.findTypeBymerIdLevyId({
        merId: this.dialogData.merchantId,
        levyId: this.dialogData.levyId
      }).then(res => {
        this.invoiceArr = res.data.data
      })
    },
    JFJTypeOptions() {
      JFJ.category({ page_size: 100 }).then(res => {
        if (res._code === 0) {
          this.invoiceArr = res.results
        } else {
          this.$message.error(res._message)
        }
      })
    },

    list() {
      this.formData.createTimeFrom = this.pickTime[0] ? moment(this.pickTime[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.createTimeTo = this.pickTime[1] ? moment(this.pickTime[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      proManage.findProjectInfoList({
        ...this.formData,
        id: this.$route.query.projectId || '',
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },

    overPro(id) {
      proManage.updateProjectInfo({
        id: id,
        status: 0
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initDialogData() {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return {
        pickTime: [start, end],
        merchantId: '', // 商户ID
        createTime: '',
        endTime: '',
        name: '',
        levyId: '',
        gender: '',
        experience: '',
        salaryRange: '',
        standard: '',
        type2: '',
        categoryId: '',
        describe: '',
        files: []
      }
    },
    viewInfo(id) {
      this.$router.push({
        path: '/proManage/sign',
        query: { id }
      })
    },

    openboxtask(scope) {
      this.dialogShow = true
      const { title, data } = scope
      this.title = title
      this.$nextTick(function() {
        if ('data' in scope) {
          this.dialogData = Object.assign({}, data)
          const files = this.dialogData.files
          files.length > 0 && files.forEach(item => { item.url = item.filePath })
          this.merAllList(this.dialogData.merchantName)
          this.getLevyBodyId(this.dialogData.merchantId)
          this.findTypeBymerIdLevyId()
          this.$set(this.dialogData, 'files', files)
          this.$set(this.dialogData, 'pickTime', [data.createTime, data.endTime])
        } else {
          this.dialogData = this.initDialogData()
        }
      })
    },

    submit(formName, status) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          this.dialogData.merchantName = this.merAllListArr.find(res => res.id == this.dialogData.merchantId).merName
          this.dialogData.levyName = this.levyBodyArr.find(res => res.id == this.dialogData.levyId).NAME
          this.dialogData.files && this.dialogData.files.forEach(item => { item.status = false })
          this.dialogData.pickTime[0] && (this.dialogData.createTime = moment(this.dialogData.pickTime[0]).format('YYYY-MM-DD HH:mm:ss'))
          this.dialogData.pickTime[1] && (this.dialogData.endTime = moment(this.dialogData.pickTime[1]).format('YYYY-MM-DD HH:mm:ss'))
          proManage[this.title === '新增项目' ? 'addProjectInfo' : 'updateProjectInfo']({
            ...this.dialogData,
            status
          }).then(res => {
            this.btnLoading = false
            if (res.data.code == '0000') {
              this.dialogShow = false
              this.dialogData = this.initDialogData()
              this.$message.success(res.data.message)
              this.list()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    downProjectDetail() {
      this.formData.createTimeFrom = this.pickTime[0] ? moment(this.pickTime[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.createTimeTo = this.pickTime[1] ? moment(this.pickTime[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      proManage.downProjectDetail({
        ...this.formData
      }, `项目列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },

    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },

    openBox(id) {
      this.getOneDeliveryFile(id)
    },
    initDialogProData() {
      return {
        projectId: '',
        remark: '',
        files: []
      }
    },
    getOneDeliveryFile(id) {
      proManage.getProOneDeliveryFile({
        projectId: id
      }).then(res => {
        if (res.data.code == '0000') {
          if (JSON.stringify(res.data.data) !== '{}') {
            this.dialogProData = res.data.data
          } else {
            this.dialogProData = this.initDialogProData()
          }
          this.$set(this.dialogProData, 'id', id)
          this.dialogProShow = true
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    delFiles(index) {
      this.dialogProData.files.splice(index, 1)
    },

    // 上传
    uprequest(param, fileType, index) {
      const that = this
      that.upLoading = true
      const upEvent = param.file
      const size = upEvent.size
      const limitSize = size / 1024 / 1024 < 50
      if (!limitSize) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      that.upData.file = upEvent
      that.upData.fileType = fileType
      this.fileListData(index)
    },
    fileListData(index) {
      const formDataPro = new FormData()
      const data = this.upData
      for (const key in data) {
        formDataPro.append(key, data[key])
      }
      publics.upFiles(formDataPro).then(res => {
        if (res.data.code == '0000') {
          const { fileName, fileSize, fileExt, filePath, fileType } = res.data.data
          if (Object.prototype.toString.call(index) == '[object Number]') {
            this.$set(this.dialogProData.files, index, { fileName, fileExt, filePath, fileSize, fileType })
          } else {
            if (this.dialogShow) {
              this.dialogData.files.push({
                fileName,
                fileExt,
                filePath,
                fileSize,
                fileType,
                url: filePath
              })
            } else if (this.dialogProShow) {
              this.dialogProData.files.push({
                fileName,
                fileExt,
                filePath,
                fileSize,
                fileType,
                url: filePath
              })
            }
          }
          this.upLoading = false
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
          this.upLoading = false
        }
      }).catch(res => {
        this.upLoading = false
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove(file, fileList) {
      this.$set(this.dialogData, 'files', fileList)
    },
    submitPro() {
      this.dialogProData.files.forEach(item => { item.status = false })
      proManage.proAddDeliveryFile({
        projectId: this.dialogProData.id,
        remark: this.dialogProData.remark,
        files: this.dialogProData.files
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
          this.dialogProShow = false
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    batchDownLoadFile() {
      proManage.batchProDownLoadFile({
        projectId: this.dialogProData.id
      }, `交付物列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.zip`, '.zip').then(res => {

      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

  .table-header{
    display: flex;
    justify-content: flex-end;
    .file-upload{
      margin-right: 5px;
    }
  }
</style>
