<template>
  <div class="socket">
    <h4 ref="socketHeader" style="padding: 0 18px;">系统消息推送通知</h4>
    <div class="socketBody" :style="{height: socketHeight}">
      <div class="socketItems">
        <div v-if="rows.length>0">
          <div class="socketItem" v-for="item in rows" :key="item.id" @click="onRead(item)">
            <el-badge :is-dot="item.isUse==0">
              <div class="socketItemTxt">{{ item.content }}</div>
              <div class="socketItemTime">{{ item.createTime }}</div>
            </el-badge>
          </div>
        </div>
        <div class="noMoreTxt" v-if="rows.length == 0">暂无消息通知</div>
        <div class="noMoreTxt" style="color: #57a3f3; cursor: pointer" v-if="rows.length>0 && !loaded" @click="loadMore">加载更多>></div>
        <div class="noMoreTxt" v-if="rows.length>0 && loaded">没有更多消息了</div>
      </div>
    </div>
    <el-button ref="socketFooter" type="primary" plain @click="onAllRead(rows)">全部标记为已读</el-button>
  </div>
</template>
<script>
export default {
  name: 'MessageSocket',
  props: {
    rows: {
      type: Array,
      default: () => {
        return []
      }
    },
    loaded: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      socketHeight: '80%'
    }
  },
  mounted() {
    this.socketHeight = `${document.documentElement.clientHeight - 106}px`
    window.onresize = () => {
      return (() => {
        this.socketHeight = `${document.documentElement.clientHeight - 106}px`
      })()
    }
  },
  methods: {
    loadMore() {
      this.$emit('loadMore')
    },
    onRead(v) {
      this.$emit('read', v)
    },
    onAllRead(v) {
      this.$emit('allRead', v)
    }
  }
}
</script>
<style scoped lang="scss">
.socket {
  .socketBody {
    padding: 0 18px;
    overflow-y: scroll;
    width: 268px;
    .socketItem {
      cursor: pointer;
      margin-bottom: 22px;
      padding: 4px 8px;
      box-shadow: 0 0px 6px 0px rgba(0,0,0,.2);
      .socketItemTxt {
        font-size: 14px;
        color: #666;
        margin-bottom: 6px;
      }
      .socketItemTime {
        font-size: 12px;
        color: #999;
        text-align: right;
      }
    }
    .noMoreTxt {
      font-size: 14px;
      color: #999;
      text-align: center;
    }
  }
  button {
    margin: 0 !important;
    width: 100% !important;
    margin-top: 10px !important;
  }
}
</style>
