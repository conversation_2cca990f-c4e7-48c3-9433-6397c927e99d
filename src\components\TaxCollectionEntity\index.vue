<template>
  <el-select
    v-model="selectedValue"
    v-bind="$attrs"
    class="auto-width"
    placeholder="请选择"
    v-on="$listeners"
  >
    <el-option v-if="list.length>1" label="请选择" value="" />
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>

<script>
import publicApi from '@/axios/default/public'

export default {
  name: 'TaxCollectionEntity',
  inheritAttrs: false,
  props: {
    value: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      list: [],
      selectedValue: this.value
    }
  },
  watch: {
    value(newVal) {
      this.selectedValue = newVal
    },
    selectedValue(newVal) {
      this.$emit('input', newVal)
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      const { data } = await publicApi.queryLevyBodyInfos()
      this.list = data.data || []
    }
  }
}
</script>

<style scoped>
.auto-width {
  width: 100%;
}
</style>
