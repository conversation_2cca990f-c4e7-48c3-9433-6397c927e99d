<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="完税月份" class="form-items">
                    <el-date-picker
                      v-model="formData.month"
                      class="auto-width"
                      format="yyyy 年 MM 月"
                      value-format="yyyy-MM"
                      type="month"
                      placeholder="请选择月份"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入商户名称" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-top: 30px;">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增完税证明</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantName"
            min-width="180"
            label="商户名称"
          />
          <el-table-column
            prop="levyName"
            min-width="140"
            label="代征主体"
          />
          <el-table-column
            prop="month"
            min-width="140"
            label="完税月份"
          />
          <el-table-column
            prop="createTime"
            min-width="140"
            label="创建时间"
          />
          <el-table-column
            prop="merName"
            label="操作"
            fixed="right"
            min-width="100"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="detail(scope.row)">查看</el-button>
              <el-button type="text" @click="edit(scope.row, '0')">修改</el-button>
              <el-button type="text" @click="edit(scope.row, '1')">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>

      <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">
        <el-scrollbar class="dialog-scroll">
          <el-form ref="dialogData" :model="dialogData" :rules="rules" :label-width="formLabelWidth" size="mini" class="form-style">
            <el-form-item label="商户名称" prop="merchantName">
              <el-select v-model="dialogData.merchantName" :disabled="dialogTitle == '编辑'" class="auto-width" filterable clearable remote placeholder="请输入关键词" :remote-method="getMerList" :loading="loading">
                <el-option v-for="item in merArr" :key="item.id" :label="item.merName" :value="item.merName" />
              </el-select>
            </el-form-item>
            <el-form-item label="代征主体" prop="levyId">
              <el-select v-model="dialogData.levyId" placeholder="请选择申报代征主体" class="auto-width" @change="onSelectLevy">
                <el-option
                  v-for="item in levyArr"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="上传完税证明" prop="filePath">
              <el-upload
                action=""
                :file-list="fileList"
                accept=".pdf"
                :before-remove="beforeRemove"
                :before-upload="beforeUpload"
                :limit="1"
                :http-request="(params => uprequest(params,'1'))"
              >
                <div style="display: flex; align-items: center;">
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">只能上传.pdf文件</div>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item label="完税月份" prop="month">
              <el-date-picker
                v-model="dialogData.month"
                class="auto-width"
                format="yyyy 年 MM 月"
                value-format="yyyy-MM"
                type="month"
                placeholder="请选择申报月份"
              />
            </el-form-item>
            <el-form-item>
              <div style="text-align: right">
                <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">保存</el-button>
                <el-button :loading="btnLoading" @click="dialogFormVisible = false">取消</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import taxpayment from '@/axios/default/taxpayment'
import publics from '@/axios/default/public'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { newAlert } from '@/utils'

export default {
  name: 'TaxDeclareFiles',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        month: moment().subtract(1, 'months').format('YYYY-MM'),
        merchantName: '',
        levyId: ''
      },
      listData: [],
      levyArr: [],
      fileList: [],
      upLoading: false, // 上传文件是否完成
      merArr: [],
      loading: false,
      btnLoading: false,

      dialogData: this.initData(),
      dialogFormVisible: false,
      dialogTitle: '',
      formLabelWidth: '180px',
      rules: {
        merchantName: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
        levyId: [{ required: true, message: '请选择申报主体', trigger: 'change' }],
        filePath: [{ required: true, message: '请上传申报文件', trigger: 'change' }],
        month: [{ required: true, message: '请选择申报月份', trigger: 'change' }]
      }
    }
  },
  mounted() {
    this.list()
    this.getMerList()
    this.queryLevyBodyInfos()
  },
  methods: {
    initData() {
      return {
        levyId: '',
        levyName: '',
        merchantName: '',
        month: '',
        filePath: ''
      }
    },
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    list() {
      taxpayment.findTaxDeclareFileByPage({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },

    add() {
      this.dialogFormVisible = true
      this.dialogTitle = '新增'
      this.$nextTick(function() {
        this.fileList = []
        this.dialogData = this.initData()
      })
    },
    detail(args) {
      const { filePath } = args
      window.open(filePath)
    },
    edit(args, status) {
      this.dialogData = args
      this.$set(this.dialogData, 'deleteFlag', status)
      if (status === '0') {
        // 新增/编辑
        const { filePath } = this.dialogData
        const fileName = filePath.split('/')[filePath.split('/').length - 1]
        this.fileList = JSON.parse(JSON.stringify([{ name: fileName, url: filePath }]))
        this.dialogFormVisible = true
        this.dialogTitle = '编辑'
      } else {
        // 删除
        taxpayment.updateTaxDeclareFile({ ...this.dialogData }).then(res => {
          if (res.data.code == '0000') {
            this.dialogFormVisible = false
            this.btnLoading = false
            this.$message.success(res.data.message)
            this.list()
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      }
    },

    getMerList(query = '') {
      this.loading = true
      publics.merAllList({
        merName: query
      }).then(res => {
        this.loading = false
        this.merArr = res.data.data
      })
    },

    onSelectLevy(v) {
      const curLevyName = this.levyArr.filter(item => item.id === v)[0].name
      this.$set(this.dialogData, 'levyName', curLevyName)
    },

    uprequest(param, fileType) {
      const that = this
      const size = param.file.size
      const limitSize = size / 1024 / 1024 < 50
      if (!limitSize) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      that.upLoading = true
      const upData = {
        file: param.file,
        fileType: fileType
      }
      const formData = new FormData()
      for (const key in upData) {
        formData.append(key, upData[key])
      }

      const config = {
        onUploadProgress: progressEvent => {
          this.progressPercent = Number(progressEvent.loaded / progressEvent.total * 100).toFixed(2)
          param.onProgress({ percent: this.progressPercent - 1 })
        }
      }

      publics.upFiles(formData, config).then(res => {
        if (res.data.code == '0000') {
          const { fileName, fileSize, fileExt, filePath, fileType } = res.data.data
          this.fileList = JSON.parse(JSON.stringify([{ name: fileName, fileExt, url: filePath, fileSize: fileSize, fileType: fileType }]))
          this.$set(this.dialogData, 'filePath', filePath)
          this.upLoading = false
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
          this.upLoading = false
        }
      }).catch(res => {
        this.upLoading = false
      })
    },
    beforeRemove(file, fileList) {
      this.$confirm(`即将删除${file.name}文件, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fileList.splice(0, 1)
        this.$set(this.dialogData, 'filePath', '')
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })

      return this.fileList.length === 0
    },
    beforeUpload() {
      if (this.fileList.length === 1) {
        this.$message.warning('仅可上传一份文件/图片')
        return false
      }
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogTitle == '编辑') {
            this.$set(this.dialogData, 'deleteFlag', status)
            taxpayment.updateTaxDeclareFile({ ...this.dialogData }).then(res => {
              if (res.data.code == '0000') {
                this.dialogFormVisible = false
                this.btnLoading = false
                this.$message.success(res.data.message)
                this.list()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          } else {
            taxpayment.addTaxDeclareFile({ ...this.dialogData }).then(res => {
              if (res.data.code == '0000') {
                this.dialogFormVisible = false
                this.btnLoading = false
                this.$message.success(res.data.message)
                this.getMerList()
                this.list()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    },
    queryLevyBodyInfos() {
      publics.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyArr = res.data.data
          if (this.levyArr.length === 1) {
            this.formData.levyId = this.levyArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.auto-width{
  width: 100%;
}

.dialog-form{
  display: flex;
  flex-flow: column nowrap;
}
.el-form-item__content{
  margin-left: 0;
}

.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
  /deep/ .warning-row {
    background: #fbc4c4 !important;
  }
  /deep/ .warning-oldlace {
    background: oldlace !important;
  }

}
</style>
