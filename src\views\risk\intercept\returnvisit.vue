<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="标题" class="form-items">
                    <el-input v-model="formData.title" placeholder="请输入标题" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="身份证号" class="form-items">
                    <el-input v-model="formData.idCard" placeholder="请输入身份证号" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="起止时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="datetimerange"
                      align="right"
                      unlink-panels
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增回访记录</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="title"
            fixed
            min-width="140"
            label="标题"
          />
          <el-table-column
            prop="idCard"
            min-width="180"
            label="证件号"
          />
          <el-table-column
            prop="createTime"
            min-width="140"
            label="创建时间"
          />
          <el-table-column
            prop="merName"
            label="操作"
            fixed="right"
            min-width="100"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="edit(scope.row)">编辑</el-button>
              <el-button type="text" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>

      <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">
        <el-scrollbar class="dialog-scroll">
          <el-form ref="dialogData" :model="dialogData" :rules="rules" :label-width="formLabelWidth" size="mini" class="form-style">
            <el-form-item label="标题" prop="title">
              <el-input v-model="dialogData.title" :disabled="dialogTitle == '编辑'" autocomplete="off" />
            </el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="dialogData.idCard" :disabled="dialogTitle == '编辑'" autocomplete="off" />
            </el-form-item>
            <el-form-item label="回访描述" prop="description">
              <el-input v-model="dialogData.description" type="textarea" :rows="2" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="上传附件" prop="description">
              <el-upload
                ref="upload"
                class="file-upload"
                action=""
                :disabled="upLoading"
                accept=".pdf,.jpg,.jpeg,.png"
                :file-list="fileList"
                :on-preview="handlePreview"
                :before-remove="beforeRemove"
                :on-remove="handleRemove"
                :http-request="(params => uprequest(params,'0'))"
              >
                <div style="display: flex; align-items: center;">
                  <el-button plain icon="el-icon-plus" size="mini">添加附件</el-button>
                  <div slot="tip" class="el-upload__tip" style="color: #dc3c3c;">只能上传pdf/jpg/jpeg/png文件，且不超过2MB</div>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <div style="text-align: right">
                <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">保存</el-button>
                <el-button :loading="btnLoading" @click="dialogFormVisible = false">取消</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import publics from '@/axios/default/public'
import risk from '@/axios/default/risk'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'
import moment from 'moment'
export default {
  name: 'ReturnVisit',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        title: '',
        idCard: '',
        createTimeFrom: '',
        createTimeTo: ''
      },
      listData: [],
      loading: false,
      btnLoading: false,

      dialogData: this.initData(),
      upLoading: false, // 上传附加加载动画
      dialogFormVisible: false,
      dialogTitle: '',
      formLabelWidth: '90px',
      rules: {
        title: [{ required: true, message: '请输入回访标题', trigger: 'blur' }],
        idCard: [{ required: true, message: '自由职业者身份证号', trigger: 'blur' }],
        description: [{ required: true, message: '请输入回访描述信息', trigger: 'blur' }]
      },
      fileList: [],
      pickTime: ['', ''],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    initData() {
      return {
        id: '',
        title: '',
        idCard: '',
        description: '',
        filePath: '',
        fileSize: '',
        fileName: '',
        fileExt: ''
      }
    },
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    list() {
      this.formData.createTimeFrom = this.pickTime && this.pickTime[0] ? moment(this.pickTime[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.createTimeTo = this.pickTime && this.pickTime[1] ? moment(this.pickTime[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      risk.returnvisit({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },

    add() {
      this.dialogFormVisible = true
      this.dialogTitle = '新增'
      this.$nextTick(function() {
        this.dialogData = this.initData()
        this.fileList = []
      })
    },
    edit(args) {
      args.filePath = args.file_path
      this.fileList = [{ name: args.filePath, url: args.filePath }]
      this.dialogData = args
      this.dialogFormVisible = true
      this.dialogTitle = '编辑'
    },
    del(args) {
      this.$confirm('删除后不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        risk.deleteReturnvisit({ ...args }).then(res => {
          if (res.data.code == '0000') {
            this.$message.success(res.data.message)
            this.list()
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    beforeRemove() {
      return this.$confirm('删除后不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    },
    handleRemove(file, fileList) {
      this.fileList = []
      this.$set(this.dialogData, 'filePath', '')
      this.$message({
        type: 'success',
        message: '删除成功'
      })
    },
    handlePreview(file) {
      window.open(file.url, '_blank')
    },
    uprequest(param, fileType) {
      this.upLoading = true
      const upEvent = param.file
      const size = upEvent.size
      const limitSize = size / 1024 / 1024 < 2
      if (!limitSize) {
        setTimeout(() => {
          this.$message.error('上传大小不能超过 2MB!')
        }, 100)
        return
      }
      const upData = {
        file: upEvent,
        fileType: fileType
      }

      const formDataPro = new FormData()
      for (const key in upData) {
        formDataPro.append(key, upData[key])
      }

      publics.upFiles(formDataPro).then(res => {
        if (res.data.code == '0000') {
          const { fileName, fileSize, fileExt, filePath } = res.data.data
          this.fileList = [{ name: `${fileName}${fileExt}`, url: filePath }]
          this.$set(this.dialogData, 'filePath', filePath)
          this.$set(this.dialogData, 'fileName', fileName)
          this.$set(this.dialogData, 'fileSize', fileSize)
          this.$set(this.dialogData, 'fileExt', fileExt)
          this.upLoading = false
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
          this.upLoading = false
        }
      }).catch(res => {
        this.upLoading = false
      })
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          risk.addReturnvisit({ ...this.dialogData }).then(res => {
            if (res.data.code == '0000') {
              this.dialogFormVisible = false
              this.btnLoading = false
              this.$message.success(res.data.message)
              this.list()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.auto-width{
  width: 100%;
}

.dialog-form{
  display: flex;
  flex-flow: column nowrap;
}
.el-form-item__content{
  margin-left: 0;
}

.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
  /deep/ .warning-row {
    background: #fbc4c4 !important;
  }
  /deep/ .warning-oldlace {
    background: oldlace !important;
  }

}
</style>
