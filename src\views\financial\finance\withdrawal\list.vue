<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="提现时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                      @change="resetTime"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="7" :xs="24">
                  <el-form-item label="审核状态" class="form-items">
                    <el-select v-model="formData.cashStatus" placeholder="请选择活动区域" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option label="初审中" :value="1" />
                      <el-option label="复审中" :value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-download" type="primary" @click="cashExcel">批量下载</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-download" plain type="primary" @click="openBox1">提现录入</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="提现时间"
            show-overflow-tooltip
          />
          <el-table-column
            prop="accountName"
            label="企业名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
          />
          <el-table-column
            prop="destBankName"
            label="收款银行"
          />
          <el-table-column
            prop="destAcctName"
            label="收款账户名"
            min-width="220"
          />
          <el-table-column
            prop="destAcctNo"
            label="收款卡号"
          />
          <el-table-column
            prop="cashAmount"
            label="提现金额"
          />
          <el-table-column
            prop="cashStatus"
            label="审核状态"
          >
            <template slot-scope="scope">
              {{ adjustStatusEnum[scope.row.cashStatus] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="userName"
            label="申请人"
          />
          <el-table-column
            prop="remark"
            min-width="200"
            show-overflow-tooltip
            label="备注"
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    提现录入-->
    <el-dialog title="提现录入" :visible.sync="dialogBox1" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogBox1Data" :model="dialogBox1Data" label-width="100px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="提现主体" prop="typeStr">
            <el-select v-model="dialogBox1Data.typeStr" placeholder="请选择提现主体" class="auto-width" @change="onSetCashOut">
              <el-option v-for="item in cashOutArr" :key="item.type" :label="item.name" :value="item.type" />
            </el-select>
          </el-form-item>
          <el-form-item label="企业名称" prop="merchantId">
            <el-select
              v-if="dialogBox1Data.typeStr == 1"
              v-model="dialogBox1Data.merchantId"
              class="auto-width"
              filterable
              remote
              placeholder="请输入关键词"
              :remote-method="mccList"
              :loading="loading"
              @change="getlevyId"
            >
              <el-option v-for="(item,index) in merNameArr" :key="item.id" :label="item.merName" :value="item.id" />
            </el-select>
            <el-input v-else v-model="dialogBox1Data.merchantName" autocomplete="off" class="auto-width" disabled />
          </el-form-item>

          <el-form-item ref="levyId" label="代征主体" prop="levyId">
            <el-select v-if="dialogBox1Data.typeStr == 1" v-model="dialogBox1Data.levyId" placeholder="请选择代征主体" class="auto-width" @change="getChannel">
              <el-option v-for="(item,index) in levyIdArr" :key="item.id" :label="item.NAME" :value="item.id" />
            </el-select>
            <el-input v-else v-model="dialogBox1Data.levyName" autocomplete="off" class="auto-width" disabled />
          </el-form-item>

          <el-form-item ref="channelId" label="通道名称" prop="channelId">
            <el-select v-if="dialogBox1Data.typeStr == 1" v-model="dialogBox1Data.channelId" placeholder="请选择充值通道" class="auto-width" @change="getMoney">
              <el-option v-for="(item,index) in channelIdArr" :key="index" :label="item.channel_name" :value="item.id" />
            </el-select>
            <el-input v-else v-model="dialogBox1Data.channelName" autocomplete="off" class="auto-width" disabled />
          </el-form-item>

          <el-form-item ref="balance" label="账户余额" prop="balance">
            <el-input v-if="dialogBox1Data.typeStr == 1" v-model="rechBankNo.balance" autocomplete="off" class="auto-width" disabled />
            <el-input v-else v-model="dialogBox1Data.canBalance" autocomplete="off" class="auto-width" disabled />
          </el-form-item>

          <el-form-item label="提现金额" prop="cashAmount">
            <el-input v-model="dialogBox1Data.cashAmount" placeholder="请输入内容">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="dialogBox1Data.typeStr !=1" label="收款银行" prop="destBankCode">
            <el-select v-model="dialogBox1Data.destBankCode" filterable clearable placeholder="请选择收款银行" class="auto-width">
              <el-option v-for="(value,key) in dialogBox1Data.bankInfo" :key="key" :label="value" :value="key" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="dialogBox1Data.typeStr !=1" label="收款账户名" prop="destAcctName">
            <el-input v-model="dialogBox1Data.destAcctName" clearable autocomplete="off" class="auto-width" />
          </el-form-item>
          <el-form-item v-if="dialogBox1Data.typeStr !=1" label="收款卡号" prop="destAcctNo">
            <el-input v-model="dialogBox1Data.destAcctNo" clearable autocomplete="off" class="auto-width" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dialogBox1Data.remark" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>

          <el-form-item label="" label-width="100px" class="form-items">
            <el-button :loading="btnLoading" type="primary" @click="addCashOrderInfo('dialogBox1Data')">提交审核</el-button>
            <el-button :loading="btnLoading" @click="dialogBox1=false">取消</el-button>
          </el-form-item>

        </el-form>
      </el-scrollbar>
    </el-dialog>

    <!--    初审-->

    <!--    复审-->

  </div>
</template>
<script>
import finance from '@/axios/default/finance'
import Pagination from '@/components/Pagination'
import rechInfo from '@/axios/default/rechInfo'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'Sign',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      adjustStatusEnum: { 1: '待审核', 2: '初审通过', 3: '初审拒绝', 4: '复审拒绝', 5: '调账完成' },
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        cashStatus: '',
        accountName: ''
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogBox1: false,
      dialogBox1Data: this.initDialog1(),
      dialogBox2: false,
      dialogBox2Data: {},
      loading: false,
      merNameArr: [],
      cashOutArr: [
        {
          type: '1',
          name: '通用'
        },
        {
          type: '5',
          name: '测试勿点'
        },
        // {
        //   type: '9',
        //   name: '文昌汉唐众联科技有限公司'
        // },
        // {
        //   type: '10',
        //   name: '山西云税服科技有限公司'
        // },
        // {
        //   type: '11',
        //   name: '重庆渝税服科技有限公司'
        // }
      ],
      levyIdArr: [],
      channelIdArr: [],
      rechBankNo: {
        balance: 0
      },
      rules: {
        merchantId: [{ required: true, message: '请填写企业名称', trigger: 'blur' }],
        typeStr: [{ required: true, message: '请选择提现主体', trigger: 'change' }],
        levyId: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        channelId: [{ required: true, message: '请选择通道', trigger: 'change' }],
        // balance:[{required:true,message:'请填写企业名称',trigger:'blur'}],
        cashAmount: [{ required: true, message: '请填写提现金额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        remark: [{ required: true, message: '请填写备注', trigger: 'blur' }],
        destBankCode: [{ required: true, message: '请选择收款银行', trigger: 'change' }],
        destAcctName: [{ required: true, message: '请填写收款账户名', trigger: 'blur' }],
        destAcctNo: [{ required: true, message: '请填写收款卡号', trigger: 'blur' }]
      },
      userMoney: '',
      btnLoading: false
    }
  },

  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      finance.cashOrderInfoList({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        console.log(res)
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          console.log(this.listData)
        }
      })
    },
    cashExcel() {
      finance.cashExcel({
        ...this.formData
      }, `提现列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {
        // this.$message.success('操作成功')
      })
    },
    // 提现录入
    addCashOrderInfo(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogBox1Data.typeStr == 1) {
            // 通用提现申请
            const merName = this.merNameArr.find(res => res.id == this.dialogBox1Data.merchantId)
            const channelName = this.channelIdArr.find(res => res.id == this.dialogBox1Data.channelId)
            // const levyName = this.levyIdArr.find(res => res.id == this.dialogData.levyId)
            this.dialogBox1Data.merchantName = merName.merName
            this.dialogBox1Data.channelName = channelName.channel_name
            finance.addCashOrderInfo({
              ...this.dialogBox1Data
            }).then(res => {
              this.btnLoading = false
              this.$refs[formName].clearValidate()
              this.$refs[formName].resetFields()
              if (res.data.code == '0000') {
                this.$message({ type: 'success', message: '操作成功' })
                this.dialogBox1 = false
                this.list()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          } else {
            // 湖南郴税服提现申请
            finance.addCashOrderInfoByYs({
              ...this.dialogBox1Data
            }).then(res => {
              this.btnLoading = false
              this.$refs[formName].clearValidate()
              this.$refs[formName].resetFields()
              if (res.data.code == '0000') {
                this.$message({ type: 'success', message: '操作成功' })
                this.dialogBox1 = false
                this.list()
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 初始化弹框
    initDialog1() {
      return {
        merchantId: '',
        merchantName: '',
        levyId: '',
        levyName: '',
        channelId: '',
        channelName: '',
        canBalance: '',
        cashAmount: '',
        remark: '',
        typeStr: '1' // 1- 通用, 5-湖南郴税服 6-湖南云税服
      }
    },
    // 打开弹框
    openBox1() {
      this.dialogBox1 = true
      this.dialogBox1Data = this.initDialog1()
      this.initAddRechInfo({ queryType: '1', merName: '' }, 'merNameArr')
    },
    onSetCashOut() {
      if (this.dialogBox1Data.typeStr == 1) {
        this.dialogBox1Data = this.initDialog1()
        this.initAddRechInfo({ queryType: this.dialogBox1Data.typeStr, merName: '' }, 'merNameArr')
      } else {
        this.initAddRechInfo({ queryType: this.dialogBox1Data.typeStr, merName: '' }, 'hunan')
      }
    },

    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    // 时间
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    // 查询充值
    // 企业: "queryType":"1",
    // 指定企业: "queryType":"5",
    // 主体: "queryType":"2","merId":"1"
    // 渠道: "queryType":"3","levyId":"1"
    // 充值账号: "queryType":"4","merId":"1"，"channelId":"1"
    initAddRechInfo(data, item) {
      rechInfo.initAddRechInfo(data).then(res => {
        if (res.data.code === '0000') {
          this.loading = false
          this[item] = res.data.data
          const { data } = res.data // 特殊处理
          if ((this[item].constructor === Array && this[item].length === 1) || data.hasOwnProperty('id')) {
            if (item == 'merNameArr') {
              this.dialogBox1Data.merchantId = this[item][0].id
              this.getlevyId()
            } else if (item == 'levyIdArr') {
              this.dialogBox1Data.levyId = this[item][0].id
              this.getChannel()
            } else if (item == 'channelIdArr') {
              this.dialogBox1Data.channelId = this[item][0].id
              this.getMoney()
            } else if (item == 'hunan') {
              const { dialogBox1Data } = this
              this.$set(dialogBox1Data, 'merchantId', data.id)
              this.$set(dialogBox1Data, 'merchantName', data.merName)
              this.$set(dialogBox1Data, 'levyId', data.levyId)
              this.$set(dialogBox1Data, 'levyBodyId', data.levyId)
              this.$set(dialogBox1Data, 'levyName', data.name)
              this.$set(dialogBox1Data, 'channelId', data.channelId)
              this.$set(dialogBox1Data, 'channelName', data.channel_name)
              this.$set(dialogBox1Data, 'canBalance', data.canBalance)
              this.$set(dialogBox1Data, 'bankInfo', data.bankInfo)
            }
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    getlevyId() {
      this.$refs['levyId'].resetField()
      this.$refs['channelId'].resetField()
      this.rechBankNo.balance = 0
      this.initAddRechInfo({ queryType: '2', merId: this.dialogBox1Data.merchantId }, 'levyIdArr')
    },
    mccList(query) {
      if (query !== '') {
        this.loading = true
        this.initAddRechInfo({ queryType: '1', merName: query }, 'merNameArr')
      } else {
        this.merNameArr = []
      }
    },
    getChannel() {
      this.$refs['channelId'].resetField()
      this.rechBankNo.balance = 0
      this.initAddRechInfo({ queryType: '3', merId: this.dialogBox1Data.merchantId, levyId: this.dialogBox1Data.levyId }, 'channelIdArr')
    },
    getMoney() {
      this.rechBankNo.balance = 0
      this.initAddRechInfo({ queryType: '4', merId: this.dialogBox1Data.merchantId, channelId: this.dialogBox1Data.channelId }, 'rechBankNo')
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.auto-width{
  width: 100%;
}

.dialog-form{
  display: flex;
  flex-flow: column nowrap;
}
.el-form-item__content{
  margin-left: 0;
}

.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
}

.dialog-scroll{
  overflow-y: auto;
  height: calc(100%);
}

.reset-dialog{
  /deep/ .el-dialog{
    height: 80vh;
    overflow: hidden;
    .el-dialog__body{
      height: calc(100% - 54px);
    }
  }
  overflow: hidden;
}
.reset-dialog-small{
  /deep/ .el-dialog{
    height: 65vh;
  }
  overflow: hidden;
}
.form-style{
  padding-right: 20px;
}

</style>
