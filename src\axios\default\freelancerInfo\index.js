// 自由职业者
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  list: `${API_Header}/freelancerInfo/list`, // 自由职业者列表
  addFreelancerInfo: `${API_Header}/freelancerInfo/addFreelancerInfo`, // 新增自由职业者
  delete: `${API_Header}/freelancerInfo/delete`, // 删除自由职业者
  update: `${API_Header}/freelancerInfo/update`, // 修改自由职业者
  getFreelancerInfo: `${API_Header}/freelancerInfo/getFreelancerInfo`, // 修改自由职业者
  batchImportFreeLeader: `${API_Header}/upload/batchImportFreeLeader`, // 批量上传自由职业者
  freelancerDownLoadData: `${API_Header}/freelancerInfo/freelancerDownLoadData`, // 下载自由职业者信息
  import: `${API_Header}/freelancerInfo/import`, // 上传身份证图片压缩包
  download: `${API_Header}/freelancerInfo/download`, // 下载身份证图片
  downloadContract: `${API_Header}/freelancerInfo/downloadContract`, // 下载合同
  freelancerFailDownLoadData: `${API_Header}/freelancerInfo/freelancerFailDownLoadData`, // 下载自由职业者信息
  downloadOneContract: `${API_Header}/freelancerInfo/downloadOneContract` // 下载合同
}

const freelancerInfo = {
  list: (params) => {
    return request.postJson(api.list, params)
  },
  addFreelancerInfo: (params) => {
    return request.postJson(api.addFreelancerInfo, params)
  },
  delete: (params) => {
    return request.postJson(api.delete, params)
  },
  update: (params) => {
    return request.postJson(api.update, params)
  },
  getFreelancerInfo: (params) => {
    return request.postJson(api.getFreelancerInfo, params)
  },
  batchImportFreeLeader: (params) => {
    return request.postJson(api.batchImportFreeLeader, params)
  },
  freelancerDownLoadData: (params, filename, type) => {
    return request.downFiles(
      api.freelancerDownLoadData,
      params,
      filename,
      type
    )
  },
  import: (params) => {
    return request.postJson(api.import, params)
  },
  download: (params, filename, type) => {
    return request.downFiles(api.download, params, filename, type)
  },
  downloadContract: (params, filename, type) => {
    return request.downFiles(api.downloadContract, params, filename, type)
  },
  freelancerFailDownLoadData: (params, filename, type) => {
    return request.downFiles(
      api.freelancerFailDownLoadData,
      params,
      filename,
      type
    )
  },
  downloadOneContract: (params, filename, type) => {
    return request.downFiles(api.downloadOneContract, params, filename, type)
  }
}

export default freelancerInfo
