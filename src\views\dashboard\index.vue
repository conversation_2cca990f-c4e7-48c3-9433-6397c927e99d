<template>
  <div class="content-main">
    <div v-hasPermission="['index:show-welcome']" class="welcome">
      您好，欢迎登陆链接云运营系统！
    </div>

    <div v-hasNoPermission="['index:noShow-statistical']" class="panel-box">
      <div class="grid-content bg-item-1">
        <div class="card-name">
          <span>开票金额统计</span>
        </div>
        <div class="grid-content-text">
          <div class="card-num">
            <span class="card-title">已开票金额:</span>
            <countTo
              class="countTo totalVisit"
              :decimals="2"
              :start-val="0"
              :end-val="homeDataList.invoicedSum"
              :duration="1500"
            />
            元
          </div>
          <div class="card-num">
            <span class="card-title">预开票金额:</span>
            <countTo
              class="countTo totalVisit"
              :decimals="2"
              :start-val="0"
              :end-val="homeDataList.preInvoiceSum"
              :duration="1500"
            />
            元
          </div>
        </div>
      </div>

      <div class="grid-content bg-item-2">
        <div class="card-name">
          <span>充值统计</span>
        </div>
        <div>
          <div class="card-num">
            本月充值：
            <countTo
              class="countTo totalVisit"
              :decimals="2"
              :start-val="0"
              :end-val="homeDataList.rechargeMonthSum"
              :duration="1500"
            />
            元
          </div>
          <div class="card-num">
            本日充值：
            <countTo
              class="countTo totalVisit"
              :decimals="2"
              :start-val="0"
              :end-val="homeDataList.rechargeTodaySum"
              :duration="1500"
            />
            元
          </div>
        </div>
      </div>

      <div class="grid-content bg-item-3">
        <div class="card-name">
          <span>结算统计</span>
        </div>
        <div>
          <div class="card-num">
            本月结算：
            <countTo
              class="countTo totalVisit"
              :decimals="decimals"
              :start-val="0"
              :end-val="homeDataList.settleMonthSum"
              :duration="1500"
            />
            元
          </div>
          <div class="card-num">
            本日结算：
            <countTo
              class="countTo totalVisit"
              :decimals="decimals"
              :start-val="0"
              :end-val="homeDataList.settleTodaySum"
              :duration="1500"
            />
            元
          </div>
        </div>
      </div>

      <!-- <el-col :span="6">
          <div class="grid-content bg-item-4">
            <div class="card-name">
              <span>开票统计</span>
            </div>
            <div>
              <div class="card-num"> 本月开票数量：
                <countTo class="countTo totalVisit" :decimals="0" :start-val="0" :end-val="homeDataList.invoiceCount"
                         :duration="1500"/>
                张
              </div>
              <div class="card-num"> 待开票金额：
                <countTo class="countTo totalVisit" :decimals="decimals" :start-val="0"
                         :end-val="homeDataList.waitInvoiceSum" :duration="1500"/>
                元
              </div>
            </div>
          </div>
        </el-col> -->
    </div>

    <div ref="visitcountchart">
      <el-row :gutter="20">
        <el-col
          v-hasNoPermission="['index:noShow-echart']"
          :xs="24"
          :sm="24"
          :md="18"
          :lg="17"
          :xl="17"
          style="height: calc(100%)"
        >
          <div class="chart-container">
            <!--<p class="echars-name"> <span>TOP10</span></p>-->
            <div class="tab_wrap">
              <div>
                <el-button
                  size="mini"
                  type="primary"
                  :plain="activeName !== 'shsj'"
                  @click="handleClick('shsj')"
                  >商户数据</el-button
                >
                <el-button
                  size="mini"
                  type="primary"
                  :plain="activeName !== 'xssj'"
                  @click="handleClick('xssj')"
                  >销售数据</el-button
                >
              </div>
              <el-form :inline="true">
                <el-form-item label="时间范围">
                  <el-select
                    v-model="time"
                    placeholder="请选择"
                    class="auto-width"
                    @change="changeQuery()"
                  >
                    <el-option label="请选择" value="" />
                    <el-option label="本月" value="1" />
                    <el-option label="上月" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="类型">
                  <el-select
                    v-model="type"
                    placeholder="请选择"
                    class="auto-width"
                    @change="changeQuery()"
                  >
                    <el-option label="请选择" value="" />
                    <el-option label="充值" value="1" />
                    <el-option label="结算" value="2" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>

            <div id="visit-count-chart" />
          </div>
        </el-col>
        <el-col
          v-hasNoPermission="['index:noShow-todo']"
          :xs="24"
          :sm="24"
          :md="6"
          :lg="7"
          :xl="7"
        >
          <div class="todo-container">
            <div class="table-name table-name-pd">待办事项</div>
            <div class="thing-table">
              <div class="thing-table-ul">
                <router-link class="item" to="/recharge/trial">
                  <div class="thing-table-li">充值初审任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.RECH_AUDIT_1 }}
                </div>
              </div>
              <div class="thing-table-ul">
                <router-link class="item" to="/recharge/review">
                  <div class="thing-table-li">充值复审任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.RECH_AUDIT_3 }}
                </div>
              </div>
              <div class="thing-table-ul">
                <router-link class="item" to="/balance/trial">
                  <div class="thing-table-li">结算初审任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.TRADE_OPERATE_1 }}
                </div>
              </div>
              <div class="thing-table-ul">
                <router-link class="item" to="/balance/review">
                  <div class="thing-table-li">结算复审任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.TRADE_OPERATE_3 }}
                </div>
              </div>
              <div class="thing-table-ul">
                <router-link class="item" to="/invoice/trial">
                  <div class="thing-table-li">开票初审任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.INVOICE_AUDIT_1 }}
                </div>
              </div>
              <div class="thing-table-ul">
                <router-link class="item" to="/invoice/review">
                  <div class="thing-table-li">开票复审任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.INVOICE_AUDIT_3 }}
                </div>
              </div>
              <div class="thing-table-ul">
                <router-link class="item" to="/invoice/upload">
                  <div class="thing-table-li">开票上传任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.INVOICE_AUDIT_5 }}
                </div>
              </div>
              <div class="thing-table-ul">
                <router-link class="item" to="/invoice/check">
                  <div class="thing-table-li">开票审核任务</div>
                </router-link>
                <div class="thing-table-li">
                  {{ getWaitDealtList.INVOICE_AUDIT_6 }}
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import echarts from "echarts";
import { newAlert } from "@/utils";
import countTo from "vue-count-to";
import resize from "@/components/Charts/mixins/resize";
import index from "@/axios/default/index/index";

export default {
  name: "Dashboard",
  components: { countTo },
  mixins: [resize],
  data() {
    return {
      type: "1",
      time: "1",
      activeName: "shsj",
      chart: "",
      decimals: 2,
      homeDataList: {
        personCount: 0,
        personMonthCount: 0,
        rechargeMonthSum: 0,
        rechargeTodaySum: 0,
        settleMonthSum: 0,
        settleTodaySum: 0,
        invoiceCount: 0,
        waitInvoiceSum: 0,
      },
      getWaitDealtList: {},
    };
  },
  computed: {},
  mounted() {
    this.getHomeData();
    this.getRechargeTop10();
    this.getWaitDealt();
  },

  methods: {
    initTime(type) {
      const now = new Date(); // 当前日期
      const nowMonth = now.getMonth(); // 当前月
      let nowYear = now.getYear(); // 当前年
      nowYear += nowYear < 2000 ? 1900 : 0;
      const lastMonthDate = new Date(); // 上月日期
      lastMonthDate.setDate(1); // 设置日期为本月1号
      lastMonthDate.setMonth(lastMonthDate.getMonth() - 1); // 设置日期为上个月
      const lastMonth = lastMonthDate.getMonth();

      function formatDate(date) {
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();

        if (mymonth < 10) {
          mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
          myweekday = "0" + myweekday;
        }
        return myyear + "-" + mymonth + "-" + myweekday;
      }

      function getMonthDays(myMonth) {
        const monthStartDate = new Date(nowYear, myMonth, 1);
        const monthEndDate = new Date(nowYear, myMonth + 1, 1);
        const days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24);
        return days;
      }

      if (type === 1) {
        // 获得本月的开始日期
        const monthStartDate = new Date(nowYear, nowMonth, 1);
        const monthEndDate = new Date(
          nowYear,
          nowMonth,
          getMonthDays(nowMonth)
        );
        return [
          formatDate(monthStartDate) + " 00:00:00",
          formatDate(monthEndDate) + " 23:59:59",
        ];
      } else {
        // 获得上月开始时间
        const date = new Date();
        const mymonth = date.getMonth() + 1;
        console.log(mymonth);
        const lastMonthStartDate = new Date(
          mymonth === 1 ? nowYear - 1 : nowYear,
          lastMonth,
          1
        );
        const lastMonthEndDate = new Date(
          mymonth === 1 ? nowYear - 1 : nowYear,
          lastMonth,
          getMonthDays(lastMonth)
        );
        return [
          formatDate(lastMonthStartDate) + " 00:00:00",
          formatDate(lastMonthEndDate) + " 23:59:59",
        ];
      }
    },
    // homeData
    getHomeData() {
      index.getHomeData().then((res) => {
        if (res.data.code === "0000") {
          const { data } = res.data;
          this.homeDataList = data;
        } else {
          newAlert(this.$tips, res.data.message);
        }
      });
    },
    handleClick(tab) {
      if (tab === "shsj") {
        this.getRechargeTop10();
      } else {
        this.getRechargeTop10ByAgent();
      }
      this.activeName = tab;
    },
    changeQuery() {
      if (this.activeName === "shsj") {
        this.getRechargeTop10();
      } else {
        this.getRechargeTop10ByAgent();
      }
    },
    getRechargeTop10ByAgent() {
      index
        .getRechargeTop10ByAgent({
          type: this.type,
          timeFrom: this.initTime(this.time === "1" ? 1 : 2)[0],
          timeTo: this.initTime(this.time === "1" ? 1 : 2)[1],
        })
        .then((res) => {
          if (res.data.code === "0000") {
            const { data } = res.data;
            const xData = [];
            const yData = [];
            data.forEach((res) => {
              xData.push(res.name);
              yData.push(res.moneySum);
            });
            console.log(yData);
            const option = {
              tooltip: {
                formatter: function (params) {
                  return `<p>销售名称：${params.name}</p> <p>充值金额：${params.data}元</p>`;
                },
              },
              grid: {
                // show:true,//是否显示直角坐标系网格。[ default: false ]
                top: "20px",
                left: "120px", // grid 组件离容器左侧的距离。
                bottom: "100px",
                right: "40px",
                borderColor: "#333333", // 网格的边框颜色
              },
              xAxis: {
                data: xData,
                axisLabel: {
                  // 坐标轴刻度标签的相关设置。
                  rotate: "20",
                  fontWeight: "normal",
                  width: "10%",
                },
              },
              yAxis: {
                type: "value",
              },
              series: [
                {
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                      {
                        offset: 0,
                        color: "#ff4242",
                      },
                      { offset: 1, color: "#FF9975" },
                    ]),
                    barBorderRadius: [20],
                  },
                  name: "金额",
                  data: yData,
                  type: "bar",
                  barWidth: "20px",
                },
              ],
            };

            const height =
              this.$refs.visitcountchart.getBoundingClientRect().height;
            const dom = document.getElementById("visit-count-chart");
            dom.style.height = height + 60 + "px";
            this.chart = echarts.init(dom);
            this.chart.setOption(option);
            this.chart.on("click", (params) => {
              const startTime = this.initTime(this.time === "1" ? 1 : 2)[0];
              const endTime = this.initTime(this.time === "1" ? 1 : 2)[1];
              if (this.type === "1") {
                this.$router.push(
                  `/recharge/list?pageType=${
                    this.activeName === "shsj" ? 1 : 2
                  }&name=${
                    params.name
                  }&startTime=${startTime} &endTime=${endTime}`
                );
              } else {
                this.$router.push(
                  `/balance/list?pageType=${
                    this.activeName === "shsj" ? 1 : 2
                  }&name=${
                    params.name
                  }&startTime=${startTime} &endTime=${endTime}`
                );
              }
              // 可以根据点击的位置进行相关操作或跳转页面等
            });
          } else {
            newAlert(this.$tips, res.data.message);
          }
        });
    },
    getRechargeTop10() {
      index
        .getRechargeTop10({
          type: this.type,
          timeFrom: this.initTime(this.time === "1" ? 1 : 2)[0],
          timeTo: this.initTime(this.time === "1" ? 1 : 2)[1],
        })
        .then((res) => {
          if (res.data.code === "0000") {
            const { data } = res.data;
            const xData = [];
            const yData = [];
            data.forEach((res) => {
              xData.push(res.name);
              yData.push(res.moneySum);
            });
            console.log(yData);
            const option = {
              tooltip: {
                formatter: function (params) {
                  return `<p>企业名称：${params.name}</p> <p>充值金额：${params.data}元</p>`;
                },
              },
              grid: {
                // show:true,//是否显示直角坐标系网格。[ default: false ]
                top: "20px",
                left: "120px", // grid 组件离容器左侧的距离。
                bottom: "100px",
                right: "40px",
                borderColor: "#333333", // 网格的边框颜色
              },
              xAxis: {
                data: xData,
                axisLabel: {
                  // 坐标轴刻度标签的相关设置。
                  rotate: "20",
                  fontWeight: "normal",
                  width: "10%",
                },
              },
              yAxis: {
                type: "value",
              },
              series: [
                {
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                      {
                        offset: 0,
                        color: "#ff4242",
                      },
                      { offset: 1, color: "#FF9975" },
                    ]),
                    barBorderRadius: [20],
                  },
                  name: "金额",
                  data: yData,
                  type: "bar",
                  barWidth: "20px",
                },
              ],
            };

            const height =
              this.$refs.visitcountchart.getBoundingClientRect().height;
            const dom = document.getElementById("visit-count-chart");
            dom.style.height = height + 60 + "px";
            this.chart = echarts.init(dom);
            this.chart.setOption(option);
            this.chart.on("click", (params) => {
              console.log(params);
              const startTime = this.initTime(this.time === "1" ? 1 : 2)[0];
              const endTime = this.initTime(this.time === "1" ? 1 : 2)[1];
              if (this.type === "1") {
                this.$router.push(
                  `/recharge/list?pageType=${
                    this.activeName === "shsj" ? 1 : 2
                  }&name=${
                    params.name
                  }&startTime=${startTime} &endTime=${endTime}`
                );
              } else {
                this.$router.push(
                  `/balance/list?pageType=${
                    this.activeName === "shsj" ? 1 : 2
                  }&name=${
                    params.name
                  }&startTime=${startTime} &endTime=${endTime}`
                );
              }
              // 可以根据点击的位置进行相关操作或跳转页面等
            });
          } else {
            newAlert(this.$tips, res.data.message);
          }
        });
    },
    getWaitDealt() {
      index.getWaitDealt().then((res) => {
        if (res.data.code === "0000") {
          const { data } = res.data;
          this.getWaitDealtList = data;
        } else {
          newAlert(this.$tips, res.data.message);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.demo-form-inline {
  display: flex;
  align-items: center;
}

.content-main {
  display: flex;
  flex-flow: column nowrap;
  overflow-y: scroll;
}

.chart-container,
.todo-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.05);
  padding: 20px;
  height: 100%;
  margin-bottom: 20px;
}

.tab_wrap {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20px;

  @media screen and (max-width: 768px) {
    flex-direction: column;

    .el-form {
      margin-top: 15px;
    }
  }
}

.panel-box {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  gap: 20px;
  margin-bottom: 20px;
}

.grid-content {
  overflow: hidden;
  border-radius: 4px;
  height: 125px;
  box-sizing: border-box;
  padding: 20px 20px;
  color: #fff;
  display: flex;
  flex-flow: column nowrap;
  .grid-content-text {
    display: flex;
    flex-flow: column nowrap;
  }
  .card-name {
    padding-bottom: 6px;
    display: flex;
    align-items: center;
    font-weight: 500;
    justify-content: space-between;
    font-size: 17px;
  }
  .view-more {
    color: #fff;
  }
  .card-title {
    text-align: right;
  }
  .card-num {
    font-size: 11px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 10px;
    line-height: 20px;
    .countTo {
      font-size: 20px;
      font-weight: bold;
      color: #ffffff;
    }
  }
  .card-des {
    font-size: 14px;
  }
}

.bg-item-1 {
  background: linear-gradient(180deg, #005aff 0%, #00a2ff 100%);
}

.bg-item-2 {
  background: linear-gradient(180deg, #b507ff 0%, #bb81f6 100%);
}

.bg-item-3 {
  background: linear-gradient(180deg, #ff003c 0%, #ff7b7b 100%);
}

.bg-item-4 {
  background: linear-gradient(180deg, #c8773d 0%, #f3b77b 100%);
}

.table-name {
  font-size: 17px;
  font-weight: 600;
  color: #181f2d;
}

.table-name-pd {
  padding-bottom: 15px;
}

#visit-count-chart {
  margin-bottom: 20px;

  @media screen and (max-width: 768px) {
    margin-bottom: 30px;
  }
}

.thing-table {
  box-sizing: border-box;
  border: 1px solid rgba(32, 53, 128, 0.16);
}

.thing-table-ul {
  display: flex;
  border-bottom: 1px solid rgba(32, 53, 128, 0.16);
}

.thing-table-li {
  flex: 1;
  padding: 15px;
  font-size: 13px;
  font-weight: 400;
  color: #11142d;

  &:nth-child(2n) {
    border-left: 1px solid rgba(32, 53, 128, 0.16);
  }
}

.thing-table-ul:last-child {
  border-bottom: none;
}

.content-main,
.welcome {
  height: calc(100%);
  padding-bottom: 15px;
}

.welcome {
  background: #fff;
  text-align: center;
  padding-top: 10%;
  box-sizing: border-box;
}

.right_content,
.left_content {
  box-sizing: border-box;
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 21px 0px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
}

.right_content {
  width: 25%;
}

.left_content {
  margin-right: 20px;
}

.echars-name {
  font-size: 17px;
  font-weight: 600;
  color: #181f2d;
  span {
    color: #ff6628;
  }
}

/* 移动端优化 */
@media screen and (max-width: 768px) {
  .thing-table {
    margin-bottom: 20px;
  }

  .chart-container,
  .todo-container {
    margin-bottom: 20px;
  }
}
</style>
