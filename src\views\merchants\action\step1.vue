<template>
  <el-form ref="step1Form" size="mini" :model="formData" label-width="140px" label-position="right" :rules="rules" :disabled="viewMerchant">
    <div class="tab-box">
      <div class="sub-title">
        <i class="el-icon-collection" />
        基本信息</div>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="用户名" class="form-items" prop="loginName">
            <el-input v-model="formData.loginName" class="auto-width" placeholder="请输入商户系统登录用户名" :disabled="id ? true : false" />
            <p class="remake-box">商户系统登录名</p>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="商户编号" class="form-items">
            <el-input v-model="formData.merNo" class="auto-width" placeholder="默认自动生成" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="认证邮箱" class="form-items" prop="authEmail">
            <el-input v-model="formData.authEmail" placeholder="用户接收商户密码的邮箱" class="auto-width" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否预开" class="form-items" prop="preOpenFlag">
            <el-radio v-model="formData.preOpenFlag" :label="1">是</el-radio>
            <el-radio v-model="formData.preOpenFlag" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="认证手机号" class="form-items" prop="authMobile">
            <el-input v-model="formData.authMobile" class="auto-width" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="项目负责人" class="form-items" prop="projectLeaderId">
            <el-select v-model="formData.projectLeaderId" placeholder="请选择" class="auto-width">
              <el-option
                v-for="item in leaderArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="销售负责人" class="form-items" prop="saleId">
            <el-select v-model="formData.saleId" placeholder="请选择" class="auto-width" @change="initAgentInfo">
              <el-option
                v-for="item in saleArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :xs="24">
          <el-form-item label="所属代理" class="form-items" prop="agentId">
            <el-select v-model="formData.agentId" filterable placeholder="请选择" class="auto-width" clearable>
              <el-option
                v-for="item in agentArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
<!--      <el-row :gutter="20">-->
<!--        <el-col :lg="12" :xs="24">-->
<!--          <el-form-item label="业务联系电话" class="form-items" prop="businessMobile">-->
<!--            <el-input v-model="formData.businessMobile" class="auto-width" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :lg="12" :xs="24">-->
<!--          <el-form-item label="业务联系人" class="form-items" prop="businessName">-->
<!--            <el-input v-model="formData.businessName" class="auto-width" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="企业名称" class="form-items" prop="merName">
            <el-input v-model="formData.merName" placeholder="请输入企业名称 " class="auto-width" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="注册地址" class="form-items" prop="regAddr">
            <el-input v-model="formData.regAddr" class="auto-width" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="纳税识别号" class="form-items" prop="taxNo">
            <el-input v-model="formData.taxNo" class="auto-width" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="电话" class="form-items" prop="taxMobile">
            <el-input v-model="formData.taxMobile" class="auto-width" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="开户行" class="form-items" prop="taxOpenBank">
            <el-input v-model="formData.taxOpenBank" class="auto-width" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="银行卡号" class="form-items" prop="taxAcc">
            <el-input v-model="formData.taxAcc" class="auto-width" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="邮寄联系人" class="form-items" prop="postName">
            <el-input v-model="formData.postName" class="auto-width" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="邮寄联系电话" class="form-items" prop="postMobile">
            <el-input v-model="formData.authMobile" class="auto-width" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="邮寄地址" class="form-items" prop="contractPostAddr">
            <el-input v-model="formData.contractPostAddr" class="auto-width" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="行业类型" class="form-items" prop="industryNo">
            <el-select
              v-model="formData.industryNo"
              class="auto-width"
              filterable
              remote
              clearable
              placeholder="请选择行业类型"
              :remote-method="industryList"
              :loading="loading"
            >
              <el-option
                v-for="item in invoiceTypeIdArr2"
                :key="item.industryCode"
                :label="item.industryDesc"
                :value="item.industryCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="合同编号" class="form-items" prop="contractNo">
            <el-input v-model="formData.contractNo" class="auto-width" placeholder="请填写合同编号" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="法人姓名" class="form-items" prop="legalName">
            <el-input v-model="formData.legalName" class="auto-width" placeholder="请填写法人姓名" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="法人证件号" class="form-items" prop="legalCardNo">
            <el-input v-model="formData.legalCardNo" class="auto-width" placeholder="请填写法人证件号" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="法人电话" class="form-items" prop="legalPhone">
            <el-input v-model="formData.legalPhone" class="auto-width" placeholder="请填写法人电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="12">
          <el-form-item label="结算短信开关" class="form-items" prop="isSendMsg">
            <el-radio v-model="formData.isSendMsg" :label="1">开</el-radio>
            <el-radio v-model="formData.isSendMsg" :label="0">关</el-radio>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="风控资料是否完整" class="form-items" prop="isRisk">
            <el-radio v-model="formData.isRisk" :label="1">是</el-radio>
            <el-radio v-model="formData.isRisk" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否充值短信提醒" class="form-items" prop="isSendMsgRecharge">
            <el-radio v-model="formData.isSendMsgRecharge" :label="1">是</el-radio>
            <el-radio v-model="formData.isSendMsgRecharge" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否可开专票" class="form-items" prop="isSpecialTicket">
            <el-radio v-model="formData.isSpecialTicket" :label="1">是</el-radio>
            <el-radio v-model="formData.isSpecialTicket" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="企业简称" class="form-items" prop="shortName">
            <el-input v-model="formData.shortName" class="auto-width" placeholder="请填写企业简称" />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>
<script>
import merchants from '@/axios/default/merchants'
import publics from '@/axios/default/public'
const getQueryObject = function(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}
const loginNameRequired = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入用户名'))
  } else {
    merchants.checkLoginName({
      loginName: value,
      id: getQueryObject().id || '-1'
    }).then(res => {
      if (res.data.code == '0000') {
        callback()
      } else {
        callback(new Error('用户名已被占用'))
      }
    })
  }
}

const checkPhone = (rule, value, callback) => {
  const reg = /^1[0-9]\d{9}$/
  if (reg.test(value)) {
    callback()
  } else {
    return callback(new Error('请输入正确的手机号'))
  }
}

const merChantsRequired = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入企业名称'))
  } else {
    publics.checkMerName({
      merName: value,
      id: getQueryObject().id || '-1'
    }).then(res => {
      if (res.data.code == '0000') {
        callback()
      } else {
        callback(new Error('企业名称已被占用'))
      }
    })
  }
}

export default {
  name: 'Step1',
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    },
    id: {
      default: ''
    },
    viewMerchant: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      invoiceTypeIdArr: [],
      invoiceTypeIdArr2: [],
      levyItemsArr: [],
      leaderArr: [],
      saleArr: [],
      agentArr: [],
      rules: {
        loginName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
          { validator: loginNameRequired, trigger: 'blur' }
        ],
        authEmail: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        preOpenFlag: [
          { required: true, message: '请选择是否预开', trigger: 'change' }
        ],
        authMobile: [
          { required: true, message: '请输入认证手机', trigger: 'blur' },
          { validator: checkPhone, trigger: ['blur', 'change'] }
        ],
        projectLeaderId: [
          { required: true, message: '请选择项目负责人', trigger: 'change' }
        ],
        saleId: [
          { required: true, message: '请选择销售负责人', trigger: 'change' }
        ],
        // businessMobile: [
        //   { required: true, message: '请输入业务联系电话', trigger: 'blur' }
        // ],
        // businessName: [
        //   { required: true, message: '请输入业务联系人', trigger: 'blur' }
        // ],
        agentId: [
          { required: true, message: '请选择所属代理', trigger: 'change' }
        ],
        merName: [
          { required: true, message: '请填写企业名称', trigger: 'blur' },
          { validator: merChantsRequired, trigger: 'blur' }
        ],
        regAddr: [
          { required: true, message: '请填写注册地址', trigger: 'blur' }
        ],
        taxNo: [
          { required: true, message: '请填写纳税识别号', trigger: 'blur' }
        ],
        taxMobile: [
          { required: true, message: '请填写电话', trigger: 'blur' }
        ],
        taxOpenBank: [
          { required: true, message: '请填写开户行', trigger: 'blur' }
        ],
        taxAcc: [
          { required: true, message: '请填写银行卡号', trigger: 'blur' }
        ],
        postName: [
          { required: true, message: '请填写邮寄联系人', trigger: 'blur' }
        ],
    //    postMobile: [
      //    { required: true, message: '请填写邮寄联系电话', trigger: 'blur' }
      //  ],
        contractPostAddr: [
          { required: true, message: '请填写邮寄地址', trigger: 'blur' }
        ],
        // invoiceTypeId: [
        //   { required: true, message: '请选择报税行业类型', trigger: 'change' }
        // ],
        // itemCode: [
        //   { required: true, message: '请填写开票品类', trigger: 'blur' }
        // ],
        industryNo: [
          { required: false, message: '请选择行业类型', trigger: 'blur' }
        ],
        contractNo: [
          { required: false, message: '请填写合同编号', trigger: 'blur' }
        ],
        legalName: [
          { required: false, message: '请填写法人姓名', trigger: 'blur' }
        ],
        legalCardNo: [
          { required: false, message: '请填写法人证件号', trigger: 'blur' }
        ],
        legalPhone: [
          { required: false, message: '请填写法人手机号', trigger: 'blur' }
        ],
        isSendMsg: [
          { required: true, message: '推送结算短信提现开关', trigger: 'change' }
        ],
        isRisk: [
          { required: true, message: '请选择风控开关', trigger: 'change' }
        ],
        isSendMsgRecharge: [
          { required: true, message: '请选择是否充值短信提醒', trigger: 'change' }
        ],
        isSpecialTicket: [
          { required: true, message: '请选择是否可开专票', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    formData: {
      get() {
        return this.data
      },
      set(val) {

      }
    }
  },
  watch: {
    '$route.path': {
      handler(newName, oldName) {
        this.projectLeader() // 项目负责人
        this.initSaleInfo() // 销售
      }
    },

    data(n, o) {
      if (n.id) {
        this.mccList(this.formData.invoiceTypeName)
        this.industryList(this.formData.industryTypeName)
        this.queryLevyItems(this.formData.itemCodeName)
      } else {
        this.mccList() // 类目
        this.industryList()
        this.queryLevyItems() // 品目
      }
    }
  },

  mounted() {
    this.projectLeader() // 项目负责人
    this.initSaleInfo() // 销售
  },
  methods: {
    // 项目负责人
    projectLeader() {
      publics.projectAllList().then(res => {
        this.leaderArr = res.data.data
      })
    },
    // 销售
    initSaleInfo() {
      publics.saleAllList().then(res => {
        this.saleArr = res.data.data
      })
    },
    // 代理
    initAgentInfo(id) {
      this.formData.agentId = ''
      publics.agentAllList({
        id
      }).then(res => {
        this.agentArr = res.data.data
      })
    },

    // 报税行业类型
    mccList(query) {
      this.loading = true
      publics.mccList({
        mccName: query
      }).then(res => {
        this.loading = false
        this.invoiceTypeIdArr = res.data.data
      })
    },

    industryList(query) {
      this.loading = true
      publics.industry({
        industryDesc: query
      }).then(res => {
        this.loading = false
        this.invoiceTypeIdArr2 = res.data.data
      })
    },

    // 查询征收品目列表
    queryLevyItems(query) {
      this.loading = true
      publics.levyItems({
        itemName: query
      }).then(res => {
        this.loading = false
        this.levyItemsArr = res.data.data
      })
    },
    step1Form() {
      return this.$refs.step1Form.validate()
    }
  }
}
</script>
<style scoped>
  .auto-width{
    width: 100%;
  }
  .remake-box{
    padding: 0;
    margin: 0;
    line-height: 20px;
    font-size: 12px;
    color: #9a9a9a;
  }
  .sub-title{
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 20px;
    background: #efefef;
    padding: 10px;
    margin-top: 20px;
  }
  .sub-title:first-child{
    margin-top: 0;
  }
</style>
