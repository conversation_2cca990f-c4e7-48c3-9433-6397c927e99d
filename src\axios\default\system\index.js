// 系统管理API
import request from '@/axios/default/request'
import { API_system, API_Header } from '../index'

const api = {
  // 数据导出相关接口
  downTradeOrderDetailsForTq: `${API_system}/upload/downTradeOrderDetailsForTq`, // 下载交易订单明细

  // 数据上传相关接口
  batchImportOfExternalData: `${API_system}/upload/batchImportOfExternalData`, // 批量导入外部数据

  // 隐式调用接口
  findProjectByMerLev: `${API_Header}/projectInfo/findProjectByMerLev`, // 根据商户级别查找项目信息

  // 表单数据获取接口
  getMerchantList: `${API_Header}/merchantInfo/list`, // 获取商户列表
  getLevyList: `${API_Header}/levyBody/list`, // 获取代征主体列表
  getChannelList: `${API_Header}/payChannel/list`, // 获取充值通道列表
  getMoneyInfo: `${API_Header}/money/info` // 获取金额信息
}

const system = {
  // 下载交易订单明细 - 使用form-data格式
  downTradeOrderDetailsForTq: (params) => {
    const formData = new FormData()
    Object.keys(params).forEach(key => {
      formData.append(key, params[key])
    })

    // 使用新添加的uploadDownload方法，既保持token认证又支持FormData和blob响应
    return request.uploadDownload(api.downTradeOrderDetailsForTq, formData)
  },

  // 批量导入外部数据 - 使用form-data格式
  batchImportOfExternalData: (formData) => {
    return request.upload(
      api.batchImportOfExternalData,
      formData
    )
  },

  // 隐式调用 - 根据商户级别查找项目信息
  findProjectByMerLev: () => {
    const params = {
      'merchanmtId': 2167,
      'levyId': 17
    }
    return request.postJson(api.findProjectByMerLev, params)
  },

  // 获取商户列表
  getMerchantList: (params) => {
    return request.get(api.getMerchantList, params)
  },

  // 获取代征主体列表
  getLevyList: (params) => {
    return request.get(api.getLevyList, params)
  },

  // 获取充值通道列表
  getChannelList: (params) => {
    return request.get(api.getChannelList, params)
  },

  // 获取金额信息
  getMoneyInfo: (params) => {
    return request.get(api.getMoneyInfo, params)
  },

  // 组合方法：下载完成后自动调用隐式接口
  downloadAndCallHidden: async(downloadParams) => {
    try {
      // 先执行下载
      const downloadResult = await system.downTradeOrderDetailsForTq(downloadParams)

      // 下载成功后调用隐式接口
      const hiddenResult = await system.findProjectByMerLev()

      return {
        downloadResult,
        hiddenResult
      }
    } catch (error) {
      console.log(error)
      throw error
    }
  }
}

export default system
