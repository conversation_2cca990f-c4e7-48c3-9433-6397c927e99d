// 充值管理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  insertRechInfo: `${API_Header}/rechInfo/insertRechInfo`, // 充值保存接口
  queryRechInfosByPage: `${API_Header}/rechInfo/queryRechInfosByPage`, // 充值查询/初审/复审列表接口
  auditRechInfo: `${API_Header}/rechInfo/auditRechInfo`, // 充值审批接口
  initAddRechInfo: `${API_Header}/rechInfo/initAddRechInfo`, // 查询充值企业/主体/渠道/账号接口
  queryRechAbnormalInfo: `${API_Header}/rechAbnormalInfo/queryRechAbnormalInfo`, // 充值异常列表查询
  queryRechAbnomalById: `${API_Header}/rechAbnormalInfo/queryRechAbnomalById`, // 查询充值异常详情
  updateRechAbnomal: `${API_Header}/rechAbnormalInfo/updateRechAbnomal`, // 充值异常审核功能
  downRechInfo: `${API_Header}/rechInfo/downRechInfo`, // 充值异常审核功能
  checkRechRecord: `${API_Header}/rechInfo/checkRechRecord`, // 充值异常审核功能
  rechInfoImport: `${API_Header}/rechInfo/import`, // 充值电子回单上传
  findSendMsgBalance: `${API_Header}/merchantInfo/findSendMsgBalance`, // 查询余额短信通知信息列表
  addSendMsgBalance: `${API_Header}/merchantInfo/addSendMsgBalance`, // 添加余额短信通知信息
  findMerchantMsgById: `${API_Header}/merchantInfo/findMerchantMsgById`, // 查询余额短信通知信息详情

  findSendMsgChannel: `${API_Header}/merchantInfo/findSendMsgChannel`, // 查询某商户下需要提醒余额不足的通道列表
  addSendMsgChannel: `${API_Header}/merchantInfo/addSendMsgChannel`, // 添加某商户下通道余额不足提醒
  deleteSendMsgChannel: `${API_Header}/merchantInfo/deleteSendMsgChannel`, // 删除某商户余额提醒通道
  sendMsgBalanceManual: `${API_Header}/merchantInfo/sendMsgBalanceManual` // 手动发送余额不足短信通知

}

const rechInfo = {
  insertRechInfo: params => {
    return request.postJson(api.insertRechInfo, params)
  },
  queryRechInfosByPage: params => {
    return request.postJson(api.queryRechInfosByPage, params)
  },
  auditRechInfo: params => {
    return request.postJson(api.auditRechInfo, params)
  },
  initAddRechInfo: params => {
    return request.postJson(api.initAddRechInfo, params)
  },
  queryRechAbnormalInfo: params => {
    return request.postJson(api.queryRechAbnormalInfo, params)
  },
  queryRechAbnomalById: params => {
    return request.postJson(api.queryRechAbnomalById, params)
  },
  updateRechAbnomal: params => {
    return request.postJson(api.updateRechAbnomal, params)
  },
  downRechInfo: (params, filename, type) => {
    return request.downFiles(api.downRechInfo, params, filename, type)
  },
  checkRechRecord: (params, filename, type) => {
    return request.postJson(api.checkRechRecord, params, filename, type)
  },
  rechInfoImport: (params, filename, type) => {
    return request.postJson(api.rechInfoImport, params)
  },
  findSendMsgBalance: (params, filename, type) => {
    return request.postJson(api.findSendMsgBalance, params)
  },
  addSendMsgBalance: (params, filename, type) => {
    return request.postJson(api.addSendMsgBalance, params)
  },
  findMerchantMsgById: (params, filename, type) => {
    return request.postJson(api.findMerchantMsgById, params)
  },

  findSendMsgChannel: (params, filename, type) => {
    return request.postJson(api.findSendMsgChannel, params)
  },
  addSendMsgChannel: (params, filename, type) => {
    return request.postJson(api.addSendMsgChannel, params)
  },
  deleteSendMsgChannel: (params, filename, type) => {
    return request.postJson(api.deleteSendMsgChannel, params)
  },
  sendMsgBalanceManual: (params, filename, type) => {
    return request.postJson(api.sendMsgBalanceManual, params)
  }
}

export default rechInfo

