<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="商户编号" class="form-items">
                    <el-input v-model="formData.merchantNo" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="姓名" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="职位" class="form-items">
                    <el-input v-model="formData.position" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="16" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantName"
            fixed
            min-width="220"
            label="商户名称"
          />
          <el-table-column
            prop="merchantNo"
            min-width="180"
            label="商户编号"
          />
          <el-table-column
            prop="name"
            min-width="120"
            label="姓名"
          />
          <el-table-column
            prop="idCardNo"
            min-width="200"
            label="身份证号"
          />
          <el-table-column
            prop="position"
            min-width="100"
            label="职位"
          />
          <el-table-column
            prop="remake"
            min-width="220"
            label="备注"
          />
          <el-table-column
            fixed="right"
            prop="createTime"
            min-width="140"
            label="创建时间"
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import risk from '@/axios/default/risk'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'
export default {
  name: 'ManagerList',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 10,
      formData: {
        merchantNo: '',
        merchantName: '',
        name: '',
        position: '',
        idCardNo: ''
      },
      listData: [],
      loading: false,
      btnLoading: false
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    list() {
      risk.findFreelancerBlacklists({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.auto-width{
  width: 100%;
}

.dialog-form{
  display: flex;
  flex-flow: column nowrap;
}
.el-form-item__content{
  margin-left: 0;
}

.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
  /deep/ .warning-row {
    background: #fbc4c4 !important;
  }
  /deep/ .warning-oldlace {
    background: oldlace !important;
  }

}
</style>
