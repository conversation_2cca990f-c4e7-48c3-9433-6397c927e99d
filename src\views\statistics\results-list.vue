<template>
  <div class="content-main">
    <!-- 数据概览卡片 -->
    <div
      v-hasNoPermission="['index:noShow-statistical']"
      class="overview-cards"
    >
      <el-card class="overview-card" :class="['bg-gradient-1']">
        <div class="card-header">
          <span class="card-title">上月结算</span>
        </div>
        <div class="card-content">
          <div class="amount">
            <countTo
              class="count-to"
              :decimals="2"
              :start-val="0"
              :end-val="merTradeTotal.lastMonthAmount"
              :duration="1500"
            />
            <span class="unit">元</span>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card" :class="['bg-gradient-2']">
        <div class="card-header">
          <span class="card-title">本月结算</span>
        </div>
        <div class="card-content">
          <div class="amount">
            <countTo
              class="count-to"
              :decimals="decimals"
              :start-val="0"
              :end-val="merTradeTotal.monthAmount"
              :duration="1500"
            />
            <span class="unit">元</span>
          </div>
        </div>
      </el-card>

      <el-card class="overview-card" :class="['bg-gradient-3']">
        <div class="card-header">
          <span class="card-title">本年结算</span>
        </div>
        <div class="card-content">
          <div class="amount">
            <countTo
              class="count-to"
              :decimals="decimals"
              :start-val="0"
              :end-val="merTradeTotal.yearAmount"
              :duration="1500"
            />
            <span class="unit">元</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div ref="chartContainer">
      <div v-hasNoPermission="['index:noShow-echart']">
        <el-card>
          <div style="margin-bottom: 10px">
            <el-button
              size="mini"
              type="primary"
              :plain="activeName !== '结算分布'"
              @click="handleTabClick('结算分布')"
            >结算分布</el-button>
            <el-button
              size="mini"
              type="primary"
              :plain="activeName !== '结算趋势'"
              @click="handleTabClick('结算趋势')"
            >结算趋势</el-button>
          </div>

          <!-- 筛选表单 -->
          <el-form :disabled="chartLoading" size="small" :inline="true">
            <!-- 时间范围选择 -->
            <el-form-item v-if="activeName === '结算分布'" label="时间范围">
              <el-select
                v-model="time"
                placeholder="请选择"
                class="filter-select"
                @change="handleTimeChange"
              >
                <el-option label="请选择" value="" />
                <el-option label="本月" value="2" />
                <el-option label="上月" value="1" />
                <el-option label="本年" value="3" />
              </el-select>
            </el-form-item>

            <!-- 代征主体选择 -->
            <el-form-item label="代征主体">
              <TaxCollectionEntity
                v-model="type"
                clearable
                @change="handleQueryChange"
              />
            </el-form-item>

            <!-- 日期选择 -->
            <el-form-item v-if="activeName === '结算分布'" label="日期">
              <el-date-picker
                v-model="pickTime"
                type="daterange"
                class="filter-date-picker"
                clearable
                unlink-panels
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptionsMonth"
                @change="handleDateRangeChange"
              />
            </el-form-item>

            <!-- 月份选择 -->
            <el-form-item v-if="activeName === '结算趋势'" label="月份">
              <el-date-picker
                v-model="dateMonth"
                class="filter-date-picker"
                value-format="yyyy-MM"
                type="month"
                placeholder="选择日期"
                @change="handleQueryChange"
              />
            </el-form-item>
          </el-form>

          <!-- 总金额显示 -->
          <div v-if="activeName === '结算分布'" style="margin-bottom: 10px">
            结算总金额：<span style="font-weight: bold; font-size: 16px">{{
              totalAmount
            }}</span>元
          </div>

          <!-- 图表容器 -->
          <div v-loading="chartLoading">
            <div
              v-show="data6.length > 0"
              id="visit-count-chart"
              class="chart-container"
            />
            <div v-show="data6.length === 0" class="empty-state">
              <el-card class="box-card" shadow="never">
                <div class="empty-content">
                  <i class="el-icon-document" />
                  <div class="empty-text">暂无数据</div>
                </div>
              </el-card>
            </div>
          </div>
        </el-card>

        <!-- 数据表格 -->
        <el-card v-if="showDataTable" class="data-table-card">
          <div style="margin-bottom: 10px">
            <el-button
              size="mini"
              type="primary"
              :plain="tableType !== 1"
              @click="tableType = 1"
            >全局</el-button>
            <el-button
              size="mini"
              type="primary"
              :plain="tableType !== 2"
              @click="tableType = 2"
            >个人</el-button>
          </div>
          <el-table
            v-show="tableType === 1"
            v-loading="chartLoading"
            border
            :data="this.data6"
            size="mini"
            class="list-table"
          >
            <el-table-column
              prop="agentName"
              min-width="160"
              label="销售/团队名称"
              fixed="left"
              show-overflow-tooltip
            />
            <el-table-column
              prop="settAmount"
              min-width="140"
              label="结算金额(元)"
              align="left"
              show-overflow-tooltip
            />
            <el-table-column
              prop="tradeRate"
              min-width="140"
              label="占比"
              show-overflow-tooltip
            />
            <el-table-column
              prop="lastMonthMoney"
              min-width="140"
              label="上月结算金额(元)"
              align="left"
              show-overflow-tooltip
            />
            <el-table-column
              prop="rate"
              min-width="140"
              label="环比增长率(%)"
              show-overflow-tooltip
            />
            <el-table-column
              prop="rate1"
              min-width="140"
              label="同比增长率(%)"
              show-overflow-tooltip
            />
          </el-table>
          <el-table
            v-show="tableType === 2"
            v-loading="tableLoading"
            border
            :data="tableData"
            size="mini"
          >
            <el-table-column
              prop="mer_name"
              min-width="260"
              label="企业名称"
              fixed="left"
              show-overflow-tooltip
            />
            <el-table-column
              prop="saleName"
              min-width="160"
              label="销售名称"
              show-overflow-tooltip
            />
            <el-table-column
              prop="name"
              min-width="160"
              label="代理名称"
              show-overflow-tooltip
            />
            <el-table-column
              prop="money"
              min-width="140"
              label="结算金额(元)"
              align="right"
              show-overflow-tooltip
            />
            <el-table-column
              prop="lastMonthMoney"
              min-width="140"
              label="上月结算金额(元)"
              align="right"
              show-overflow-tooltip
            />
            <el-table-column
              prop="rate"
              min-width="140"
              label="环比增长率(%)"
              align="right"
              show-overflow-tooltip
            />
            <el-table-column
              prop="rate1"
              min-width="140"
              label="同比增长率(%)"
              align="right"
              show-overflow-tooltip
            />
            <el-table-column
              prop="counts"
              min-width="140"
              label="结算笔数"
              align="right"
              show-overflow-tooltip
            />
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="pageNum"
            :limit.sync="pageSize"
            @pagination="handlePaginationChange"
          />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { formatNumber, newAlert } from '@/utils'
import countTo from 'vue-count-to'
import resize from '@/components/Charts/mixins/resize'
import index from '@/axios/default/statistics/index'
import merList from '@/axios/default/financial/index'
import Pagination from '@/components/Pagination'
import TaxCollectionEntity from '@/components/TaxCollectionEntity'
import moment from 'moment'

export default {
  name: 'StatisticsResults',
  components: {
    countTo,
    Pagination,
    TaxCollectionEntity
  },
  mixins: [resize],
  data() {
    return {
      // 基础数据
      agentId: '',
      type: '',
      time: '2',
      activeName: '结算分布',
      switchType: 1,

      // 分页数据
      pageSize: this.$route.query.pageSize
        ? Number(this.$route.query.pageSize)
        : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,

      // 图表数据
      chartLoading: false,
      chart: null,
      merTradeTotal: {},
      totalAmount: 0,

      // 表格数据
      listData: [],
      data6: [],
      tableType: 1,
      tableLoading: false,

      // 日期相关
      dateMonth: moment().subtract(1, 'months').format('YYYY-MM'),
      pickTime: [],
      createTimeFrom: '',
      createTimeTo: '',

      // 图表配置
      decimals: 2,

      // 日期选择器配置
      pickerOptionsMonth: {
        shortcuts: [
          {
            text: '当月至今',
            onClick(picker) {
              const end = new Date(
                new Date().getFullYear(),
                new Date().getMonth(),
                new Date().getDate()
              )
              const start = new Date(
                new Date().getFullYear(),
                new Date().getMonth(),
                1
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近六个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 6)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {
    showDataTable() {
      return this.activeName === '结算分布'
    },
    tableData() {
      return this.listData.map((item) => ({
        ...item,
        money: formatNumber(item.money)
      }))
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      this.queryMerTradePieChart()
      this.queryMerTradeTotal()
    },
    // 事件处理方法
    handleTabClick(tab) {
      if (tab.name === '结算分布') {
        this.$router.go(0)
        this.switchType = 2
        this.queryMerTradePieChart()
      } else {
        this.queryMerTradePieChart2()
      }
      this.activeName = tab.name
    },
    handleTimeChange() {
      if (this.time !== '') {
        this.createTimeFrom = ''
        this.createTimeTo = ''
        this.pickTime = []
      }
      this.handleQueryChange()
    },
    handleDateRangeChange() {
      this.time = ''
      if (this.pickTime && this.pickTime.length === 2) {
        this.createTimeFrom = moment(this.pickTime[0]).format('YYYY-MM-DD')
        this.createTimeTo = moment(this.pickTime[1]).format('YYYY-MM-DD')
      } else {
        this.createTimeFrom = ''
        this.createTimeTo = ''
      }
      this.handleQueryChange()
    },
    handleQueryChange() {
      if (this.activeName === '结算分布') {
        if (this.switchType === 1) {
          this.queryMerTradePieChart()
        } else {
          this.queryMerTradePieChart()
          this.queryMerList()
        }
      } else {
        this.queryMerTradePieChart2()
      }
    },
    handlePaginationChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryMerList()
    },
    // API 调用方法
    async queryMerTradePieChart() {
      this.chartLoading = true
      try {
        const res = await index.queryMerTradePieChart({
          agentFlag: '0',
          agentId: this.agentId,
          dataType: this.time,
          levyId: this.type === '' ? null : this.type,
          switchType: this.switchType,
          createTimeFrom: this.createTimeFrom,
          createTimeTo: this.createTimeTo
        })

        if (res.data.code === '0000') {
          this.data6 = res.data.data.listData
          this.totalAmount = res.data.data.totalAmount
          this.updatePieChart(res.data.data.listData)
        } else {
          // newAlert(this.$tips, res.data.message)
        }
      } finally {
        this.chartLoading = false
      }
    },

    async queryMerTradePieChart2() {
      try {
        const res = await index.queryMerTradePieChart({
          dateMonth: this.dateMonth,
          levyId: this.type,
          smallRateData: '',
          axisType: '1'
        })

        if (res.data.code === '0000') {
          this.updateLineChart(res.data.data)
        } else {
          newAlert(this.$tips, res.data.message)
        }
      } catch (error) {
        console.error('获取折线图数据失败:', error)
        newAlert(this.$tips, '获取数据失败')
      }
    },

    async queryMerTradePieChart3() {
      try {
        const res = await index.queryMerTradePieChart({
          agentId: this.agentId,
          dataType: this.time,
          levyId: this.type,
          switchType: this.switchType,
          createTimeFrom: this.createTimeFrom,
          createTimeTo: this.createTimeTo
        })

        if (res.data.code === '0000') {
          this.data6 = res.data.data.listData
          this.totalAmount = res.data.data.totalAmount
        } else {
          newAlert(this.$tips, res.data.message)
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        newAlert(this.$tips, '获取数据失败')
      }
    },

    async queryMerList() {
      this.tableLoading = true
      try {
        const res = await merList.queryMerList({
          agentId: this.agentId,
          dataType: this.time,
          levyId: this.type,
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          createTimeFrom: this.createTimeFrom,
          createTimeTo: this.createTimeTo
        })

        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      } finally {
        this.tableLoading = false
      }
    },

    async queryMerTradeTotal() {
      try {
        const res = await index.queryMerTradeTotal({})
        if (res.data.code === '0000') {
          this.merTradeTotal = res.data.data
        }
      } catch (error) {
        console.error('获取总数据失败:', error)
        newAlert(this.$tips, '获取数据失败')
      }
    },

    // 图表更新方法
    updatePieChart(data) {
      const chartData = data.map((item) => ({
        name: `${item.agentName}-${item.tradeRate}`,
        value: item.settAmount,
        agentId: item.agentId
      }))

      const option = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        // legend: {
        //   orient: 'vertical',
        //   left: 'left'
        // },
        legend: {
          top: 'top', // 放置在顶部
          left: 'center', // 水平居中
          orient: 'horizontal', // 横向排列
          itemWidth: 14,
          itemHeight: 14,
          itemGap: 8,
          padding: [10, 10, 10, 10],
          // 当图例过多时使用 type: 'scroll'，否则可以省略
          type: 'scroll'
        },
        color: [
          '#fc8251',
          '#5470c6',
          '#9A60B4',
          '#ef6567',
          '#f9c956',
          '#3BA272'
        ],
        series: [
          {
            name: '下发金额',
            type: 'pie',
            radius: '50%',
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      this.updateChart(option)
    },

    updateLineChart(data) {
      const { resTime, resList } = data
      const series = resList.map((item) => ({
        name: item.agentName,
        type: 'line',
        stack: 'Total',
        data: Object.values(item.resList).map((a) => a.money)
      }))

      const option = {
        title: {
          text: '销售结算数据线形图'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: resList.map((a) => a.agentName)
        },
        grid: {
          left: '8%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: resTime
        },
        yAxis: {
          type: 'value'
        },
        series
      }

      this.updateChart(option)
    },

    updateChart(option) {
      this.$nextTick(() => {
        const dom = document.getElementById('visit-count-chart')

        if (!dom) {
          return
        }

        if (!this.chart) {
          this.chart = echarts.init(dom)
          // 只在初始化时绑定一次事件
          if (this.activeName === '结算分布') {
            this.chart.on('click', this.handleChartClick)
          }
        }

        this.chart.setOption(option)
      })
    },

    handleChartClick(params) {
      if (params.data && params.data.agentId) {
        this.agentId = params.data.agentId
        this.tableType = 2
        if (this.switchType === 1) {
          this.switchType = 2
        }
        this.queryMerList()
      } else {
        console.error('无法获取 agentId，点击的数据:', params.data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content-main {
  padding-bottom: calc(150px + env(safe-area-inset-bottom));
}
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 0;
}
.empty-content {
  text-align: center;
}
.empty-content i {
  font-size: 48px;
  color: #909399;
}
.empty-text {
  margin-top: 16px;
  font-size: 14px;
  color: #909399;
}

.chart-container {
  height: 350px;
  width: 100%;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;

  .overview-card {
    height: 125px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      margin-bottom: 15px;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #ffffff;
      }
    }

    .card-content {
      .amount {
        font-size: 24px;
        font-weight: 700;
        color: #ffffff;

        .count-to {
          font-size: 24px;
          font-weight: bold;
        }

        .unit {
          margin-left: 4px;
          font-size: 16px;
        }
      }
    }
  }
}

// 渐变背景
.bg-gradient-1 {
  background: linear-gradient(135deg, #0fb97b 0%, #099cf1 100%);
}

.bg-gradient-2 {
  background: linear-gradient(135deg, #25b1d8 0%, #6d77ec 100%);
}

.bg-gradient-3 {
  background: linear-gradient(135deg, #d15e5e85 0%, #7e7e72a1 100%);
}
</style>
