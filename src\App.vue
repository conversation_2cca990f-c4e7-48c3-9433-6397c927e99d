<template>
  <!--  <div id="app" @mousemove="moveEvent" @click="moveEvent">-->
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import socket from '@/utils/socket'
import db from '@/utils/localstorage'
export default {
  name: 'App',
  data() {
    return {
      // 用户超时定时器
      timmer: null
    }
  },
  methods: {
    moveEvent() {
      const path = ['/login']
      if (!path.includes(this.$route.path)) {
        clearTimeout(this.timmer)
        this.init()
      }
    },
    init() {
      this.timmer = setTimeout(() => {
        socket.close()
        // this.$router.push({
        //   path: "/login",
        // })
        db.clear()
        location.reload()
      }, 1200000)
    }
  }
}
</script>
<style lang="scss">
  html,body{
    overflow: hidden;
  }
  body .el-table th.gutter {
    display: table-cell !important;
  }

  body .el-table colgroup.gutter {
    display: table-cell !important;
  }

  .el-message-box__status.el-icon-my-alert{
    color: #f54343;
    top: 0;
    transform: translateY(0);
    font-size: 28px!important;
  }
  .el-icon-my-alert::before{
    content: "\e7a3";
  }
  .my-messaagebox{
    width: 300px;
    padding-bottom: 0;
    text-align: center;
    .alert-name{
      font-size: 14px;
      font-weight: bold;
    }
    .alert-content{
      padding-top: 5px;
    }
    .el-message-box__message{
      background: url("./assets/danger.png") no-repeat center 10px;
      background-size: 50%;
      padding-top: 105px;
      padding-left: 0;
      padding-right: 0;
    }
    .el-message-box__message p{
      line-height: 20px;
    }
    .el-message-box__header{
      padding: 0;
    }
    .el-message-box__headerbtn{
      z-index: 99;
      top: 10px;
    }
    .el-message-box__headerbtn:focus .el-message-box__close, .el-message-box__headerbtn:hover .el-message-box__close {
      color: #f54343;
    }
  }
</style>
