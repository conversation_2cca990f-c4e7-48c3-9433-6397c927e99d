// 销售
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  addSaleInfo: `${API_Header}/saleInfo/addSaleInfo`, // 新增销售
  list: `${API_Header}/saleInfo/list`, // 销售列表
  updateSaleInfo: `${API_Header}/saleInfo/updateSaleInfo`, // 修改销售
  getSaleInfo: `${API_Header}/saleInfo/getSaleInfo`, // 根据id查询销售
  updateStatus: `${API_Header}/saleInfo/updateStatus`, // 开通禁用销售
  allList: `${API_Header}/saleInfo/allList`, // 销售列表（全部）
  saleDownLoadData: `${API_Header}/saleInfo/saleDownLoadData`, // 销售列表（全部）
  updateSalePsd: `${API_Header}/saleInfo/updateSalePsd` // 重置密码

}

const saleInfo = {
  addSaleInfo: params => {
    return request.postJson(api.addSaleInfo, params)
  },
  list: params => {
    return request.postJson(api.list, params)
  },
  updateSaleInfo: params => {
    return request.postJson(api.updateSaleInfo, params)
  },
  getSaleInfo: params => {
    return request.postJson(api.getSaleInfo, params)
  },
  updateStatus: params => {
    return request.postJson(api.updateStatus, params)
  },
  allList: params => {
    return request.postJson(api.allList, params)
  },
  saleDownLoadData: (params, filename, type) => {
    return request.downFiles(api.saleDownLoadData, params, filename, type)
  },

  updateSalePsd: params => {
    return request.postJson(api.updateSalePsd, params)
  }
}

export default saleInfo
