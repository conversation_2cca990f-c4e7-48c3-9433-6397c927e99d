<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="5" :xs="24">
                  <el-form-item label="姓名" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="5" :xs="24">
                  <el-form-item label="状态" class="form-items">
                    <el-select v-model="formData.status" placeholder="请选择状态" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option label="正常" value="1" />
                      <el-option label="停用" value="0" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="代理级别" class="form-items">
                    <el-select v-model="formData.agentGradeId" placeholder="请选择代理级别" class="auto-width" @change="getAgentGradeByParentId3">
                      <el-option label="请选择" value="" />
                      <el-option v-for="(item,index) in agentGradeArr" :key="item.id" :label="item.gradeName" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="上级代理／销售" class="form-items">
                    <!--                    <el-select v-model="formData.parentName" placeholder="请选择上级代理／销售" class="auto-width">-->
                    <!--                      <el-option v-for="(item,index) in parentArr" :key="item.id" :label="item.gradeName" :value="item.id" />-->
                    <!--                    </el-select>-->
                    <el-input v-model="formData.parentName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-add" type="primary" @click="agentDownLoadData">下载文件</el-button>
                    <el-button icon="el-icon-add" type="primary" @click="agentDownNewData">下载关系表</el-button>

                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增代理</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="loginName"
            label="登录账号"
          />
          <el-table-column
            prop="name"
            label="姓名"
          />
          <el-table-column
            prop="feeRate"
            label="费率"
          >
            <template slot-scope="scope">
              {{ scope.row.feeRate }}%
            </template>
          </el-table-column>
          <el-table-column
            prop="calMode"
            label="计算方式"
          >
            <template slot-scope="scope">
              {{ scope.row.calMode == '3' ? '外扣' :'内扣' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="balanceCycle"
            label="计算周期"
          >
            <template slot-scope="scope">
              {{ balanceCycleObj[scope.row.balanceCycle] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="balanceType"
            label="结算类型"
          >
            <template slot-scope="scope">
              {{ scope.row.balanceType == '1' ? '充值金额' :'结算金额' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="状态"
          >
            <template slot-scope="scope">
              {{ scope.row.status == '1' ? '正常' :'停用' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button v-if="scope.row.status == '1'" type="text" @click="edit(scope.row)">修改</el-button>
              <el-button v-if="scope.row.status == '0'" type="text" @click="updateStatus(scope.row.id,'1')">开通</el-button>
              <el-button v-else type="text" @click="updateStatus(scope.row.id,'0')">停用</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>
    </div>

    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" :label-width="formLabelWidth" size="mini" class="form-style" :rules="rules">
          <el-form-item label="登录账号" prop="loginName">
            <el-input v-model="dialogData.loginName" autocomplete="off" placeholder="请填写登录账号" />
          </el-form-item>
          <el-form-item v-if="dialogType == 'add'" label="登录密码" prop="passWord">
            <el-input v-model="dialogData.passWord" autocomplete="off" show-password placeholder="请填写密码" />
          </el-form-item>
          <el-form-item label="姓      名" prop="name">
            <el-input v-model="dialogData.name" autocomplete="off" placeholder="请填写姓名" />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="dialogData.mobile" autocomplete="off" placeholder="请填写手机号" />
          </el-form-item>
          <el-form-item label="邮     箱" prop="email">
            <el-input v-model="dialogData.email" autocomplete="off" placeholder="请填写邮箱" />
          </el-form-item>
          <el-form-item label="代理级别" prop="agentGradeId">
            <el-select v-model="dialogData.agentGradeId" placeholder="请选择代理级别" class="auto-width" @change="getAgentGradeByParentId">
              <el-option v-for="(item,index) in agentGradeArr" :key="item.id" :label="item.gradeName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="上级代理／销售" prop="parentId">
            <el-select v-model="dialogData.parentId" placeholder="请选择上级代理／销售" class="auto-width">
              <el-option v-for="(item,index) in parentArr" :key="item.id" :label="item.gradeName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-row :gutter="0">
            <el-col :span="12">
              <el-form-item label="计算方式" prop="calMode">
                <el-select v-model="dialogData.calMode" placeholder="请选择计算方式" class="auto-width">
                  <el-option label="内扣" value="4" />
                  <el-option label="外扣" value="3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="费率" prop="feeRate">
                <el-input v-model="dialogData.feeRate" class="auto-width" placeholder="请填写费率">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :span="12">
              <el-form-item label="结算周期" prop="balanceCycle">
                <el-select v-model="dialogData.balanceCycle" placeholder="请选择结算周期" class="auto-width">
                  <el-option label="周结" value="1" />
                  <el-option label="月结" value="2" />
                  <el-option label="季结" value="3" />
                  <el-option label="年结" value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结算类型" prop="balanceType">
                <el-select v-model="dialogData.balanceType" placeholder="请选择结算类型" class="auto-width">
                  <el-option label="充值金额结算" value="1" />
                  <el-option label="结算金额结算" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!--          <el-form-item label="备注">-->
          <!--            <el-input v-model="dialogData.note" type="textarea" :rows="2" placeholder="请输入内容" />-->
          <!--          </el-form-item>-->
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" @click="submit('dialogData')">保存</el-button>
              <el-button @click="dialogFormVisible = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import agentGrade from '@/axios/default/agentGrade'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
var checkPhone = (rule, value, callback) => {
  const reg = /^1[0-9]\d{9}$/
  if (reg.test(value)) {
    callback()
  } else {
    return callback(new Error('请输入正确的手机号'))
  }
}
export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        name: '',
        status: '',
        agentGradeId: '',
        parentName: ''
      },
      dialogData: this.initDialogData(),
      dialogFormVisible: false,
      formLabelWidth: '130px',
      listData: [],
      balanceCycleObj: { 1: '周', 2: '月', 3: '季', 4: '年' },
      // 弹框部分
      title: '',
      dialogType: '',
      agentGradeArr: [],
      parentArr: [],
      rules: {
        loginName: [{ required: true, message: '请填写登陆账号', trigger: 'blur' }],
        passWord: [{ required: true, message: '请填写登陆密码', trigger: 'blur' }],
        name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
        mobile: [{ required: true, message: '请填写手机号', trigger: 'blur' },
          { validator: checkPhone, trigger: ['blur', 'change'] }
        ],
        email: [{ required: true, message: '请填写邮箱', trigger: 'blur' }],
        calMode: [{ required: true, message: '请填写计算方式', trigger: 'change' }],
        feeRate: [{ required: true, message: '请填写费率', trigger: 'blur' }, { validator: (rule, value, callback) => {
          const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
          if (result) {
            callback()
          } else {
            callback('请输入正确的数字')
          }
        }, trigger: ['blur', 'change'] }],
        balanceCycle: [{ required: true, message: '请填写结算周期', trigger: 'change' }],
        balanceType: [{ required: true, message: '请填写结算类型', trigger: 'change' }],
        agentGradeId: [{ required: true, message: '请选择代理级别', trigger: 'change' }],
        parentId: [{ required: true, message: '请选择上级销售', trigger: 'change' }]
      }
    }
  },
  mounted() {
    this.list()
    this.getAllAgentGrades()
  },
  methods: {
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogType == 'add') {
            this.addSaleInfo()
          } else {
            this.updateSaleInfo()
          }
        } else {
          console.log('error')
          return false
        }
      })
    },
    // 添加销售
    add() {
      this.title = '新增代理信息'
      this.dialogFormVisible = true
      this.dialogType = 'add'
      this.dialogData = this.initDialogData()
      this.$nextTick(function() {
        this.resetForm('dialogData')
      })
    },
    addSaleInfo() {
      agentGrade.addAgentInfo({
        ...this.dialogData
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 修改销售
    edit(data) {
      this.title = '修改代理信息'
      this.dialogType = 'edit'
      this.dialogFormVisible = true
      this.dialogData = this.initDialogData()
      agentGrade.getAgentInfo({
        id: data.id
      }).then(res => {
        if (res.data.code == '0000') {
          this.resetForm('dialogData')
          this.dialogData = res.data.data
          this.getAgentGradeByParentId2(this.dialogData.agentGradeId)
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    updateSaleInfo() {
      agentGrade.updateAgentInfo({
        ...this.dialogData
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.resetForm('dialogData')
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 开通停用
    updateStatus(id, status) {
      agentGrade.updateStatus({
        id: id,
        status
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initDialogData() {
      return {
        loginName: '',
        passWord: '',
        name: '',
        mobile: '',
        email: '',
        agentGradeId: '',
        parentId: '',
        calMode: '',
        feeRate: '',
        balanceCycle: '',
        balanceType: ''
      }
    },
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      // let gradeName
      // if (this.formData.parentName) {
      //   gradeName = this.parentArr.find(res => res.id === this.formData.parentName).gradeName
      // }
      agentGrade.agentInfoList({
        name: this.formData.name,
        status: this.formData.status,
        agentGradeId: this.formData.agentGradeId,
        parentName: this.formData.parentName,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    agentDownLoadData() {
      agentGrade.agentDownLoadData({
        ...this.formData
      }, `代理列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, 'xls').then(res => {
        this.$message.success('操作成功')
      })
    },
    agentDownNewData() {
      agentGrade.agentDownNewData({
      }, `代理关系表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, 'xls').then(res => {
        this.$message.success('操作成功')
      })
    },
    // 上级
    getAllAgentGrades() {
      agentGrade.getAllAgentGrades().then(res => {
        this.agentGradeArr = res.data.data
      })
    },
    getAgentGradeByParentId(id) {
      this.dialogData.parentId = ''
      const parentId = this.agentGradeArr.find(res => res.id == id).parentId
      agentGrade.getAgentGradeByParentId({
        parentId
      }).then(res => {
        this.parentArr = res.data.data
      })
    },
    getAgentGradeByParentId2(id) {
      const parentId = this.agentGradeArr.find(res => res.id == id).parentId
      agentGrade.getAgentGradeByParentId({
        parentId
      }).then(res => {
        this.parentArr = res.data.data
      })
    },
    getAgentGradeByParentId3(id) {
      this.formData.parentName = ''
      const parentId = this.agentGradeArr.find(res => res.id == id).parentId
      agentGrade.getAgentGradeByParentId({
        parentId
      }).then(res => {
        this.parentArr = res.data.data
      })
    },

    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .form-row{
    display: flex;
  }
  .form-row-style{
    margin: 0 20px;
    .title{
      width: 80px;
      text-align: right;
    }
    .spacing{
      margin: 0 5px;
      width: 75px;
    }
  }
  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
</style>
