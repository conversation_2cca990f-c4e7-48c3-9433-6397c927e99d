<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="开票申请时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="是否预开" class="form-items">
                    <el-select v-model="formData.invPreopen" placeholder="请选择是否预开" class="auto-width">
                      <el-option label="请选择" value="" />
                      <el-option label="是" value="1" />
                      <el-option label="否" value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="项目负责人" class="form-items">
                    <el-input v-model="formData.projectName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          stripe
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">

              <el-popover
                placement="top"
                width="200"
                trigger="hover">
                <p>可开发票金额：{{scope.row.canInvoiceBalance}}</p>
                <p>已开发票金额：{{scope.row.invoiceBalance}}</p>
                <p>已下发金额：{{scope.row.tradeMoney}}</p>
                <p>已充值金额：{{scope.row.rechMoney}}</p>
                <p slot="reference">{{scope.row.merName}}</p>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="invoiceTypeName"
            label="发票类目"
            width="300"
            show-overflow-tooltip
          />
          <el-table-column
            prop="invAmount"
            label="开票金额"
          />
          <el-table-column
            prop="invQuantity"
            label="开票数量（张）"
            width="110"
          />
          <el-table-column
            prop="createTime"
            label="发票申请时间"
            width="150"
          />
          <el-table-column
            prop="levyName"
            label="开票方"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="auditRemark"
            width="200"
            label="审核情况"
          />

          <el-table-column
            prop="invCategory"
            label="开票类型"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invCategory == '1'" class="invoice-ordinary">普票</span>
              <span v-if="scope.row.invCategory == '2'" class="invoice-specially">专票</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="projectName"
            label="项目负责人"
            width="150"
          />
          <el-table-column
            prop="invPreopen"
            label="是否预开"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invPreopen == '1'" style="color: #f54343">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="操作"
            width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="jumpDetail(scope.row.id)">查看详情</el-button>
              <el-button type="text" @click="trialAudit(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    开票复审-->
    <el-dialog title="开票复审" :visible.sync="dialogA" width="40%" class="reset-dialog reset-dialog-small ">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="auditForm" :model="auditForm" label-width="100px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="商户名称" class="form-items">
            <el-input v-model="dialogAData.merName" disabled />
          </el-form-item>
          <el-form-item label="开票金额" class="form-items">
            <el-input v-model="dialogAData.invAmount" disabled />
          </el-form-item>
          <el-form-item label="开票类目" class="form-items">
            <el-input v-model="dialogAData.invoiceTypeName" type="textarea" disabled />
          </el-form-item>
          <el-form-item label="是否预开" class="form-items">
            {{ dialogAData.invPreopen == '1' ? '是' : '否' }}
          </el-form-item>
          <el-form-item label="初审备注" class="form-items">
            <el-input v-model="trialauditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="备注" class="form-items" prop="reviewauditRemark">
            <el-input v-model="auditForm.reviewauditRemark" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('auditForm','1')">通过</el-button>
              <el-button :loading="btnLoading" @click="submit('auditForm','2')">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import invoiceType from '@/axios/default/invoiceType'
import publicApi from '@/axios/default/public'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        'createTimeFrom': '',
        'createTimeTo': '',
        'auditState': '3',
        'merName': '',
        'invPreopen': '',
        levyId: ''
      },
      levyBodyIdArr: [],
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogA: false,
      dialogAData: {

      },
      trialauditRemark: '',
      auditForm: {
        reviewauditRemark: ''
      },
      rules: {
        reviewauditRemark: [{ required: true, message: '请填写复审备注', trigger: 'blur' }]
      },
      btnLoading: false
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
    this.queryInvoiceInfo()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryInvoiceInfo()
    },
    //   列表
    queryInvoiceInfo() {
      invoiceType.queryInvoiceInfo({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'InvoiceInfo': {
          'createTimeFrom': this.pickTime[0],
          'createTimeTo': this.pickTime[1],
          'auditState': this.formData.auditState,
          'merName': this.formData.merName,
          'invPreopen': this.formData.invPreopen,
          'levyId': this.formData.levyId
        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
      })
    },
    // 打开复审框
    trialAudit(data) {
      this.dialogA = true
      this.dialogAData = data
      this.auditForm.reviewauditRemark = ''
      this.queryInvoiceAuditRecord(data.id)
    },
    // 提交审核
    submit(formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          invoiceType.auditInvoiceInfo({
            id: this.dialogAData.id,
            btnType: type,
            auditStage: 2,
            auditRemark: this.auditForm.reviewauditRemark
          }).then(res => {
            this.btnLoading = false
            this.$refs[formName].clearValidate()
            this.$refs[formName].resetFields()

            if (res.data.code == '0000') {
              this.dialogA = false
              this.$message({ type: 'success', message: '操作成功' })
              this.queryInvoiceInfo()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 分页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryInvoiceInfo()
    },
    queryInvoiceAuditRecord(id) {
      invoiceType.queryInvoiceAuditRecord({
        invId: id
      }).then(res => {
        this.trialauditRemark = res.data.data.listRec[0].auditReason
      })
    },
    // 查看详情
    jumpDetail(id) {
      this.$router.push({
        path: '/invoice/detail',
        query: {
          origin: 'review',
          type: 'view',
          id
        }
      })
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  /deep/ .el-form-item__content{
    margin-left: 0!important;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .invoice-ordinary{
    color: #f29c39;
  }
  .invoice-specially{
    color: #5791fd;
  }
</style>
