// 结算管理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  whiteList: `${API_Header}/whiteList/list`, // 白名单列表
  whiteListAdd: `${API_Header}/whiteList/add`, // 白名单列表
  whiteListDel: `${API_Header}/whiteList/delete`, // 白名单列表
  whiteListUpdate: `${API_Header}/whiteList/update`, // 白名单列表
  getOneWhiteList: `${API_Header}/whiteList/getOneWhiteList` // 白名单列表

}

const whiteList = {
  whiteList: params => {
    return request.postJson(api.whiteList, params)
  },
  whiteListAdd: params => {
    return request.postJson(api.whiteListAdd, params)
  },
  whiteListDel: params => {
    return request.postJson(api.whiteListDel, params)
  },
  whiteListUpdate: params => {
    return request.postJson(api.whiteListUpdate, params)
  },
  getOneWhiteList: params => {
    return request.postJson(api.getOneWhiteList, params)
  }
}

export default whiteList

