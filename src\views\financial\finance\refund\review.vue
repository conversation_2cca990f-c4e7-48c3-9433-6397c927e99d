<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="退款时间" class="form-items"> <el-date-picker
                    v-model="pickTime"
                    class="auto-width"
                    type="daterange"
                    align="right"
                    unlink-panels
                    value-format="yyyy-MM-dd"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :picker-options="pickerOptions"
                    @change="resetTime"
                  />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="退款时间"
            width="150"
          />
          <el-table-column
            prop="merchantName"
            width="200"
            label="企业名称"
          />
          <el-table-column
            prop="rechargeAccountName"
            label="打款名称"
            width="200"
          />
          <el-table-column
            prop="tradeOrderNumber"
            label="商户订单号"
            width="200"
          />
          <el-table-column
            prop="refundAmount"
            label="退款金额"
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            width="200"
          />
          <el-table-column
            prop="refundType"
            label="退款类型"
          >
            <template slot-scope="scope">
              {{ refundType[scope.row.refundType] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="refundStatus"
            label="审核状态"
          >
            <template slot-scope="scope">
              {{ refundStatus[scope.row.refundStatus] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="userName"
            label="申请人"
          />
          <el-table-column
            prop="remark"
            label="备注"
            show-overflow-tooltip
          />
          <el-table-column
            prop="remark"
            label="操作"
            show-overflow-tooltip
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="openbox(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    退款复审-->
    <el-dialog title="退款复审" :visible.sync="dialogMain" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogMainData" :model="dialogMainData" size="mini" class="form-style" :rules="rules" label-width="90px">

          <el-form-item label="企业名称">
            <el-input v-model="dialogMainData.merchantName" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item v-if="dialogMainData.refundType == '2'" label="打款名称">
            <el-input v-model="dialogMainData.rechargeAccountName" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item v-if="dialogMainData.refundType == '2'" label="充值通道">
            <el-input v-model="dialogMainData.channelName" type="text" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="商户订单号">
            <el-input v-model="dialogMainData.tradeOrderNumber" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="退款金额">
            <el-input v-model="dialogMainData.refundAmount" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="退款类型">
            <el-input v-model="refundType[dialogMainData.refundType]" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="申请备注">
            <el-input v-model="dialogMainData.remark" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="初审备注">
            <el-input v-model="dialogMainData.firistAuditRemark" type="text" placeholder="请输入内容" disabled />
          </el-form-item>
          <el-form-item label="备注" prop="reAuditRemark">
            <el-input v-model="dialogMainData.reAuditRemark" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>

          <el-form-item label="" class="form-items">
            <el-button type="primary" @click="reAuditRefundOrderInfo('dialogMainData','5')">通过</el-button>
            <el-button @click="reAuditRefundOrderInfo('dialogMainData','4')">不通过</el-button>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import finance from '@/axios/default/finance'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Review',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        accountName: ''
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogMain: false,
      dialogMainData: this.initDialog(),
      refundType: { 1: '充值退款', 2: '结算退款' },
      refundStatus: { 1: '待审核', 2: '初审通过', 3: '初审拒绝', 4: '复审拒绝', 5: '完成' },
      rules: {
        reAuditRemark: [{ required: true, message: '请填写备注', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      finance.refundOrderInfoList({
        ...this.formData,
        refundStatus: '2',
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    initDialog() {
      return {
        reAuditRemark: ''
      }
    },
    openbox(data) {
      this.dialogMain = true
      this.$nextTick(function() {
        this.$refs['dialogMainData'].clearValidate()
        this.$refs['dialogMainData'].resetFields()
      })
      this.dialogMainData = data
    },
    reAuditRefundOrderInfo(formName, refundStatus) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          finance.reAuditRefundOrderInfo({
            refundStatus: refundStatus,
            id: this.dialogMainData.id,
            reAuditRemark: this.dialogMainData.reAuditRemark
          }).then(res => {
            if (res.data.code == '0000') {
              this.dialogMain = false
              this.$message({ type: 'success', message: '操作成功' })
              this.list()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
