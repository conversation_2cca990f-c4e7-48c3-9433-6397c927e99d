<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间起" class="form-items" prop="createTimeFrom">
                <el-date-picker
                  v-model="formData.createTimeFrom"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间止" class="form-items" prop="createTimeTo">
                <el-date-picker
                  v-model="formData.createTimeTo"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="身份证号" class="form-items">
                <el-input v-model="formData.payeeIdCard" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="订单完成时间起" class="form-items" prop="completeTimeStart">
                <el-date-picker
                  v-model="formData.completeTimeStart"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="订单完成时间止" class="form-items" prop="completeTimeEnd">
                <el-date-picker
                  v-model="formData.completeTimeEnd"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="企业名称" class="form-items">
                <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="5" :xs="24">
              <el-form-item label="姓名" class="form-items">
                <el-input v-model="formData.payeeName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="5" :xs="24">
              <el-form-item label="交易状态" class="form-items" prop="tradeState">
                <el-select v-model="formData.tradeState" placeholder="请选择交易状态" class="auto-width">
                  <el-option label="全部" value="" />
                  <el-option label="未代付" value="1" />
                  <el-option label="结算成功" value="2" />
                  <el-option label="失败" value="3" />
                  <el-option label="处理中" value="4" />
                  <el-option label="已退款" value="5" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :lg="7" :xs="24">
              <el-form-item label="商户批次号" class="form-items">
                <el-input v-model="formData.batchNum" placeholder="请输入内容" clearable @input="batchNumInput" />
              </el-form-item>
            </el-col>

            <el-col :lg="7" :xs="24">
              <el-form-item label="商户订单号" class="form-items">
                <el-input v-model="formData.merOrderNum" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="6" :xs="24">
              <el-form-item label="结算方式" class="form-items">
                <el-select v-model="formData.orderSource" placeholder="请选择结算方式" class="auto-width">
                  <el-option label="全部" value="" />
                  <el-option label="商户提交" value="1" />
                  <el-option label="接口提交" value="2" />
                  <el-option label="线下导入" value="3" />
                  <el-option label="代理提现" value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="6" :xs="24">
              <el-form-item v-if="this.flag ==true" label="销售名称" class="form-items">
                <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="6" :xs="24">
              <el-form-item v-if="this.flag ==true" label="代理名称" class="form-items">
                <el-input v-model="formData.agentName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :lg="6" :xs="24">
              <el-form-item label="项目负责人" class="form-items">
                <el-input v-model="formData.projectName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="结算金额起" class="form-items" prop="startSettMoney">
                <el-input v-model.number="formData.startSettMoney" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="结算金额止" class="form-items" prop="endSettMoney">
                <el-input v-model.number="formData.endSettMoney" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="商户编号" class="form-items">
                <el-input v-model="formData.merNo" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="8" :xs="24">
              <el-form-item label="代征主体" class="form-items">
                <el-select
                  v-model="formData.levyId"
                  placeholder="请选择"
                  class="auto-width"
                  @change="queryChannelInfoByLevyId"
                >
                  <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                  <el-option v-for="item in levyBodyIdArr" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :xs="24">
              <el-form-item label="通道名称" class="form-items">
                <!--                    <el-input v-model="formData.channelName" placeholder="请输入内容" clearable />-->
                <el-select v-model="formData.channelId" placeholder="请选择" class="auto-width">
                  <el-option v-if="channelArr.length>1" label="请选择" value="" />
                  <el-option v-for="item in channelArr" :key="item.id" :label="item.channelName" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="24" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                <el-button icon="el-icon-search" type="primary" @click="downTradeOrderDetails">下载文件</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card class="list-card">
        <el-row class="table-head-money">
          <el-col :span="6">
            <div class="table-headers">
              <span class="table-header-num">{{ payData.payMoney || 0 }}</span>
              <span class="table-header-name">发起付款金额(元)</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="table-headers">
              <span class="table-header-num">{{ payData.payMoneyPro || 0 }}</span>
              <span class="table-header-name">付款中(元)</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="table-headers">
              <span class="table-header-num">{{ payData.payMoneySuc || 0 }}</span>
              <span class="table-header-name">付款成功(元)</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="table-headers">
              <span class="table-header-num">{{ payData.payMoneyFai || 0 }}</span>
              <span class="table-header-name">付款失败(元)</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-card>
        <div class="table-top">
          <el-button v-if="settNum" plain size="mini" @click="downSettReceipt">结算回单批量下载</el-button>
        </div>
        <el-table :data="listData" border size="mini" class="list-table">
          <el-table-column prop="merNo" label="商户编号" width="200" show-overflow-tooltip />
          <el-table-column prop="projectId" label="项目ID" width="80" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="goProject" @click="goProject(scope.row.projectId)">{{ scope.row.projectId }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="projectNames" label="项目名称" min-width="200" show-overflow-tooltip />

          <el-table-column prop="merName" width="200" label="企业名称" show-overflow-tooltip />

          <el-table-column prop="levyName" label="代征主体" width="200" show-overflow-tooltip />

          <el-table-column prop="channelName" label="通道名称" width="200" show-overflow-tooltip />

          <el-table-column prop="payeeName" label="姓名" show-overflow-tooltip />
          <el-table-column prop="payeeIdCard" label="身份证号" width="200" show-overflow-tooltip />

          <el-table-column prop="createTime" label="订单创建时间" width="200" show-overflow-tooltip />

          <el-table-column prop="orderCompleteTime" label="订单完成时间" width="200" show-overflow-tooltip />
          <el-table-column prop="operateState" label="审核状态" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ operateState[scope.row.operateState] }}
            </template>
          </el-table-column>

          <el-table-column prop="orderSource" label="结算方式" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ orderSource[scope.row.orderSource] }}
            </template>
          </el-table-column>

          <el-table-column prop="tradeState" label="交易状态" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ tradeState[scope.row.tradeState] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="taxMoney"
            label="应发金额"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="taxFee"
            label="个人所得税"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="settMoney"
            label="实发金额"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column prop="batchNum" label="商户批次号" width="200" show-overflow-tooltip />
          <el-table-column prop="merOrderNum" label="商户订单号" width="200" show-overflow-tooltip />
          <!--          <el-table-column-->
          <!--            prop="channelName"-->
          <!--            label="通道名称"-->
          <!--            width="200"-->
          <!--            show-overflow-tooltip-->
          <!--          />-->

          <el-table-column v-if="this.flag ==true" prop="saleName" label="销售名称" width="100" show-overflow-tooltip />
          <el-table-column v-if="this.flag ==true" prop="agentName" label="代理名称" width="100" show-overflow-tooltip />
          <el-table-column prop="projectName" label="项目负责人" width="100" show-overflow-tooltip />

          <el-table-column prop="transMsg" label="结算备注" width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.transMsg }}
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" width="200" show-overflow-tooltip />
          <el-table-column prop="finalAuditTime" label="审核通过时间" width="200" show-overflow-tooltip />
          <el-table-column prop="address" label="操作" fixed="right" width="250">
            <template slot-scope="scope">
              <a
                v-if="scope.row.eleReceipt"
                :href="scope.row.eleReceipt"
                target="_blank"
                class="tb-active-red mar-right"
              >查看电子回单</a>
              <a class="tb-active-red" @click="viewInfo(scope.row)">查看订单详情</a>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="handleSizeChange"
        />
      </el-card>
    </div>

    <el-dialog title="查看订单详情" :visible.sync="dialogOrder" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form label-width="140px" size="mini" class="form-style">
          <el-form-item label="企业名称">
            <el-input v-model="dialogOrderData.merName" disabled />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="dialogOrderData.payeeName" disabled />
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="dialogOrderData.payeeIdCard" disabled />
          </el-form-item>
          <el-form-item label="银行卡号">
            <el-input v-model="dialogOrderData.payeeAcc" disabled />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="dialogOrderData.telephone" disabled />
          </el-form-item>
          <el-form-item label="收款银行">
            <el-input v-model="dialogOrderData.bankName" disabled />
          </el-form-item>
          <el-form-item label="结算金额">
            <el-input v-model="dialogOrderData.settMoney" disabled />
          </el-form-item>
          <el-form-item label="服务费">
            <el-input v-model="dialogOrderData.platFee" disabled />
          </el-form-item>
          <el-form-item label="支付通道">
            <el-input v-model="dialogOrderData.channelName" disabled />
          </el-form-item>
          <el-form-item label="商户批次号">
            <el-input v-model="dialogOrderData.batchNum" disabled />
          </el-form-item>
          <el-form-item label="商户订单号">
            <el-input v-model="dialogOrderData.merOrderNum" disabled />
          </el-form-item>
          <el-form-item label="银行流水号">
            <el-input v-model="dialogOrderData.bankOrderNum" disabled />
          </el-form-item>

          <el-form-item label="结算方式">
            <el-select v-model="dialogOrderData.orderSource" class="auto-width" disabled>
              <el-option label="全部" value="" />
              <el-option label="商户提交" value="1" />
              <el-option label="接口提交" value="2" />
              <el-option label="线下导入" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="销售名称">
            <el-input v-model="dialogOrderData.saleName" disabled />
          </el-form-item>
          <el-form-item label="代理名称">
            <el-input v-model="dialogOrderData.agentName" disabled />
          </el-form-item>
          <el-form-item label="项目负责人">
            <el-input v-model="dialogOrderData.projectName" disabled />
          </el-form-item>

          <el-form-item label="订单创建时间">
            <el-input v-model="dialogOrderData.createTime" disabled />
          </el-form-item>
          <!--          <el-form-item label="银行流水生成时间">-->
          <!--              <el-input v-model="" disabled ></el-input>-->
          <!--          </el-form-item>-->
          <el-form-item label="订单完成时间">
            <el-input v-model="dialogOrderData.orderCompleteTime" disabled />
          </el-form-item>
          <el-form-item label="订单备注">
            <el-input v-model="dialogOrderData.remark" type="textarea" :row="2" disabled />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button @click="dialogOrder = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import tradeService from '@/axios/default/tradeService'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import publicApi from '@/axios/default/public'
import moment from 'moment'

export default {
  name: 'List',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      settNum: false,
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        completeTimeStart: null,
        completeTimeEnd: null,
        // completeTimeStart: new Date(moment(new Date()).startOf('day').format('YYYY-MM-DD HH:mm:ss')),
        // completeTimeEnd: new Date(moment(new Date()).endOf('day').format('YYYY-MM-DD HH:mm:ss')),
        batchNum: '', // 批次号
        merName: '', // 企业名称
        payeeName: '', // 姓名
        tradeState: '',
        payeeIdCard: '', // 身份证号
        channelName: '', // 通道名称
        merOrderNum: '', // 商户订单号
        orderSource: '', // 结算方式
        saleName: '', // 销售名称
        agentName: '', // 代理名称
        projectName: '', // 项目负责人
        startSettMoney: '',
        endSettMoney: '',
        levyId: '',
        merNo: '',
        channelId: ''
      },
      uploadLoading: false,
      levyBodyIdArr: [],
      channelArr: [],
      formLabelWidth: '180px',
      listData: [],
      dialogOrder: false,
      dialogOrderData: {},
      flag: true,
      operateState: {
        1: '初审中',
        2: '初审拒绝',
        3: '复审',
        4: '复审通过',
        5: '复审拒绝',
        6: '系统通过',
        7: '批次风控审核中',
        8: '批次风控审核通过',
        9: '批次风控审核拒绝',
        10: '单笔风控审核中',
        11: '单笔风控审核通过',
        12: '单笔风控审核拒绝'
      },
      tradeState: { 1: '未代付', 2: '结算成功', 3: '失败', 4: '处理中', 5: '已取消' },
      orderSource: { 1: '商户提交', 2: '接口提交', 3: '线下导入', 4: '代理提现' },
      payData: {
        payMoney: 0,
        payMoneySuc: 0,
        payMoneyFai: 0,
        payMoneyPro: 0
      }
    }
  },
  created() {

  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  activated() {
    if (this.$route.query.pageType) {
      setTimeout(() => {
        this.formData.merName = this.$route.query.pageType == 1 ? this.$route.query.name : ''
        this.formData.saleName = this.$route.query.pageType == 2 ? this.$route.query.name : ''
        this.formData.createTimeFrom = this.$route.query.startTime
        this.formData.createTimeTo = this.$route.query.endTime
      }, 10)
    }
    this.formData.batchNum = this.$route.query.hasOwnProperty('batchNum') ? this.$route.query.batchNum : ''
    if (this.$route.query.hasOwnProperty('levyId') || this.$route.query.hasOwnProperty('merName')) {
      this.getLinkData()
    } else {
      this.pageNum = 1
      // this.queryTradeOrderDetailsService()
    }
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      debugger
      this.queryTradeOrderDetailsService()
    },
    getLinkData() {
      const data = this.$route.query
      this.formData.levyId = Number(data.levyId) || ''
      this.formData.merName = data.merName
      this.formData.createTimeFrom = data.createTimeFrom
      this.formData.createTimeTo = data.createTimeTo
      debugger
      this.queryTradeOrderDetailsService()
    },
    batchNumInput(v) {
      if (!v) {
        this.formData.createTimeFrom = moment(new Date(moment(new Date()).startOf('day'))).format('YYYY-MM-DD HH:mm:ss')
        this.formData.createTimeTo = moment(new Date(moment(new Date()).endOf('day'))).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    queryTradeOrderDetailsService() {
      this.formData.createTimeFrom = this.formData.batchNum ? '' : moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = this.formData.batchNum ? '' : moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      this.formData.completeTimeStart = this.formData.completeTimeStart ? moment(this.formData.completeTimeStart).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.completeTimeEnd = this.formData.completeTimeEnd ? moment(this.formData.completeTimeEnd).format('YYYY-MM-DD HH:mm:ss') : ''
      tradeService.queryTradeOrderDetailsService({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'trade': {
          ...this.formData
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum

          this.settNum = res.data.data.settNum
          this.payData.payMoney = res.data.data.payMoney
          this.payData.payMoneySuc = res.data.data.payMoneySuc
          this.payData.payMoneyFai = res.data.data.payMoneyFai
          this.payData.payMoneyPro = res.data.data.payMoneyPro
        }
      })
    },
    viewInfo(data) {
      this.dialogOrder = true
      this.dialogOrderData = data
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryTradeOrderDetailsService()
    },
    downTradeOrderDetails() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      this.formData.completeTimeStart = this.formData.completeTimeStart ? moment(this.formData.completeTimeStart).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.completeTimeEnd = this.formData.completeTimeEnd ? moment(this.formData.completeTimeEnd).format('YYYY-MM-DD HH:mm:ss') : ''
      tradeService.downTradeOrderDetails({
        ...this.formData
      }, `结算列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },
    downSettReceipt() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      this.formData.completeTimeStart = this.formData.completeTimeStart ? moment(this.formData.completeTimeStart).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.completeTimeEnd = this.formData.completeTimeEnd ? moment(this.formData.completeTimeEnd).format('YYYY-MM-DD HH:mm:ss') : ''
      tradeService.downSettReceipt({
        ...this.formData
      }, `结算回单${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.zip`, '.zip').then(res => {

      })
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
            this.queryChannelInfoByLevyId(this.levyBodyIdArr[0].id)
            this.flag = false
          }

          this.queryTradeOrderDetailsService()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    queryChannelInfoByLevyId(levyBodyId) {
      this.formData.channelId = ''
      publicApi.queryChannelInfoByLevyId({ levyBodyId }).then(res => {
        if (res.data.code === '0000') {
          this.channelArr = res.data.data
          if (this.channelArr.length === 1) {
            this.formData.channelId = this.channelArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    uploadImport(param) {
      const that = this
      const size = param.file.size
      const isLtM = size / 1024 / 1024 < 50
      if (!isLtM) {
        setTimeout(function() {
          // that.$message.error('上传大小不能超过 15MB!')
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      this.uploadLoading = true
      const formData = new FormData()
      formData.append('file', param.file)
      tradeService.tradeServiceImport(formData).then(res => {
        this.uploadLoading = false
        if (res.data.code === '0000') {
          this.$message.success('操作成功')
          this.queryTradeOrderDetailsService()
        } else {
          this.$message.error(res.data.message)
        }
      })
    },

    goProject(args) {
      this.$router.push({ path: '/proManage/list', query: { projectId: args }})
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card {
    margin-bottom: 20px;
  }

  .auto-width {
    width: 100%;
  }

  .dialog-form {
    display: flex;
    flex-flow: column nowrap;
  }

  .el-form-item__content {
    margin-left: 0;
  }

  .list-table {
    margin: 0 0 20px 0;
    .el-button {
      padding: 0;
    }
  }

  .dialog-scroll {
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog {
    /deep/ .el-dialog {
      height: 80vh;
      overflow: hidden;
      .el-dialog__body {
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }

  .reset-dialog-small {
    /deep/ .el-dialog {
      height: 65vh;
    }
    overflow: hidden;
  }

  .form-style {
    padding-right: 20px;
  }

  .table-head-money .el-col .table-headers {
    display: flex;
    flex-flow: nowrap column;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #eff2f6;
    .table-header-num {
      font-size: 18px;
      margin-bottom: 5px;
      color: #333;
    }
    .table-header-name {
      font-size: 12px;
      color: #999;
    }
  }

  .table-head-money .el-col:last-child .table-headers {
    border-right: none;
    .table-header-num {
      color: #f54343;
    }
  }

  .upload-more {
    display: inline-block;
  }

  .goProject {
    color: #2b66fd;
    cursor: pointer;
  }
</style>

