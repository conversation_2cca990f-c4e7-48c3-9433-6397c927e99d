// 站内消息
import request from '@/axios/default/request'
import { API_message as API_Header } from '../index'

const api = {
  addMessageVO: `${API_Header}/messageInfo/addMessageVO`, // 发送公告
  updateMessageVO: `${API_Header}/messageInfo/updateMessageVO`, // 修改公告
  findNoticeByConditionByPage: `${API_Header}/messageInfo/findNoticeByConditionByPage` // 分页查询公告列表
}

const message = {
  addMessageVO: params => {
    return request.postJson(api.addMessageVO, params)
  },
  updateMessageVO: params => {
    return request.postJson(api.updateMessageVO, params)
  },
  findNoticeByConditionByPage: params => {
    return request.postJson(api.findNoticeByConditionByPage, params)
  }
}

export default message

