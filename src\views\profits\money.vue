<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="申请日期起" class="form-items" prop="createTimeFrom">
                    <el-date-picker
                      v-model="formData.applyTimeStart"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="申请日期止" class="form-items" prop="createTimeTo">
                    <el-date-picker
                      v-model="formData.applyTimeEnd"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="审核状态" class="form-items">
                    <el-select v-model="formData.auditStatus" placeholder="请选择审核状态" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option v-for="(item,key) in auditStatus" :label="item" :value="key" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="申请人" class="form-items">
                    <el-input v-model="formData.applyName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="list-card">
        <div class="table-top">
          <el-button v-if="listData.length>0" plain size="mini" @click="batchMoney">批量打款</el-button>
        </div>
        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column
            prop="applyTime"
            label="申请日期"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="auditStatus"
            label="审核状态"
            width="100"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ auditStatus[scope.row.auditStatus] }}
            </template>
          </el-table-column>

          <el-table-column
            prop="applyName"
            label="申请人"
            width="100"
            show-overflow-tooltip
          />

          <el-table-column
            prop="payeeName"
            label="收款人"
            width="100"
            show-overflow-tooltip
          />

          <el-table-column
            prop="payeeAcc"
            label="收款账号"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="payeeCardId"
            label="证件号"
            width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="telephone"
            label="手机号"
            width="120"
            show-overflow-tooltip
          />

          <el-table-column
            prop="reAuditMoney"
            label="打款金额"
            width="100"
            show-overflow-tooltip
          />

          <el-table-column
            label="备注"
            width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <!--              {{`初审备注：${scope.row.firistAuditRemark || ''};复审备注：${scope.row.reAuditRemark || ''}`}}-->
              {{ scope.row.firistAuditRemark ? `初审备注：${scope.row.firistAuditRemark};` : '' }}
              {{ scope.row.reAuditRemark ? `复审备注：${scope.row.reAuditRemark}` : '' }}
            </template>
          </el-table-column>

        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog title="批量打款" :visible.sync="dialogMoney" width="50%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogMoneyForm" label-width="140px" size="mini" class="form-style" :model="dialogMoneyForm" :rules="rules">
          <el-form-item label="打款金额" prop="applyMoney">
            <el-input v-model="dialogMoneyForm.applyMoney" disabled />
          </el-form-item>
          <el-form-item label="打款人数" prop="applyNum">
            <el-input v-model="dialogMoneyForm.applyNum" disabled />
          </el-form-item>
          <el-form-item label="登录密码" prop="passWord">
            <el-input v-model="dialogMoneyForm.passWord" type="password" show-password />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogMoneyForm','1')">确定</el-button>
              <el-button @click="closedialogMoneyForm('dialogMoneyForm')">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import profits from '@/axios/default/profits'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import publicApi from '@/axios/default/public'
import moment from 'moment'
export default {
  name: 'Money',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        applyTimeStart: new Date(moment(new Date()).startOf('day')),
        applyTimeEnd: new Date(moment(new Date()).endOf('day')),
        auditStatus: '5',
        applyName: ''
      },
      listData: [],
      incomeMoney: '',
      applyMoney: '',
      btnLoading: false,
      dialogMoney: false,
      dialogMoneyForm: {},
      rules: {
        passWord: [{ required: true, message: '请填写登录密码', trigger: 'blur' }]
      },
      multipleSelection: [],
      auditStatus: { 1: '初审中', 2: '初审拒绝', 3: '复审中', 4: '复审拒绝', 5: '复审成功', 6: '打款中', 7: '打款成功', 8: '打款失败' }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      this.formData.applyTimeStart = moment(this.formData.applyTimeStart).format('YYYY-MM-DD HH:mm:ss')
      this.formData.applyTimeEnd = moment(this.formData.applyTimeEnd).format('YYYY-MM-DD HH:mm:ss')
      profits.queryCashApplyByPage({
        'pageSize': this.pageSize,
        'pageNum': this.pageNum,
        ...this.formData
      }).then(res => {
        if (res.data.code == '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
          this.incomeMoney = res.data.data.incomeMoney
          this.applyMoney = res.data.data.applyMoney
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    handleSelectionChange(data) {
      this.multipleSelection = data
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    closedialogMoneyForm(formName) {
      this.dialogMoney = false
      this.$refs[formName].resetFields()
    },
    batchMoney() {
      if (this.multipleSelection.length > 0) {
        this.dialogMoney = true
        profits.queryApplyCashCal({
          batchNum: this.multipleSelection.map(res => res.batchNum).join(',')
        }).then(res => {
          if (res.data.code === '0000') {
            this.dialogMoneyForm = { ...res.data.data }
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      } else {
        newAlert(this.$tips, '请选择要打款的数据')
      }
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          profits.checkPassword({
            passWord: this.dialogMoneyForm.passWord
          }).then(res => {
            if (res.data.code === '0000') {
              profits.submitApplyCash({
                batchNum: this.multipleSelection.map(res => res.batchNum).join(',')
              }).then(res => {
                if (res.data.code === '0000') {
                  this.$message({
                    type: 'success',
                    message: '操作成功',
                    onClose: () => {
                      this.btnLoading = false
                    }
                  })
                  this.multipleSelection = []
                  this.dialogMoney = false
                  this.list()
                } else {
                  this.$message({
                    type: 'error',
                    message: res.data.message,
                    onClose: () => {
                      this.btnLoading = false
                    }
                  })
                }
              })
            } else {
              const that = this
              newAlert(this.$tips, res.data.message, function() {
                that.btnLoading = false
              })
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .table-head-money .el-col .table-headers{
    display: flex;
    flex-flow: nowrap column;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #eff2f6;
    .table-header-num{
      font-size: 18px;
      margin-bottom: 5px;
      color: #333;
    }
    .table-header-name{
      font-size: 12px;
      color: #999;
    }
  }
  .table-head-money .el-col:last-child .table-headers{
    border-right: none;
    .table-header-num{
      color: #F54343;
    }
  }

</style>

