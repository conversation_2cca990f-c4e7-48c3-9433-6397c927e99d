<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="开票申请时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="是否预开" class="form-items">
                    <el-select v-model="formData.invPreopen" placeholder="请选择是否预开" class="auto-width">
                      <el-option label="请选择" value="" />
                      <el-option label="是" value="1" />
                      <el-option label="否" value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="list-card">
        <el-row class="table-head-money">
          <el-col :span="24">
            <div class="table-headers">
              <span class="table-header-num">{{ invoiceData.dealInvAmount || 0 }}</span>
              <span class="table-header-name">需上传开票金额统计(元)</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
        <el-card class="list-card">
        <el-table
          :data="listData"
          border
          stripe
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">

              <el-popover
                placement="top"
                width="200"
                trigger="hover">
                <p>可开发票金额：{{scope.row.canInvoiceBalance}}</p>
                <p>已开发票金额：{{scope.row.invoiceBalance}}</p>
                <p>已下发金额：{{scope.row.tradeMoney}}</p>
                <p>已充值金额：{{scope.row.rechMoney}}</p>
                <p slot="reference">{{scope.row.merName}}</p>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="invoiceTypeName"
            label="发票类目"
            width="300"
            show-overflow-tooltip
          />
          <el-table-column
            prop="invAmount"
            label="开票金额"
          />
          <el-table-column
            prop="invQuantity"
            label="开票数量（张）"
            width="110"
          />
          <el-table-column
            prop="createTime"
            label="发票申请时间"
            width="150"
          />
          <!--          <el-table-column-->
          <!--            prop="fileSize"-->
          <!--            label="开票状态"-->
          <!--          />-->
          <el-table-column
            prop="levyName"
            label="开票方"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="auditRemark"
            label="审核情况"
            show-overflow-tooltip
          />

          <el-table-column
            prop="invCategory"
            label="开票类型"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invCategory == '1'" class="invoice-ordinary">普票</span>
              <span v-if="scope.row.invCategory == '2'" class="invoice-specially">专票</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="invPreopen"
            label="是否预开"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invPreopen == '1'" style="color: #f54343">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="操作"
            width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="jumpDetail(scope.row.id)">查看详情</el-button>
              <el-button type="text" @click="tomail(scope.row)">上传</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    开票上传-->
    <el-dialog title="开票上传" :visible.sync="dialogA" width="40%" class="reset-dialog reset-dialog-small" @close="clickbox">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="imgForm" :model="imgForm" label-width="100px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="商户名称" class="form-items">
            <el-input v-model="dialogAData.merName" disabled />
          </el-form-item>
          <el-form-item label="开票金额" class="form-items">
            <el-input v-model="dialogAData.invAmount" disabled />
          </el-form-item>
          <el-form-item label="开票类目" class="form-items">
            <el-input v-model="dialogAData.invoiceTypeName" type="textarea" disabled />
          </el-form-item>
          <el-form-item label="是否预开" class="form-items">
            {{ dialogAData.invPreopen == '1' ? '是' : '否' }}
          </el-form-item>

          <el-form-item label="初审备注" class="form-items">
            <el-input v-model="trialauditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="复审备注" class="form-items">
            <el-input v-model="reviewauditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="上传开票" class="form-items" prop="fileList">
            <div style="color: red; font-size: 12px;">*注：以下上传发票方式任选其一即可</div>
            <el-input style="margin-bottom: 14px;" v-model="imgForm.filePathTxt" clearable type="textarea" :rows="2" placeholder="请输入电子发票网址（若存在多张发票，则以英文字符“,”隔开）" />
            <el-upload
              ref="uploaderkp"
              action=""
              accept=".pdf,.jpg,.jpeg,.png"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :file-list="imgForm.fileList"
              :http-request="uploader"
            >
              <div style="display: flex">
                <el-button size="small" type="primary"><i slot="default" class="el-icon-plus" />点击上传</el-button>
                <div slot="tip" class="el-upload__tip">仅可上传pdf/jpg/jpeg/png文件</div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" :loading="btnLoad" @click="submit('imgForm')">保存</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import invoiceType from '@/axios/default/invoiceType'
import publicApi from '@/axios/default/public'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        'createTimeFrom': '',
        'createTimeTo': '',
        'auditState': '5',
        'merName': '',
        'invPreopen': '',
        'levyId': ''
      },
      levyBodyIdArr: [],
      invoiceData: {
        totalInvAmount: 0,
        successInvAmount: 0,
        failInvAmount: 0,
        dealInvAmount: 0
        },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            const end = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
            const start = new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogA: false,
      dialogAData: {
        trackNumber: ''
      },
      trialauditRemark: '',
      reviewauditRemark: '',
      dialogVisible: false,
      imgForm: {
        fileList: [],
        filePathTxt: '',
        uploadTime: '',
      },
      btnLoad: false,
      rules: {
      }
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
    this.queryInvoiceInfo()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryInvoiceInfo()
    },
    //   列表
    queryInvoiceInfo() {
      invoiceType.queryInvoiceInfo({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'InvoiceInfo': {
          'createTimeFrom': this.pickTime[0],
          'createTimeTo': this.pickTime[1],
          'auditState': this.formData.auditState,
          'merName': this.formData.merName,
          'invPreopen': this.formData.invPreopen,
          'levyId': this.formData.levyId
        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum

        this.invoiceData.totalInvAmount = res.data.data.totalInvAmount
        this.invoiceData.successInvAmount = res.data.data.successInvAmount
        this.invoiceData.dealInvAmount = res.data.data.dealInvAmount
        this.invoiceData.failInvAmount = res.data.data.failInvAmount
      })
    },
    // 打开上传框
    tomail(data) {
      this.dialogA = true
      this.dialogAData = data
      this.queryInvoiceAuditRecord(data.id)
    },
    // 提交审核
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoad = true
          const formData = new FormData()
          const data = this.imgForm.fileList
          for (let i = 0; i < data.length; i++) {
            formData.append('files', data[i])
          }
          formData.append('filePathTxt', this.imgForm.filePathTxt)
          formData.append('id', this.dialogAData.id)

          invoiceType.uploadInvoice(formData).then(res => {
            this.btnLoad = false
            this.imgForm.fileList = []
            this.imgForm.filePathTxt = ''
            if (res.data.code == '0000') {
              this.dialogA = false
              this.$message({ type: 'success', message: '操作成功' })
              this.queryInvoiceInfo()
            } else {
              newAlert(this.$tips, res.data.message)
              this.btnLoad = false
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    handlePreview(file) {
      console.log(file, '查看信息')
    },

    // 删除更新图片列表
    handleRemove(file, fileList) {
      this.imgForm.fileList = fileList.map(res => {
        return res.raw
      })
    },
    // 上传更新图片列表
    uploader(param) {
      this.imgForm.fileList.unshift(param.file)
    },
    // 分页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryInvoiceInfo()
    },
    queryInvoiceAuditRecord(id) {
      invoiceType.queryInvoiceAuditRecord({
        invId: id
      }).then(res => {
        this.trialauditRemark = res.data.data.listRec[0].auditReason
        this.reviewauditRemark = res.data.data.listRec[1].auditReason
      })
    },
    clickbox() {
      this.imgForm.fileList = []
      this.$refs.uploaderkp.clearFiles()
    },
    // 查看详情
    jumpDetail(id) {
      this.$router.push({
        path: '/invoice/detail',
        query: {
          origin: 'upload',
          type: 'view',
          id
        }
      })
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.table-head-money .el-col .table-headers {
    display: flex;
    flex-flow: nowrap column;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #eff2f6;
    .table-header-num {
      font-size: 18px;
      margin-bottom: 5px;
      color: #333;
    }
    .table-header-name {
      font-size: 12px;
      color: #999;
    }
  }

  .table-head-money .el-col:last-child .table-headers {
    border-right: none;
  }


.auto-width{
  width: 100%;
}

.dialog-form{
  display: flex;
  flex-flow: column nowrap;
}
/deep/ .el-form-item__content{
  margin-left: 0!important;
}

.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
}

.dialog-scroll{
  overflow-y: auto;
  height: calc(100%);
}

.reset-dialog{
  /deep/ .el-dialog{
    height: 80vh;
    overflow: hidden;
    .el-dialog__body{
      height: calc(100% - 54px);
    }
  }
  overflow: hidden;
}
.reset-dialog-small{
  /deep/ .el-dialog{
    height: 65vh;
  }
  overflow: hidden;
}
.form-style{
  padding-right: 20px;
}
.invoice-ordinary{
  color: #f29c39;
}
.invoice-specially{
  color: #5791fd;
}
</style>
