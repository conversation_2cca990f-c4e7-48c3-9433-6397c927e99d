<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div :class="{hasTagsView:needTagsView}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <!--      <div class="footer">© 2020 <a target="_blank" href="https://mrbird.cc">MrBird</a> - TaxPay</div>-->
      <!--      <right-panel v-if="showSettings">-->
      <!--        <settings/>-->
      <!--      </right-panel>-->
      <right-panel v-if="showMessage">
        <MessageSocket :rows="rows" :loaded="loaded" @read="onRead" @allRead="onAllRead" @loadMore="loadMore" />
      </right-panel>
    </div>
  </div>
</template>
<script>
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView, MessageSocket } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import index from '@/axios/default/index/index'
import { newAlert } from '@/utils'
import socket from '@/utils/socket'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    RightPanel,
    // Settings,
    Sidebar,
    TagsView,
    MessageSocket
  },
  mixins: [ResizeMixin],
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      rows: [],
      loaded: false
    }
  },
  computed: {
    sidebar() {
      return this.$store.state.setting.sidebar
    },
    device() {
      return this.$store.state.setting.device
    },
    showSettings() {
      return this.$store.state.setting.settingBar.opened
    },
    showMessage() {
      return this.$store.state.message.settingBar.opened
    },
    needTagsView() {
      return this.$store.state.setting.multipage
    },
    fixedHeader() {
      return this.$store.state.setting.fixHeader
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getMessageList()
  },
  mounted() {
    socket.connection()
    socket.onopen()
    socket.getMessage()
    socket.onclose()
  },
  methods: {
    getMessageList() {
      index.messageList({
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }).then(res => {
        console.log(res, '获取已发送消息成功')
        if (res.data.code === '0000') {
          const { pageNum, pageSize, rows, total } = res.data.data
          if (!this.loaded) {
            this.pageNum = pageNum + 1
            this.rows.push(...rows)
            this.$store.commit('message/setNotReadNum', this.rows.filter(item => item.isUse == 0).length)
            if (pageNum * pageSize >= total) {
              this.loaded = true
            }
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    handleClickOutside() {
      this.$store.commit('setting/closeSidebar', { withoutAnimation: false })
    },
    onRead(v) {
      this.readEr(v.id.toString(), 'single')
    },
    onAllRead(v) {
      const reqRead = []
      this.rows.forEach(item => {
        reqRead.push(item.id)
      })
      this.readEr(reqRead.toString(), 'batch')
    },
    readEr(ids, type) {
      index.readMessage({
        id: ids
      }).then(res => {
        console.log(res, '标记已读成功')
        if (res.data.code === '0000') {
          if (type === 'single') {
            this.rows.forEach(item => {
              if (item.id == ids) {
                item.isUse = 1
              }
            })
          } else if (type === 'batch') {
            this.rows.forEach(item => {
              item.isUse = 1
            })
          }
          this.$store.commit('message/setNotReadNum', this.rows.filter(item => item.isUse == 0).length)
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    loadMore() {
      this.getMessageList()
    }
  }
}
</script>
<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px)
  }

  .mobile .fixed-header {
    width: 100%;
  }

  .footer {
    position: fixed;
    bottom: 0;
    right: 0;
    text-align: center;
    height: 2.4rem;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
    font-size: 13px;
    background: #fff;
    width: calc(100% - #{$sideBarWidth});
    display: block;
    z-index: 999;
    color: #606266;
    line-height: 2.4rem;
  }

  .hideSidebar .footer {
    width: calc(100% - 54px)
  }

  .mobile .footer {
    width: 100%;
  }
</style>
<style lang="scss">
  @import "~@/styles/diy-icon.scss";
  .content-main {
    padding: 20px 20px 0 20px;
    margin: 0
  }

  .general-layout {
    padding: 15px;
    background: #fff;
  }

  .el-card__header {
    padding: 15px 20px;
    font-weight: bold;
  }

  .el-form-item__content {
    flex: 1;
  }

  .el-table th {
    padding: 14px 0;
  }

  li.el-menu-item {
    /*padding-left: 30px !important;*/
    /*i {*/
    /*  color: #fff;*/
    /*  margin: 0 10px 0 0!important;*/
    /*}*/
  }

  .el-menu--collapse li.submenu-title-noDropdown {
    margin: 0;

    i {
      margin-left: 10px !important;
    }
  }
  .el-button--mini{
    padding: 7px 15px;
  }

  .btn-del{
    color: red;
  }
  .btn-stop,.btn-enable{

  }
  .btn-edit{

  }
  .form-items{
    width: 100%;
    display: flex!important;
    .el-button{
      /*margin-right: 0;*/
    }
  }

</style>
