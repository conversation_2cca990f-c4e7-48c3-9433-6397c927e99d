<template>
  <div class="login-container">
    <div class="login_wrap">
      <img class="login-banner" src="../../assets/login_banner.png" alt="">

      <el-form ref="loginForm" :model="loginForm" :rules="rules" class="login-form" autocomplete="off" label-position="left">

        <div class="login-tab">
          <span>运营平台</span>
          <p></p>
        </div>

        <!--      帐号密码登录-->
        <div v-if="tabKey=='1'" class="tab1">
          <div class="login_user_input">
            <div class="img-icon">
              <img src="../../assets/web/login_user_icon.png" alt="">
            </div>
            <el-form-item prop="username">
              <el-input ref="username" v-model="loginForm.username" :placeholder="$t('login.username')"
                        prefix-icon="el-icon-user" name="username" type="text" autocomplete="off"
                        @keyup.enter.native="handleLogin"/>
            </el-form-item>
          </div>
          <div class="login_user_input">
            <div class="img-icon">
              <img src="../../assets/web/login_pass_icon.png" alt="">
            </div>
            <el-form-item prop="password">
              <el-input ref="password" v-model="loginForm.password" prefix-icon="el-icon-key" type="password"
                        :placeholder="$t('login.password')" name="password" autocomplete="off" :show-password="true"
                        @keyup.enter.native="handleLogin"/>
            </el-form-item>
          </div>
          <el-form-item prop="code" class="code-input">
            <el-input ref="code" v-model="loginForm.code" :placeholder="$t('login.code')"
                      name="code" type="text" autocomplete="off" style="width: 134px" @keyup.enter.native="handleLogin"/>
          </el-form-item>
          <img :src="imageCode" alt="codeImage"  class="code-image" @click="getCodeImage">
          <el-button :loading="loading" class="xzb-btn xzb-main-btn-style" style="width:100%;" @click.native.prevent="handleLogin">
            {{ $t('login.logIn') }}
          </el-button>
          <div />
        </div>
      </el-form>



      <!--<el-form ref="loginForm" :model="loginForm" :rules="rules" class="login-form" autocomplete="off"-->
               <!--label-position="left">-->
        <!--<div class="title-container">-->
          <!--<h3 class="title">-->
            <!--{{ $t('login.title') }}-->
          <!--</h3>-->
        <!--</div>-->
        <!--<span v-if="login.type === 'up'">-->
        <!--<el-form-item prop="username">-->
          <!--<el-input ref="username" v-model="loginForm.username" :placeholder="$t('login.username')"-->
                    <!--prefix-icon="el-icon-user" name="username" type="text" autocomplete="off"-->
                    <!--@keyup.enter.native="handleLogin"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item prop="password">-->
          <!--<el-input ref="password" v-model="loginForm.password" prefix-icon="el-icon-key" type="password"-->
                    <!--:placeholder="$t('login.password')" name="password" autocomplete="off" :show-password="true"-->
                    <!--@keyup.enter.native="handleLogin"/>-->
        <!--</el-form-item>-->
        <!--<el-form-item prop="code" class="code-input">-->
          <!--<el-input ref="code" v-model="loginForm.code" prefix-icon="el-icon-lock" :placeholder="$t('login.code')"-->
                    <!--name="code" type="text" autocomplete="off" style="width: 80%" @keyup.enter.native="handleLogin"/>-->
        <!--</el-form-item>-->
        <!--<img :src="imageCode" alt="codeImage" class="code-image" @click="getCodeImage">-->
        <!--<el-button :loading="loading" type="primary" style="width:100%;margin-bottom:14px;"-->
                   <!--@click.native.prevent="handleLogin">-->
          <!--{{ $t('login.logIn') }}-->
        <!--</el-button>-->
      <!--</span>-->

      <!--</el-form>-->
      <!--<span class="login-footer">-->
      <!--{{ merName }} {{ copyRight }}-->
    <!--</span>-->
    </div>
  </div>
</template>

<script>
  import db from '@/utils/localstorage'
  import {randomNum, newAlert} from '@/utils'
  import axios from 'axios'

  export default {
    name: 'Login',
    data() {
      return {
        tabKey: '1',
        merName: process.env.VUE_APP_BASE_MERNAME,
        copyRight: process.env.VUE_APP_BASE_COPYRIGHT,
        codeUrl: `${process.env.VUE_APP_BASE_API}auth/captcha`,
        imageCode: '',
        login: {
          type: 'up'
        },
        loginForm: {
          username: '',
          password: '',
          code: ''
        },
        rules: {
          username: { required: true, message: '账号不能为空', trigger: 'blur' },
          password: { required: true, message: '密码不能为空', trigger: 'blur' },
          code: { required: true, message: '请填写验证码', trigger: 'blur' }
        },

        loading: false,
        randomId: randomNum(24, 16)
      }
    },
    mounted() {
      db.clear()
      this.getCodeImage()
    },
    methods: {
      getCodeImage() {
        axios({
          method: 'GET',
          url: `${this.codeUrl}?key=${this.randomId}`,
          responseType: 'arraybuffer'
        }).then(res => {
          return 'data:image/png;base64,' + btoa(
            new Uint8Array(res.data)
              .reduce((data, byte) => data + String.fromCharCode(byte), '')
          )
        }).then((res) => {
          this.imageCode = res
        }).catch((e) => {
          if (e.toString().indexOf('429') !== -1) {
            this.$message({
              message: this.$t('tips.tooManyRequest'),
              type: 'error'
            })
          } else {
            this.$message({
              message: this.$t('tips.getCodeImageFailed'),
              type: 'error'
            })
          }
        })
      },

      resolveLogo(logo) {
        return require(`@/assets/logo/${logo}`)
      },
      handleLogin() {
        let username_c = false
        let password_c = false
        let code_c = false
        this.$refs.loginForm.validateField('username', e => {
          if (!e) {
            username_c = true
          }
        })
        this.$refs.loginForm.validateField('password', e => {
          if (!e) {
            password_c = true
          }
        })
        this.$refs.loginForm.validateField('code', e => {
          if (!e) {
            code_c = true
          }
        })
        if (username_c && password_c && code_c) {
          this.loading = true
          const that = this
          this.$login('auth/oauth/token', {
            username: `${that.loginForm.username}`,
            password: that.loginForm.password,
            code: that.loginForm.code,
            key: that.randomId
          }).then((r) => {
            const data = r.data
            this.saveLoginData(data)
            this.getUserDetailInfo()
            this.loginSuccessCallback()
          }).catch((error) => {
            console.error(error)
            that.loading = false
            that.getCodeImage()
          })
        }
      },
      saveLoginData(data) {
        this.$store.commit('account/setAccessToken', data.access_token)
        this.$store.commit('account/setRefreshToken', data.refresh_token)
        const current = new Date()
        const expireTime = current.setTime(current.getTime() + 1000 * data.expires_in)
        this.$store.commit('account/setExpireTime', expireTime)
      },
      getUserDetailInfo() {
        this.$get('auth/user').then((r) => {
          this.$store.commit('account/setUser', r.data.principal)
          this.$message({
            message: this.$t('tips.loginSuccess'),
            type: 'success'
          })
          this.loading = false
          this.$router.push('/')
        }).catch((error) => {
          this.$message({
            message: this.$t('tips.loginFail'),
            type: 'error'
          })
          console.error(error)
          this.loading = false
        })
      },
      loginSuccessCallback() {
        this.$get('system/user/success').catch((e) => {
          console.log(e)
        })
      }
    }
  }
</script>

<style lang="scss">
  @import "login";
</style>
<style lang="scss" scoped>
  @import "login-scoped";

  @media screen and (max-width: 970px) {
    .login-banner {
      display: none;
    }
  }
  .xzb-main-btn-style{
    margin-top: 20px;
    background: #355DFF;
    border: 1px solid #DBE7FF;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
    color: #FFFFFF;
  }
  .el-form-item__error {
    color: #000 !important;
  }
  .login_wrap {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: flex-end;

  }
  .login-banner {
    width: 65%;
  }
  .login-tab{
    text-align: center;
    margin-bottom: 44px;
    span{
      cursor: pointer;
      font-size: 20px;
      font-weight: 500;
      color: #1B1D21;
    }
    p {
      margin: 8px auto 0;
      width: 45px;
      height: 4px;
      background: #355DFF;
    }
  }
  .nopassword{
    float: right;
    color: #fff;
    font-size: 12px;
  }
  .nopassword:hover{
    text-decoration: underline;
    color: #355DFF;
  }
  .sendCode{
    width: 120px;
    background: #54565A;
    border-radius: 0;
    color: #fff;
    height: 40px;
    border: 1px dashed #979798;
    margin: 0;
    float: right;
    padding: 0;
    font-size: 12px;
  }
</style>
