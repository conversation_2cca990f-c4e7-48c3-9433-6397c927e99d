<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="创建时间起" class="form-items" prop="createTimeFrom">
                    <el-date-picker
                      v-model="formData.createTimeFrom"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="创建时间止" class="form-items" prop="createTimeTo">
                    <el-date-picker
                      v-model="formData.createTimeTo"
                      class="auto-width"
                      type="datetime"
                      :clearable="false"
                      :editable="false"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                </el-col>

                <!--                <el-col :lg="10" :xs="24">-->
                <!--                  <el-form-item label="创建时间" class="form-items">-->
                <!--                    <el-date-picker-->
                <!--                      v-model="formData.creatTime"-->
                <!--                      class="auto-width"-->
                <!--                      type="daterange"-->
                <!--                      align="right"-->
                <!--                      unlink-panels-->
                <!--                      range-separator="-"-->
                <!--                      format="yyyy-MM-dd HH:mm:ss"-->
                <!--                      value-format="yyyy-MM-dd HH:mm:ss"-->
                <!--                      start-placeholder="开始时间"-->
                <!--                      end-placeholder="结束时间"-->
                <!--                      :picker-options="pickerOptions"-->
                <!--                      @change="resetTime"-->
                <!--                    />-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->
                <el-col :lg="7" :xs="24">
                  <el-form-item label="公司名称" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="充值名称" class="form-items">
                    <el-input v-model="formData.rechargeName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="商户编号" class="form-items">
                    <el-input v-model="formData.merchantNo" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-download" type="primary" @click="downData">下载数据</el-button>
                    <el-button v-has-permission="['merchantRechargeManager:add']" icon="el-icon-plus" type="primary" @click="add">新增企业</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantNo"
            label="商户编号"
            width="170"
          />
          <el-table-column
            prop="merchantName"
            label="公司名称"
            width="200"
          />
          <el-table-column
            prop="rechargeName"
            label="充值名称"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="150"
          />
          <el-table-column
            prop=""
            label="操作"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-popconfirm v-has-permission="['merchantRechargeManager:delete']" title="确认删除该充值帐号吗？" @onConfirm="del(scope.row.id)">
                <el-button slot="reference" type="text">删除</el-button>
              </el-popconfirm>
              <el-button v-has-permission="['merchantRechargeManager:update']" type="text" @click="edit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>

    </div>
    <el-dialog
      :title="dialogName"
      :visible.sync="addListDialog"
      width="50%"
    >
      <el-form ref="dialogData" :model="dialogData" size="mini" class="form-style" :rules="rules">
        <el-form-item label="商户名称" prop="merchantId" class="form-items">
          <el-select
            v-model="dialogData.merchantId"
            filterable
            clearable
            remote
            class="auto-width"
            placeholder="请输入关键词"
            :remote-method="merAllList"
            :loading="loading"
          >
            <el-option v-for="item in merIdArr" :key="item.id" :label="item.merName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="充值名称" prop="rechargeName" class="form-items">
          <el-input v-model="dialogData.rechargeName" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item>
          <div style="text-align: right">
            <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">保存</el-button>
            <el-button :loading="btnLoading" @click="closeDialog('addListDialog','dialogData')">取消</el-button>
          </div>
        </el-form-item>
      </el-form>

    </el-dialog>
  </div>
</template>
<script>
import merchants from '@/axios/default/merchants'
import levyBody from '@/axios/default/levyBody'
import publics from '@/axios/default/public'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import moment from 'moment'
export default {
  name: 'AccountList',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        merchantName: '',
        rechargeName: '',
        merchantNo: ''
      },
      dialogData: {
        merchantId: '',
        rechargeName: ''
      },
      listData: [],
      merIdArr: [],
      addListDialog: false,
      btnLoading: false,
      loading: false,
      dialogType: '',
      dialogName: '',
      rules: {
        merchantId: [{ required: true, message: '请填写公司名称', trigger: 'change' }],
        rechargeName: [{ required: true, message: '请填写充值名称', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.merAllList()
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    list() {
      // this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      // this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeFrom = this.formData.createTimeFrom ? moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.createTimeTo = this.formData.createTimeTo ? moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss') : ''
      merchants.findMerchantRechargeManager({
        ...this.formData,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    // 删除
    del(id) {
      merchants.deleteMerchantRechargeManager({ id }).then(res => {
        if (res.data.code == '0000') {
          this.$message.success('操作成功')
          this.list()
        }
      })
    },
    // 编辑
    edit(data) {
      this.addListDialog = true
      this.dialogType = 'edit'
      this.dialogData = { ...data }
      this.dialogName = '编辑企业'
    },
    // 新增
    add() {
      this.dialogData = {
        merchantId: '',
        rechargeName: ''
      }
      this.addListDialog = true
      this.dialogType = 'add'
      this.dialogName = '新增企业'
      this.$refs.dialogData.resetFields()
    },
    // 提交
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogType == 'add') {
            this.addMerchantRechargeManager()
          } else {
            this.updateMerchantRechargeManager()
          }
        } else {
          return false
        }
      })
    },
    addMerchantRechargeManager() {
      const that = this
      merchants.addMerchantRechargeManager({
        ...this.dialogData
      }).then(res => {
        if (res.data.code == '0000') {
          this.addListDialog = false
          that.$message({
            type: 'success',
            message: '操作成功',
            onClose: () => {
              this.btnLoading = false
            }
          })
          this.$refs.dialogData.resetFields()
          this.list()
        } else {
          that.$message({
            type: 'error',
            message: res.data.message,
            onClose: () => {
              that.btnLoading = false
            }
          })
        }
      }).catch(res => {
        that.$message({
          type: 'error',
          message: res.data.message,
          onClose: () => {
            that.btnLoading = false
          }
        })
      })
    },
    updateMerchantRechargeManager() {
      const that = this
      merchants.updateMerchantRechargeManager({
        ...this.dialogData
      }).then(res => {
        if (res.data.code == '0000') {
          this.addListDialog = false
          this.$refs.dialogData.resetFields()
          that.$message({
            type: 'success',
            message: '操作成功',
            onClose: () => {
              that.btnLoading = false
            }
          })
          this.list()
        } else {
          that.$message({
            type: 'error',
            message: '操作失败',
            onClose: () => {
              that.btnLoading = false
            }
          })
        }
      }).catch(res => {
        that.$message({
          type: 'error',
          message: res.data.message,
          onClose: () => {
            that.btnLoading = false
          }
        })
      })
    },
    // 下载
    downData() {
      this.formData.createTimeFrom = this.formData.createTimeFrom ? moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.createTimeTo = this.formData.createTimeTo ? moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss') : ''
      merchants.excel({
        ...this.formData,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      }, `商户充值账户列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },

    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    closeDialog(formVisible, formName) {
      this[formVisible] = false
      this.$refs[formName].resetFields()
    },
    merAllList(query) {
      if (query !== '') {
        this.loading = true
        publics.merAllList({
          merName: query
        }).then(res => {
          this.loading = false
          this.merIdArr = res.data.data
        })
      } else {
        this.merIdArr = []
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }
  .auto-width{
    width: 100%;
  }
</style>
