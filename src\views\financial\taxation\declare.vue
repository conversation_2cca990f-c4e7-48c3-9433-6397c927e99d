<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="申报时间" class="form-items">
                    <el-date-picker
                      v-model="formData.monthDay"
                      class="auto-width"
                      value-format="yyyy-MM"
                      type="month"
                      placeholder="选择日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="姓名" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="身份证号" class="form-items">
                    <el-input v-model="formData.idCard" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="exportList">下载申报明细</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="monthDay"
            label="报税月份"
          />
          <el-table-column
            prop="name"
            label="姓名"
            width="100px"
          />
          <el-table-column
            prop="idCard"
            label="身份证号"
            width="180px"
          />
          <el-table-column
            prop="mobile"
            width="120px"
            label="联系电话"
          />
          <el-table-column
            prop="taxBasis"
            label="应税收入"
            width="120px"
          />
          <el-table-column
            prop="taxPayAmt"
            label="应代征税额"
            width="120px"
          />
          <el-table-column
            prop="merName"
            width="250"
            label="商户名称"
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import financial from '@/axios/default/financial'
import Pagination from '@/components/Pagination'
import publicApi from '@/axios/default/public'
import moment from 'moment'
import { newAlert } from '@/utils'

export default {
  name: 'Declare',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        monthDay: moment().subtract(1, 'months').format('YYYY-MM'),
        merName: '',
        name: '',
        idCard: '',
        levyId: ''
      },
      listData: [],
      levyBodyIdArr: []
    }
  },

  mounted() {
    this.queryLevyBodyInfos()
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      financial.list({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },
    exportList() {
      if (this.formData.levyId === '' && this.formData.merName === '') {
        this.$message.error('请先选择代征主体或商户名称')
        return
      }
      financial.exportTaxDetails({
        ...this.formData
      }, '申报明细.zip', '.zip').then(res => {
        // this.$message.success('操作成功')
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    // 时间
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
