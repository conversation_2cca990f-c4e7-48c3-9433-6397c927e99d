<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" class="demo-form-inline" size="mini">
              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增代理</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="createTime"
            label="创建时间"
          />
          <el-table-column
            prop="gradeName"
            label="级别名称"
          />
          <el-table-column
            prop="parentName"
            label="上级"
          />
          <el-table-column
            prop="createName"
            label="创建人"
          />
          <el-table-column
            prop="modifyTime"
            label="修改时间"
          />
          <el-table-column
            prop=""
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="edit(scope.row.id)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" :label-width="formLabelWidth" size="mini" class="form-style" :rules="rules">
          <el-form-item label="代理级别名称" prop="gradeName">
            <el-input v-model="dialogData.gradeName" autocomplete="off" placeholder="请输入代理级别名称" />
          </el-form-item>

          <el-form-item label="上级">
            <el-select v-model="dialogData.parentId" placeholder="请选择上级" class="auto-width">
              <el-option label="销售" :value="0" />
              <el-option v-for="(item,index) in agentGradeArr" :key="index" :label="item.gradeName" :value="item.id" />
            </el-select>
            <!--            <div style="font-size: 12px">-->
            <!--              为空时，上级为销售-->
            <!--            </div>-->
          </el-form-item>

          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogData')">保存</el-button>
              <el-button :loading="btnLoading" @click="dialogFormVisible = false">取消</el-button>
            </div>
          </el-form-item>

        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import agentGrade from '@/axios/default/agentGrade'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      dialogData: {},
      dialogFormVisible: false,
      formLabelWidth: '120px',
      listData: [],
      agentGradeArr: [],
      dialogType: '',
      title: '',
      rules: {
        gradeName: [{ required: true, message: '请填写代理级别名称', trigger: 'blur' }]
      },
      btnLoading: false
    }
  },
  mounted() {
    this.list()
    this.getAllAgentGrades()
  },
  methods: {
    add() {
      this.dialogFormVisible = true
      this.getAllAgentGrades()
      this.dialogType = 'add'
      this.title = '新增代理管理'
      this.dialogData = this.initDialogData()
    },
    // 上级
    getAllAgentGrades() {
      agentGrade.getAllAgentGrades().then(res => {
        this.agentGradeArr = res.data.data
      })
    },
    edit(id) {
      this.dialogFormVisible = true
      this.dialogType = 'edit'
      this.title = '修改代理管理'
      // 修改回显
      agentGrade.getAgentGrade({
        id: id
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogData = res.data.data
        } else {
          newAlert(this.$tips, '获取信息失败')
        }
      })
    },
    // 修改
    updateAgentGrade() {
      agentGrade.updateAgentGrade({
        id: this.dialogData.id,
        gradeName: this.dialogData.gradeName,
        parentId: this.dialogData.parentId || 0
      }).then(res => {
        this.btnLoading = false
        this.$refs.dialogData.clearValidate()
        this.$refs.dialogData.resetFields()

        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.$message.success('操作成功')
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogType == 'add') {
            this.addAgentGrade()
          } else {
            this.updateAgentGrade()
          }
        } else {
          return false
        }
      })
    },
    addAgentGrade() {
      agentGrade.addAgentGrade({
        id: this.dialogData.id,
        gradeName: this.dialogData.gradeName,
        parentId: this.dialogData.parentId || 0
      }).then(res => {
        this.btnLoading = false
        this.$refs.dialogData.clearValidate()
        this.$refs.dialogData.resetFields()

        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.$message.success('操作成功')
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    list() {
      agentGrade.list({
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    initDialogData() {
      return {
        id: '',
        gradeName: '',
        parentId: ''
      }
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }
  .dialog-scroll{
    /*overflow-y: hidden;*/
    /*height: calc(100%);*/
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 50vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
</style>
