<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="姓名" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="状态" class="form-items">
                    <el-select v-model="formData.status" placeholder="请选择" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option label="正常" value="1" />
                      <el-option label="停用" value="0" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="12" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-add" type="primary" @click="saleDownLoadData">下载文件</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" plain type="primary" @click="add">新增销售</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="loginName"
            label="登录账号"
            width="200"
          />
          <el-table-column
            prop="name"
            label="姓名"
            width="200"
          />
          <el-table-column
            prop="feeRate"
            label="费率"
            width="80"
          >
            <template slot-scope="scope">
              {{ scope.row.feeRate }}%
            </template>
          </el-table-column>
          <el-table-column
            prop="calMode"
            label="计算方式"
            width="80"
          >
            <template slot-scope="scope">
              {{ scope.row.calMode == '3' ? '外扣' :'内扣' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="balanceCycle"
            label="计算周期"
            width="100"
          >
            <template slot-scope="scope">
              {{ balanceCycleObj[scope.row.balanceCycle] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="balanceType"
            label="结算类型"
            width="100"
          >
            <template slot-scope="scope">
              {{ scope.row.balanceType == '1' ? '充值金额' :'结算金额' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="状态"
            width="80"
          >
            <template slot-scope="scope">
              {{ scope.row.status == '1' ? '正常' :'停用' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button v-if="scope.row.status == '1'" type="text" @click="edit(scope.row)">修改</el-button>
              <el-button v-if="scope.row.status == '0'" type="text" @click="updateStatus(scope.row.id,'1')">开通</el-button>
              <el-button v-else type="text" @click="updateStatus(scope.row.id,'0')">停用</el-button>
              <el-button type="text" @click="resetPassword(scope.row)">重置密码</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>
    </div>

    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="45%" class="reset-dialog">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" :label-width="formLabelWidth" size="mini" class="form-style" :rules="rules">
          <el-form-item label="登录账号" prop="loginName">
            <el-input v-model="dialogData.loginName" autocomplete="off" />
          </el-form-item>
          <el-form-item v-if="dialogType == 'add'" label="登录密码" prop="passWord">
            <el-input v-model="dialogData.passWord" autocomplete="off" show-password placeholder="请填写密码" />
          </el-form-item>
          <el-form-item label="姓      名" prop="name">
            <el-input v-model="dialogData.name" autocomplete="off" />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="dialogData.mobile" autocomplete="off" />
          </el-form-item>
          <el-form-item label="邮     箱" prop="email">
            <el-input v-model="dialogData.email" autocomplete="off" />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计算方式" prop="calMode">
                <el-select v-model="dialogData.calMode" placeholder="请选择计算方式" style="width: 150px">
                  <el-option label="外扣" value="3" />
                  <el-option label="内扣" value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="费率" prop="feeRate">
                <el-input v-model="dialogData.feeRate" style="width: 150px" class="spacing">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="结算周期" prop="balanceCycle">
                <el-select v-model="dialogData.balanceCycle" placeholder="请选择结算周期" style="width: 150px">
                  <el-option label="周结" value="1" />
                  <el-option label="月结" value="2" />
                  <el-option label="季结" value="3" />
                  <el-option label="年结" value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结算类型" prop="balanceType">
                <el-select v-model="dialogData.balanceType" placeholder="请选择结算类型" style="width: 150px" class="spacing">
                  <el-option label="充值金额结算" value="1" />
                  <el-option label="结算金额结算" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!--          <el-form-item label="备注">-->
          <!--            <el-input v-model="dialogData.note" type="textarea" :rows="2" placeholder="请输入内容" />-->
          <!--          </el-form-item>-->
          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" @click="submit('dialogData')">保存</el-button>
              <el-button @click="dialogFormVisible = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import saleInfo from '@/axios/default/saleInfo'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        name: '',
        status: ''
      },
      dialogData: this.initDialogData(),
      dialogFormVisible: false,
      formLabelWidth: '100px',
      listData: [],
      balanceCycleObj: { 1: '周', 2: '月', 3: '季', 4: '年' },
      // 弹框部分
      title: '',
      dialogType: '',
      rules: {
        loginName: [{ required: true, message: '请填写登陆账号', trigger: 'blur' }],
        passWord: [{ required: true, message: '请填写登陆密码', trigger: 'blur' }],
        name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
        mobile: [{ required: true, message: '请填写手机号', trigger: 'blur' }],
        email: [{ required: true, message: '请填写邮箱', trigger: 'blur' }],
        calMode: [{ required: true, message: '请填写计算方式', trigger: 'change' }],
        feeRate: [{ required: true, message: '请填写费率', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
              if (result) {
                callback()
              } else {
                callback('请输入正确的数字')
              }
            }, trigger: ['blur', 'change'] }],
        balanceCycle: [{ required: true, message: '请填写结算周期', trigger: 'change' }],
        balanceType: [{ required: true, message: '请填写结算类型', trigger: 'change' }]
      }
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogType == 'add') {
            this.addSaleInfo()
          } else {
            this.updateSaleInfo()
          }
        } else {
          return false
        }
      })
    },
    // 添加销售
    add() {
      this.title = '新增销售信息'
      this.dialogFormVisible = true
      this.dialogType = 'add'
      this.dialogData = this.initDialogData()
      this.$nextTick(function() {
        this.resetForm('dialogData')
      })
    },
    addSaleInfo() {
      saleInfo.addSaleInfo({
        ...this.dialogData
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 修改销售
    edit(data) {
      this.title = '修改销售信息'
      this.dialogType = 'edit'
      this.dialogFormVisible = true
      this.dialogData = this.initDialogData()
      saleInfo.getSaleInfo({
        id: data.id
      }).then(res => {
        if (res.data.code == '0000') {
          this.resetForm('dialogData')
          this.dialogData = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    updateSaleInfo() {
      saleInfo.updateSaleInfo({
        ...this.dialogData
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogFormVisible = false
          this.resetForm('dialogData')
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 开通停用
    updateStatus(id, status) {
      saleInfo.updateStatus({
        id: id,
        status
      }).then(res => {
        if (res.data.code == '0000') {
          this.$message({ type: 'success', message: '操作成功' })
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },

    // 重置密码
    resetPassword(options) {
      this.$confirm('此操作将重置该销售登录密码, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        saleInfo.updateSalePsd({
          id: options.id
        }).then(res => {
          if (res.data.code === '0000') {
            this.$message({ type: 'success', message: '操作成功' })
            this.list()
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    initDialogData() {
      return {
        loginName: '',
        passWord: '',
        name: '',
        mobile: '',
        email: '',
        calMode: '',
        feeRate: '',
        balanceCycle: '',
        balanceType: ''
      }
    },
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      saleInfo.list({
        ...this.formData,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    saleDownLoadData() {
      saleInfo.saleDownLoadData({
        ...this.formData
      }, `销售列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {
        this.$message.success('操作成功')
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.auto-width{
  width: 100%;
}
.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
}

.form-row{
  display: flex;
}
.form-row-style{
  margin: 0 20px;
  .title{
    width: 80px;
    text-align: right;
  }
  .spacing{
    margin: 0 5px;
    width: 75px;
  }
}
.dialog-scroll{
  overflow-y: auto;
  height: calc(100%);
}

.reset-dialog{
  /deep/ .el-dialog{
    height: 80vh;
    overflow: hidden;
    .el-dialog__body{
      height: calc(100% - 54px);
    }
  }
  overflow: hidden;
}
.form-style{
  padding-right: 20px;
}
</style>
