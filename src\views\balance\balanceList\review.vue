<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">

        <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
          <el-row :gutter="20">
            <!--                <el-col :lg="10" :xs="24">-->
            <!--                  <el-form-item label="订单时间" class="form-items"> <el-date-picker-->
            <!--                    v-model="pickTime"-->
            <!--                    class="auto-width"-->
            <!--                    type="daterange"-->
            <!--                    align="right"-->
            <!--                    unlink-panels-->
            <!--                    value-format="yyyy-MM-dd"-->
            <!--                    range-separator="-"-->
            <!--                    start-placeholder="开始时间"-->
            <!--                    end-placeholder="结束时间"-->
            <!--                    :picker-options="pickerOptions"-->
            <!--                  />-->
            <!--                  </el-form-item>-->
            <!--                </el-col>-->
            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间起" class="form-items" prop="createTimeFrom">
                <el-date-picker
                  v-model="formData.createTimeFrom"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="订单时间止" class="form-items" prop="createTimeTo">
                <el-date-picker
                  v-model="formData.createTimeTo"
                  class="auto-width"
                  type="datetime"
                  :clearable="false"
                  :editable="false"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>

            <el-col :lg="8" :xs="24">
              <el-form-item label="企业名称" class="form-items">
                <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :lg="24" :xs="24">
              <el-form-item class="form-items" style="text-align:right">
                <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                <el-button icon="el-icon-search" type="primary" @click="downTradeInfo">下载文件</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>


        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="batchNum"
            label="商户批次号"
            show-overflow-tooltip
          />
          <el-table-column
            prop="operateState"
            label="审核状态"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ operateState[scope.row.operateState] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="settMoney"
            label="结算金额"
          />
          <el-table-column
            prop="feeMoney"
            label="服务费"
          />
          <el-table-column
            prop="settNum"
            label="结算人数"
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            label="订单创建时间"
            show-overflow-tooltip
          />
          <el-table-column
            prop="orderCompTime"
            label="交易完成时间"
            show-overflow-tooltip
          />

          <el-table-column
            prop="address"
            label="操作"
            width="120"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="viewInfo(scope.row.id)">查看</el-button>
              <el-button type="text" @click="trialAudit(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>
    </div>

    <!--        充值初审-->
    <el-dialog title="结算复审" :visible.sync="dialogaudit" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogtrialData" :model="dialogtrialData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="商户批次号">
            <el-input v-model="dialogtrialData.batchNum" disabled />
          </el-form-item>
          <el-form-item label="结算金额">
            <el-input v-model="dialogtrialData.settMoney" disabled />
          </el-form-item>
          <el-form-item label="服务费">
            <el-input v-model="dialogtrialData.freeMoney" disabled />
          </el-form-item>
          <el-form-item label="结算人数">
            <el-input v-model="dialogtrialData.settNum" disabled />
          </el-form-item>
          <el-form-item label="初审备注">
            <el-input v-model="dialogtrialData.auditReason" disabled />
          </el-form-item>
          <el-form-item label="备注" prop="auditRemark">
            <el-input v-model="dialogtrialData.auditRemark" type="textarea" :rows="2" autocomplete="off" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogtrialData','1')">通过</el-button>
              <el-button :loading="btnLoading" @click="submit('dialogtrialData','2')">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import tradeService from '@/axios/default/tradeService'
import Pagination from '@/components/Pagination'
import { parseTime, newAlert } from '@/utils'
import moment from 'moment'
export default {
  name: 'Audit',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        'merName': '',
        'operateState': '3'
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogaudit: false,
      dialogtrialData: { auditRemark: '' },
      rules: {
        auditRemark: [{ required: true, message: '请输入初审备注', trigger: 'blur' }]
      },
      dialogTableData: [],
      operateState: {
        1: '初审中',
        2: '初审拒绝',
        3: '复审',
        4: '复审通过',
        5: '复审拒绝',
        6: '系统通过',
        7: '批次风控审核中',
        8: '批次风控审核通过',
        9: '批次风控审核拒绝',
        10: '单笔风控审核中',
        11: '单笔风控审核通过',
        12: '单笔风控审核拒绝'
      },
      btnLoading: false
    }
  },
  mounted() {
    this.queryTradeOrderInfoService()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryTradeOrderInfoService()
    },

    // 列表数据
    queryTradeOrderInfoService() {
      tradeService.queryTradeOrderInfoService({
        'request': {
          'pageSize': '',
          'pageNum': ''
        },
        'tradeInfo': {
          'createTimeFrom': moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss'),
          'createTimeTo': moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss'),
          'merName': this.formData.merName,
          'operateState': this.formData.operateState
        }
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    // 打开审核框
    trialAudit(data) {
      console.log(data)
      tradeService.queryTradeAuditRecord({
        tradeId: data.id
      }).then(res => {
        if (res.data.code == '0000') {
          this.dialogaudit = true
          this.dialogtrialData.id = data.id
          this.dialogtrialData.batchNum = data.batchNum
          this.dialogtrialData.settMoney = data.settMoney
          this.dialogtrialData.settNum = data.settNum
          this.dialogtrialData.freeMoney = data.feeMoney
          this.dialogtrialData.auditReason = res.data.data[0].auditReason
          this.dialogtrialData.auditRemark = ''
        } else {
          newAlert(this.$tips, res.data.message)
        }

        // this.dialogaudit = true
        // this.dialogauditData.id = data.id
        // this.dialogauditData.batchNum = data.batchNum
        // this.dialogauditData.settMoney = data.settMoney
        // this.dialogauditData.platFree = data.platFree
        // this.dialogauditData.auditRemark = ''
        // this.dialogauditData.auditRecord = data.auditRecord[0].auditReason
      })
    },
    // 提交审核
    submit(formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          tradeService.operateAuditTradeInfo({
            'id': this.dialogtrialData.id,
            'auditRemark': this.dialogtrialData.auditRemark,
            'btnType': type
          }).then(res => {
            this.btnLoading = false
            this.$refs[formName].clearValidate()
            this.$refs[formName].resetFields()
            if (res.data.code == '0000') {
              this.dialogaudit = false
              this.$message({ type: 'success', message: '操作成功' })
              this.queryTradeOrderInfoService()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          return false
        }
      })
    },
    viewInfo(id) {
      this.$router.push({
        path: '/balance/detail',
        query: {
          id
        }
      })
    },
    // 分页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryTradeOrderInfoService()
    },
    downTradeInfo() {
      tradeService.downTradeInfo({
        'createTimeFrom': moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss'),
        'createTimeTo': moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss'),
        'operateState': this.formData.operateState
      }, `结算列表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    }
  }
}
</script>

<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .money-title{
    padding: 10px;
  }
</style>

