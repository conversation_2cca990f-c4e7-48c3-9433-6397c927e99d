<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="12" :xs="24">
                  <el-form-item label="发布时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="datetimerange"
                      align="right"
                      unlink-panels
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="发布企业" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-for="(item,index) in levyBodyOpt" :key="index" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="项目名称" class="form-items">
                    <el-input v-model="formData.name" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="18">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantName"
            min-width="250"
            fixed
            label="发布企业"
          />
          <el-table-column
            prop="createTime"
            width="150"
            label="开始时间"
          />
          <el-table-column
            prop="name"
            width="250"
            label="项目名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="agentName"
            label="销售名称"
            show-overflow-tooltip
          />
          <el-table-column
            prop="levyName"
            width="250"
            label="服务主体"
          />
          <el-table-column
            prop="finishTime"
            width="150"
            label="项目完成时间"
          />
          <el-table-column
            prop="projectStatus"
            width="100"
            label="项目状态"
          >
            <template slot-scope="scope">
              {{ scope.row.status == '0' ? '结束' : (scope.row.status == '1' ? '进行中':scope.row.status == '2' ? '待审核':'编辑') }}
            </template>
          </el-table-column>
          <el-table-column
            prop="merName"
            label="操作"
            fixed="right"
            min-width="120"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="openboxtask({data:scope.row,title:'审核项目'}, true)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
    <!--    弹框-->
    <el-dialog :title="title" :visible.sync="dialogShow" width="50%" class="reset-dialog reset-dialog-small" @closed="dialogClosed">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" size="mini" label-width="120px" class="form-style" :rules="rules">
          <div>
            <el-form-item label="发布企业" prop="merchantId">
              <el-select
                v-model="dialogData.merchantId"
                class="auto-width"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="merAllList"
                :loading="loading"
                :disabled="handleStatus!=0"
                @change="getLevyBodyId"
              >
                <el-option v-for="item in merAllListArr" :key="item.id" :label="item.merName" :value="item.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="项目截止时间" prop="pickTime">
              <el-date-picker
                v-model="dialogData.pickTime"
                class="auto-width"
                type="daterange"
                align="right"
                unlink-panels
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :picker-options="pickerOptions"
                :disabled="handleStatus!=0"
                @change="diaresetTime"
              />
            </el-form-item>
            <el-form-item label="服务主体" prop="levyId">
              <el-select v-model="dialogData.levyId" placeholder="请选择" class="auto-width" clearable :disabled="handleStatus!=0" @change="findTypeBymerIdLevyId">
                <el-option v-for="(item) in levyBodyArr" :key="item.id" :label="item.NAME" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="dialogData.name" placeholder="请输入内容" clearable :disabled="handleStatus!=0" />
            </el-form-item>
            <el-form-item label="经验要求" prop="experience">
              <el-select v-model="dialogData.experience" placeholder="请选择" class="auto-width" clearable :disabled="handleStatus!=0">
                <el-option label="不限" value="0" />
                <el-option label="1-3年" value="1" />
                <el-option label="3-5年" value="2" />
                <el-option label="5-10年" value="3" />
                <el-option label="10年以上" value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="结算标准" prop="salaryRange">
              <el-input v-model="dialogData.salaryRange" placeholder="请输入内容" clearable :disabled="handleStatus!=0" />
            </el-form-item>

            <template>
              <el-form-item v-if="dialogData.levyId !== 14" label="项目类型" prop="type2">
                <el-select v-model="dialogData.type2" placeholder="请选择" class="auto-width" clearable :disabled="handleStatus!=0" @change="findTypeBymerIdLevyId">
                  <el-option v-for="(item) in invoiceArr" :key="item.invoiceTypeId" :label="item.type_name" :value="item.invoiceTypeId" />
                </el-select>
              </el-form-item>
              <el-form-item v-else label="项目类型" prop="category">
                <el-select v-model="dialogData.category" placeholder="请选择" class="auto-width" clearable :disabled="handleStatus!=0" @change="findTypeBymerIdLevyId">
                  <el-option v-for="(item) in invoiceArr" :key="item.object_id" :label="item.name" :value="item.object_id" />
                </el-select>
              </el-form-item>
            </template>

            <el-form-item label="项目描述" prop="describe">
              <el-input v-model="dialogData.describe" type="textarea" :row="2" placeholder="请输入内容" clearable :disabled="handleStatus!=0" />
            </el-form-item>
          </div>
          <template v-if="handleStatus==2 && isChecked">
            <el-form-item label="审核意见" :prop="handleStatus==2?'auditOpinion':''">
              <el-input v-model="dialogData.auditOpinion" type="textarea" :row="2" placeholder="请输入内容" clearable />
            </el-form-item>
            <el-form-item label="" class="form-items">
              <el-button :loading="btnLoading" type="primary" @click="submitChoose('dialogData', 1)">通过</el-button>
              <el-button :loading="btnLoading" @click="submitChoose('dialogData', 0)">不通过</el-button>
            </el-form-item>
          </template>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import proManage from '@/axios/default/proManage'
import JFJ from '@/axios/JFJ'
import publics from '@/axios/default/public'
import levyBody from '@/axios/default/levyBody'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'
import moment from 'moment'
export default {
  name: 'Check',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: ['', ''],
      formData: {
        levyId: '',
        merchantName: '',
        name: '',
        projectStatus: '2',
        createTimeFrom: '',
        createTimeTo: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      listData: [],
      title: '',
      dialogShow: false,
      dialogData: {},
      merAllListArr: [],
      levyBodyArr: [],
      invoiceArr: [],
      loading: false,
      btnLoading: false,
      rules: {
        merchantId: [{ required: true, message: '请填写发布企业', trigger: 'blur' }],
        pickTime: [{ required: true, message: '请填写项目截止时间', trigger: 'blur' }],
        levyId: [{ required: true, message: '请填写服务主体', trigger: 'blur' }],
        name: [{ required: true, message: '请填写项目名称', trigger: 'blur' }],
        experience: [{ required: true, message: '请填写经验要求', trigger: 'blur' }],
        salaryRange: [{ required: true, message: '请填写薪酬', trigger: 'blur' }],
        type2: [{ required: true, message: '请填写项目类型', trigger: 'blur' }],
        describe: [{ required: true, message: '请填写项目描述', trigger: 'blur' }],
        auditOpinion: [{ required: true, message: '请填写审核意见', trigger: 'blur' }]
      },

      handleStatus: 0, // 0-编辑, 1-查看, 2-审核 3-提交审核
      isChecked: false, // 是否为待审核状态
      levyBodyOpt: [] // 所有的代征主体
    }
  },
  created() {
    this.list()
  },
  mounted() {
    this.initLevyBody()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    list() {
      this.formData.createTimeFrom = this.pickTime[0] ? moment(this.pickTime[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      this.formData.createTimeTo = this.pickTime[1] ? moment(this.pickTime[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      proManage.findProjectInfoList({
        ...this.formData,
        id: this.$route.query.projectId || '',
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },

    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    diaresetTime(val) {
      console.log(val)
      val = val || ['', '']
      this.dialogData.createTime = val[0]
      this.dialogData.endTime = val[1]
    },
    initDialogData() {
      return {
        pickTime: '',
        merchantId: '',
        createTime: '',
        endTime: '',
        name: '',
        levyId: '',
        salaryRange: '',
        experience: '',
        type2: '',
        describe: '',
        status: 3,
        auditOpinion: '' // 审核意见
      }
    },

    openboxtask(scope, isChecked = false) {
      const { title, data } = scope
      this.title = title
      this.$nextTick(function() {
        if ('data' in scope) {
          this.dialogData = Object.assign({}, data)
          this.handleStatus = 2 // 审核
          this.isChecked = isChecked
          this.merAllList(this.dialogData.merchantName)
          this.getLevyBodyId(this.dialogData.merchantId)
          this.findTypeBymerIdLevyId()
          this.$set(this.dialogData, 'pickTime', [data.createTime, data.endTime])

          this.dialogShow = true
        }
      })
    },

    dialogClosed() {
      this.isChecked = false
    },

    submitChoose(formName, auditChoose) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          const { id, auditOpinion } = this.dialogData
          proManage.auditProject({
            id,
            auditChoose,
            auditOpinion
          }).then(res => {
            this.btnLoading = false
            this.$refs[formName].clearValidate()
            this.$refs[formName].resetFields()
            if (res.data.code == '0000') {
              this.dialogShow = false
              this.isChecked = false
              this.$message.success('操作成功')
              this.list()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    initLevyBody() {
      levyBody.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyOpt = res.data.data
          if (res.data.data.length > 1) {
            this.levyBodyOpt.unshift({ name: '请选择', id: '' })
          } else {
            this.formData.levyId = this.levyBodyOpt[0].id
          }
        }
        this.list()
      })
    },
    async merAllList(query = '') {
      this.loading = true
      publics.merAllList({
        merName: query
      }).then(res => {
        this.loading = false
        this.merAllListArr = res.data.data
      })
    },
    async getLevyBodyId(id) {
      publics.initAddRechInfo({
        'queryType': '2',
        'merId': id
      }).then(res => {
        this.levyBodyArr = res.data.data
      })
    },
    // 发票类目
    async findTypeBymerIdLevyId() {
      if (this.dialogData.levyId !== 14) {
        this.HTTypeOptions()
      } else {
        this.JFJTypeOptions()
      }
    },
    HTTypeOptions() {
      proManage.findTypeBymerIdLevyId({
        merId: this.dialogData.merchantId,
        levyId: this.dialogData.levyId
      }).then(res => {
        this.invoiceArr = res.data.data
      })
    },
    JFJTypeOptions() {
      JFJ.category({ page_size: 100 }).then(res => {
        if (res._code === 0) {
          this.invoiceArr = res.results
        } else {
          this.$message.error(res._message)
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

  .table-header{
    display: flex;
    justify-content: flex-end;
    .file-upload{
      margin-right: 5px;
    }
  }
</style>
