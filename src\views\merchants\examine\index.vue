<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="创建时间起" class="form-items">
                    <el-date-picker
                      v-model="formData.authTimeFrom"
                      class="auto-width"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="创建时间止" class="form-items">
                    <el-date-picker
                      v-model="formData.authTimeTo"
                      class="auto-width"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="所属销售" class="form-items">
                    <el-input v-model="formData.saleName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="authTime"
            label="创建时间"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="approveNo"
            min-width="150"
            label="申请编号"
          />
          <el-table-column
            prop="merName"
            min-width="240"
            label="企业名称"
          />
          <el-table-column
            prop="merNo"
            min-width="160"
            label="商户编号"
          />
          <el-table-column
            prop="saleName"
            min-width="80"
            label="所属销售"
          />
          <el-table-column
            prop="agentName"
            min-width="80"
            label="所属代理"
          />
          <el-table-column
            min-width="90"
            label="当前状态"
          >
            <template slot-scope="scope">
              {{ liststatus[scope.row.status] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="postName"
            min-width="100"
            label="联系人"
          />
          <el-table-column
            prop="postMobile"
            min-width="100"
            label="联系电话"
          />
          <el-table-column
            prop="contractPostAddr"
            min-width="260"
            label="地址"
          />
          <el-table-column
            prop="address"
            label="操作"
            min-width="180"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="viewInfo(scope.row.id)">查看</el-button>
              <el-button type="text" :disabled="!examineBtn(scope.row)" @click="openbox(scope.row)">审批</el-button>
              <el-button type="text" :disabled="!examineRecordBtn(scope.row)" @click="openRecorde(scope.row)">审批记录</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>

    </div>

    <!--审批弹框-->
    <el-dialog title="商户审批" :visible.sync="examineShow" width="40%">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="examine" :model="examine" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称">
            <el-input v-model="examine.merName" disabled />
          </el-form-item>
          <el-form-item label="审批意见" prop="authDesc">
            <el-input v-model="examine.authDesc" type="textarea" :rows="2" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="examine.remark" type="textarea" :rows="2" />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="merchantExamine('examine','1')">通过</el-button>
              <el-button :loading="btnLoading" @click="merchantExamine('examine','0')">不通过</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

    <!--审批记录弹框-->
    <el-dialog title="审批记录" :visible.sync="examineRecodeShow" width="40%">
      <el-scrollbar class="dialog-scroll">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in examineRecode"
            :key="index"
            :hide-timestamp="true"
            :type="item.authStatus==1?'success':'danger'"
          >
            <div class="examineRecodeItem">
              <div>申请编号：{{item.approveNo}}</div>
              <div>审批人：{{item.authAdmin}}</div>
              <div>审批状态：<span :style="{color: item.color}">{{item.authStatusDesc}}</span></div>
              <div>审批意见：{{item.authDesc}}</div>
              <div>审批备注：{{item.remark}}</div>
              <div>审批时间：{{item.createTime}}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import merchants from '@/axios/default/merchants'
import Pagination from '@/components/Pagination'
import db from '@/utils/localstorage'
import { newAlert } from '@/utils'

export default {
  name: 'Examine',
  components: {
    Pagination
  },
  data() {
    return {
      roleId: [], // 角色ID 1-超管， 117-风控， 31-运营/此值为逗号隔开 如： '1,31,117' 等
      liststatus: { 0: '编辑', 1: '初审中', 2: '复审中', 3: '正常', 4: '预开', 5: '停用', 6: '渠道审批中', 7: '风控审批中', 8: '运营审批中' },
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      btnLoading: false,
      total: 0,
      formData: {
        authTimeFrom: '',
        authTimeTo: '',
        saleName: '',
        merName: ''
      },
      listData: [],

      examineShow: false,
      examine: {
        approveNo: '',
        authStep: '',
        authStatus: '',
        authDesc: '',
        remark: ''
      },
      rules: {
        authDesc: { required: true, message: '请填写审批意见', trigger: 'blur' }
      },
      examineRecodeShow: false,
      examineRecode: []
    }
  },
  created() {
    // 获取角色ID
    const user = db.get('USER', '')
    if (user.hasOwnProperty('roleId')) {
      this.roleId = user.roleId.split(',')
    }
  },
  mounted() {
    this.auth_list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.auth_list()
    },
    auth_list() {
      merchants.auth_list({
        ...this.formData,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
      })
    },

    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.auth_list()
    },

    examineBtn(args) {
      // 审批按钮是否可用逻辑处理
      if (args.isAuth === '1') {
        // 审批中
        if (args.status === '7') {
          // 风控角色审批
          if (this.roleId.includes('1') || this.roleId.includes('117')) {
            return true
          } else {
            return false
          }
        } else if (args.status === '8') {
          // 运营角色审批
          if (this.roleId.includes('1') || this.roleId.includes('31')) {
            return true
          } else {
            return false
          }
        }
      } else {
        // 审批结束
        return false
      }
    },

    examineRecordBtn(args) {
      // 审批记录按钮是否可用逻辑处理
      if (args.isAuth === '1') {
        // 审批中
        if (args.status === '7') {
          // 审批中并不存在审核记录时不可查看
          return false
        } else if (args.status === '8') {
          if (this.roleId.includes('1') || this.roleId.includes('117')) {
            // 审批中但已存在风控角色审核记录时 超管和风控 可以查看
            return true
          } else {
            // 审批中但已存在风控角色审核记录时 非超管和风控 不可以查看
            return false
          }
        }
      } else {
        // 审批结束
        if (this.roleId.includes('1') || this.roleId.includes('31') || this.roleId.includes('117')) {
          // 审批结束可审批的角色均可以查看审批记录
          return true
        }
      }
    },

    // 打开审批框
    openbox(data) {
      this.examineShow = true
      this.examine = Object.assign(this.examine, data)
    },

    viewInfo(id) {
      this.$store.commit('merchants/setCreateMerchantStep', 1)
      this.$router.push({
        path: '/merchants/actions',
        query: {
          page: 'view',
          id
        }
      })
    },

    // 审批
    merchantExamine(formName, authStatus) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          const { approveNo, authStep, authDesc, remark } = this.examine

          merchants.merchantAuth({
            approveNo,
            authStep: authStep || '',
            authStatus,
            authDesc,
            remark
          }).then(res => {
            if (res.data.code === '0000') {
              this.examineShow = false
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.auth_list()
              this.$refs[formName].clearValidate()
              this.$refs[formName].resetFields()
            } else {
              newAlert(this.$tips, res.data.message)
            }
            this.btnLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    openRecorde(args) {
      merchants.getAuthList({
        id: args.id
      }).then(res => {
        if (res.data.code === '0000') {
          const { merchantAuthRelationList } = res.data.data
          merchantAuthRelationList.forEach(item => {
            if (item.authStatus === '1') {
              item.color = '#85ce61'
            } else {
              item.color = '#f78989'
            }
          })
          this.examineRecode = merchantAuthRelationList
          this.examineRecodeShow = true
        } else {
          newAlert(this.$tips, res.data.message)
        }
        this.btnLoading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }
  .pagination{
    text-align: right;
  }

  .examineRecodeItem {
    line-height: 24px;
  }
</style>
