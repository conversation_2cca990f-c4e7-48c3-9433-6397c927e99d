<template>
  <div class="content-main">

    <div class="panel-box">
      <el-row :gutter="100">
        <el-col :span="6">
          <div class="grid-content bg-item-1">
            <div class="card-name">
              <span>本月充值</span>
            </div>
            <div class="grid-content-text">
              <div class="card-num">
                <countTo
                  class="countTo totalVisit"
                  :decimals="2"
                  :start-val="0"
                  :end-val="this.merTradeTotal.rechargeSum"
                  :duration="1500"
                />
                元
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content bg-item-2">
            <div class="card-name">
              <span>本月结算</span>
            </div>
            <div class="grid-content-text">
              <div class="card-num">
                <countTo
                  class="countTo totalVisit"
                  :decimals="2"
                  :start-val="0"
                  :end-val="this.merTradeTotal.settleSum"
                  :duration="1500"
                />
                元
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content bg-item-3">
            <div class="card-name">
              <span>本月开票</span>
            </div>
            <div>
              <div class="card-num">
                <countTo
                  class="countTo totalVisit"
                  :decimals="decimals"
                  :start-val="0"
                  :end-val="this.merTradeTotal.invoicedSum"
                  :duration="1500"
                />
                元
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content bg-item-4">
            <div class="card-name">
              <span>本月预开</span>
              <div />
            </div>
            <div>
              <div class="card-num">
                <countTo
                  class="countTo totalVisit"
                  :decimals="decimals"
                  :start-val="0"
                  :end-val="this.merTradeTotal.preInvoiceSum"
                  :duration="1500"
                />
                元
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

    </div>
    <div ref="visitcountchart">
      <el-card class="list-card" style="height: calc(50%);width: calc(100%);">
        <el-form :inline="true" class="demo-form-inline">

          <el-col :lg="8" :xs="24">
            <!-- <el-form-item label="代征主体" class="form-items">
                  <el-select v-model="type" @change="changeQuery()" placeholder="请选择" class="auto-width">
                    <el-option label="请选择" value="" />
                    <el-option label="海南人杰科技有限公司" value=6 />
                    <el-option label="湖北宝风信息科技有限公司" value=7 />
                  </el-select>
                </el-form-item> -->
            <el-form-item label="代征主体" class="form-items">
              <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width" @change="changeQuery()">
                <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                <el-option v-for="item in levyBodyIdArr" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
        <div id="main" style="width: calc(90%); height: calc(80%);">

          <div id="visit-count-chart" style="width: calc(33%); height: 400px;display: inline-block;" />
          <div id="visit-count-chart1" style="width: calc(33%); height: 400px;display: inline-block;" />
          <div id="visit-count-chart2" style="width: calc(33%); height: 400px;display: inline-block;" />
        </div>
      </el-card>
      <el-card class="list-card" style="height: calc(50%);width: calc(100%);">
        <div id="main1" style="width: calc(100%); height: calc(40%);">
          <div id="visit-count-chart3" style="width: calc(100%); display: inline-block; " />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { formatNumber, formatLongMoney, newAlert } from '@/utils'
import countTo from 'vue-count-to'
import resize from '@/components/Charts/mixins/resize'
import index from '@/axios/default/statistics/index'
import merList from '@/axios/default/financial/index'
import Pagination from '@/components/Pagination'
import publicApi from '@/axios/default/public'

import moment from 'moment'

export default {
  name: 'Dashboard',
  components: { countTo, Pagination },
  mixins: [resize],
  data() {
    return {
      agentId: '',
      height1: 0,
      homeDataList: '',
      totalAmount: 0.00,
      merTradeTotal: '',
      data6: [],
      listData: [],
      dateMonth: moment().subtract(1, 'months').format('YYYY-MM'),
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      type: '',
      time: '2',
      formData: {
        levyId: ''
      },
      levyBodyIdArr: [],

      activeName: '结算分布',
      switchType: 1,
      chart: '',
      decimals: 2,
      createTimeFrom: '',
      createTimeTo: '',
      getWaitDealtList: {},
      // pickTime: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())],
      pickTime: [],
      pickerOptionsDay: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now()
        // }
      },
      pickerOptionsMonth: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now()
        // },
        shortcuts: [{
          text: '当月至今',
          onClick(picker) {
            const end = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
            const start = new Date(new Date().getFullYear(), parseInt(new Date().getMonth()), 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 3)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  computed: {},
  mounted() {
    this.queryMerTradePieChart(this.formData.levyId)
    this.queryMerTradePieChart3()
    // this.queryMerList()
    this.queryMerTradeTotal()
    this.height = this.$el.getBoundingClientRect().height
    this.queryLevyBodyInfos()
  },

  methods: {
    resetTime(val) {
      this.pickTime = val
    },
    initTime(type) {
    },
    handleClick(tab, event) {

    },
    changeQuery() {
      console.log(this.formData.levyId)
      this.queryMerTradePieChart(this.formData.levyId)
    },
    timeChangeQuery() {

    },
    clickQuery() {

    },
    clickReturn() {
      this.$router.go(0)
    },
    queryMerTradePieChart2() {
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
            this.flag = false
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    queryMerTradePieChart(levyId) {
      index.queryInvoiceTotal({
        levyId
      }).then(res => {
        if (res.data.code === '0000') {
          const data1 = res.data.data.data0
          const data2 = res.data.data.data1
          const data3 = res.data.data.data2
          const data4 = res.data.data.data3
          this.merTradeTotal = res.data.data

          const option = {
            title: {
              text: '已充值金额申请开票情况', // 主标题文本
              left: 'center', // 标题水平位置
              top: '90%', // top middle bottom

              textStyle: { // 主标题样式
                color: 'black'
              }
            },
            tooltip: {
              trigger: 'item'
            },
            legend: {
              top: '5%',
              left: 'center'
            },
            color: ['#48e1ab', '#799dfd'],
            series: [
              {
                name: '已充值金额申请开票情况',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: 40,
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: [
                ]
              }
            ]
          }

          const option1 = {
            title: {
              text: '已申请发票开具情况', // 主标题文本
              left: 'center', // 标题水平位置
              top: '90%', // top middle bottom
              textStyle: { // 主标题样式
                color: 'black'
              }
            },
            tooltip: {
              trigger: 'item'
            },
            legend: {
              top: '5%',
              left: 'center'
            },
            color: ['#f98db1', '#f2c32b', '#49b2c8'],

            series: [
              {
                name: '已申请发票开具情况',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: 40,
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: [
                ]
              }
            ]
          }

          const option2 = {
            title: {
              text: '预开票回款情况', // 主标题文本
              left: 'center', // 标题水平位置
              top: '90%', // top middle bottom
              textStyle: { // 主标题样式
                color: 'black'
              }
            },
            tooltip: {
              trigger: 'item'
            },
            legend: {
              top: '5%',
              left: 'center'
            },
            color: ['#a3d538', '#f9cb3a'],

            series: [
              {
                name: '预开票回款情况',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: 40,
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: []
              }
            ]
          }

          const option3 = {
            title: {
              text: '近三月充值结算开票统计', // 主标题文本
              left: 'center', // 标题水平位置
              top: 'bottom', // top middle bottom
              textStyle: { // 主标题样式
                color: 'black'
              }
            },
            legend: {},
            tooltip: {},
            dataset: {
              source: []
            },
            xAxis: { type: 'category' },
            yAxis: {},
            color: ['#fdafbd', '#79d2fe', '#ffb73c'],

            series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
          }

          option3.dataset.source = data1
          option2.series[0].data = data2
          option1.series[0].data = data3
          option.series[0].data = data4

          const height = this.$refs.visitcountchart.getBoundingClientRect().height
          if (this.height1 == '') {
            this.height1 = height
          }
          const dom = document.getElementById('visit-count-chart')
          const dom1 = document.getElementById('visit-count-chart1')
          const dom2 = document.getElementById('visit-count-chart2')
          const dom3 = document.getElementById('visit-count-chart3')

          dom.style.height = this.height1 + 'px'
          dom1.style.height = this.height1 + 'px'
          dom2.style.height = this.height1 + 'px'
          dom3.style.height = this.height1 + 'px'

          this.chart = echarts.init(dom)
          this.chart.setOption(option)
          this.chart1 = echarts.init(dom1)
          this.chart1.setOption(option1)
          this.chart2 = echarts.init(dom2)
          this.chart2.setOption(option2)
          this.chart3 = echarts.init(dom3)
          this.chart3.setOption(option3)
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    queryMerTradePieChart3() {
    },
    queryMerList() {
      merList.queryMerList({
        'agentId': this.agentId,
        'dataType': this.time,
        'levyId': this.type,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum,
        'createTimeFrom': this.createTimeFrom,
        'createTimeTo': this.createTimeTo
      })
        .then(res => {
          if (res.data.code === '0000') {
            res.data.data.rows.forEach(item => {
              item.money = formatNumber(item.money)
            })
            this.listData = res.data.data.rows
            this.total = res.data.data.total
          }
        })
    },
    queryMerTradeTotal() {
    },

    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      // this.queryMerList()
    }
  }

}
</script>

  <style lang="scss" scoped>
  .demo-form-inline {
    display: flex;
    align-items: center;
  }

  .content-main {
    display: flex;
    flex-flow: column nowrap;
    //overflow: hidden;
    overflow-x: scroll;
    overflow-y: scroll;
  }

  .tab_wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;

  }

  .panel-box {
    margin-bottom: 20px;
  }

  .grid-content {
    overflow: hidden;
    border-radius: 4px;
    height: 125px;
    box-sizing: border-box;
    padding: 20px 20px;
    color: #ffffff;
    display: flex;
    flex-flow: column nowrap;

    .grid-content-text {
      display: flex;
      flex-flow: column nowrap;
    }

    .card-name {
      padding-bottom: 6px;
      display: flex;
      align-items: center;
      font-weight: 600;
      justify-content: space-between;
      font-size: 25px;
    }

    .view-more {
      color: #fff;
    }

    .card-title {
      text-align: right;
    }

    .card-num {
      font-size: 20px;
      font-weight: 700;
      color: #ffffff;
      margin-top: 10px;
      line-height: 20px;

      .countTo {
        font-size: 20px;
        font-weight: bold;
        color: #FFFFFF;
      }
    }

    .card-des {
      font-size: 14px;
    }
  }

  .bg-item-1 {
    background: linear-gradient(180deg, #0fb97b 0%, #099cf1 100%);
  }

  .bg-item-2 {
    background: linear-gradient(180deg, #ae7fc2 0%, #BB81F6 100%);
  }

  .bg-item-3 {
    background: linear-gradient(180deg, #25b1d8 0%, #6d77ec 100%);
  }

  .bg-item-4 {
    background: linear-gradient(180deg, #d15e5e85 0%, #7e7e72a1 100%);
  }

  .content-bottom {
    display: flex;
    justify-content: space-between;
  }

  .table-name {
    font-size: 17px;
    font-weight: 600;
    color: #181F2D;
  }

  .table-name-pd {
    padding-bottom: 15px;
  }

  #visit-count-chart {}

  .thing-table {
    box-sizing: border-box;
    border: 1px solid rgba(32, 53, 128, 0.16);

  }

  .right-aligned {
    text-align: right;
    font-size: 17px;
  }

  .thing-table-ul {
    display: flex;
    border-bottom: 1px solid rgba(32, 53, 128, 0.16);
  }

  .thing-table-li {
    flex: 1;
    padding: 15px;
    font-size: 13px;
    font-weight: 400;
    color: #11142D;

    &:nth-child(2n) {
      border-left: 1px solid rgba(32, 53, 128, 0.16);
    }
  }

  .thing-table-ul:last-child {
    border-bottom: none;
  }

  .content-main,
  .welcome {
    height: calc(100%);
    padding-bottom: 15px;
  }

  .welcome {
    background: #fff;
    text-align: center;
    padding-top: 10%;
    box-sizing: border-box;
  }

  .right_content,
  .left_content {
    box-sizing: border-box;
    padding: 20px;
    background: #FFFFFF;
    box-shadow: 0px 3px 21px 0px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
  }

  .right_content {
    width: 25%;
  }

  .left_content {
    margin-right: 20px;
  }

  .echars-name {
    font-size: 17px;
    font-weight: 600;
    color: #181F2D;

    span {
      color: #FF6628;
    }
  }
  </style>
