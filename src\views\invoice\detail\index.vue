<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row>
          <el-col :lg="24" :xs="24">
            <el-form label-width="150px" label-position="right">
              <el-form-item label="企业名称" class="form-items">
                {{ formData.invoice.merName || '-' }}
              </el-form-item>
              <el-form-item label="结算批次号" class="form-items">
                <div class="goProject" v-for="item in formData.bathNums" :key="item">
                  <p @click="goProject(item)">{{ item }}</p>
                </div>
              </el-form-item>
              <el-form-item label="代征主体" class="form-items">
                {{ formData.invoice.levyName || '-' }}
              </el-form-item>
              <el-form-item v-if="formData.invoice.trackNumber" label="快递单号" class="form-items">
                {{ formData.invoice.trackNumber }}
              </el-form-item>
              <el-form-item label="审核备注" class="form-items">
                {{ formData.invoice.auditRemark || '-' }}
              </el-form-item>
              <el-form-item label="开票信息" class="form-items">
                <p>名称：{{ formData.record.merName || '-' }}</p>
                <p>纳税人识别号：{{ formData.record.merIdenNumber || '-' }}</p>
                <p>地址：{{ formData.record.merAddress || '-' }}</p>
                <p>电话：{{ formData.record.merTelephone || '-' }}</p>
                <p>开户行：{{ formData.record.merBank || '-' }}</p>
                <p>银行账号：{{ formData.record.merBankCode || '-' }}</p>
              </el-form-item>
              <el-form-item label="邮寄人" class="form-items">
                {{ formData.record.conPerson || '-' }}
              </el-form-item>
              <el-form-item label="邮寄电话" class="form-items">
                {{ formData.record.conTelephone || '-' }}
              </el-form-item>
              <el-form-item label="邮寄地址" class="form-items">
                {{ formData.record.conAddress || '-' }}
              </el-form-item>
              <!--              <el-form-item label="可开票金额" class="form-items">-->
              <!--                {{formData.invoice.invAmount}}元-->
              <!--              </el-form-item>-->
              <el-form-item label="开票金额" class="form-items">
                {{ formData.invoice.invAmount || '-' }}元
              </el-form-item>
              <el-form-item label="开票类型" class="form-items">
                <span v-if="formData.invoice.invCategory == '1'" class="invoice-ordinary">普票</span>
                <span v-if="formData.invoice.invCategory == '2'" class="invoice-specially">专票</span>
              </el-form-item>
              <el-form-item label="是否预开" class="form-items">
                <span v-if="formData.invoice.invPreopen == '1'" style="color: #f54343">是</span>
                <span v-else>否</span>
              </el-form-item>

              <el-form-item label="开票类目" class="form-items">
                <template v-for="item in formData.billType">
                  {{ item.typeName }} {{ item.invMoney ? '| 开票金额' + item.invMoney +'元' : '' }}</br>
                </template>
              </el-form-item>

              <el-form-item v-if="formData.list1.length > 0" label="发票" class="form-items">
                <template v-for="item in formData.list1">
                  <img v-if="item.filePath && (item.filePath.indexOf('.png') > 0 || item.filePath.indexOf('.jpg') > 0 || item.filePath.indexOf('.jpeg') > 0)" :src="item.filePath" alt="" class="invoice-img" />
                  <a v-else :href="item.filePath" target="_blank" style="color: #007aff; text-decoration: underline;display: block;">{{ item.filePath }}</a>
                </template>
              </el-form-item>

              <el-form-item v-if="formData.list2.length > 0" label="确认单" class="form-items">
                <template v-for="item in formData.list2">
                  <img v-if="item.filePath && (item.filePath.indexOf('.png') > 0 || item.filePath.indexOf('.jpg') > 0 || item.filePath.indexOf('.jpeg') > 0)" :src="item.filePath" alt="" class="sure-order">
                  <a v-else :href="item.filePath" target="_blank" style="color: #007aff; text-decoration: underline;display: block;">{{ item.filePath }}</a>
                </template>
              </el-form-item>

              <el-form-item class="form-items" label="">
                <div style="padding-left: 150px">
                  <!--                  <el-button plain type="primary" v-if="routeData.type !== 'view'">提交审核</el-button>-->
                  <el-button plain type="primary" @click="goback">返回</el-button>
                </div>
              </el-form-item>

            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>
<script>
import invoiceType from '@/axios/default/invoiceType'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  data() {
    return {
      formData: {
        list1: [],
        list2: [],
        billType: [],
        invoice: [],
        record: []
      },
      routeData: ''
    }
  },
  watch: {
    '$route.path': {
      handler(oldVal, newVal) {
        if (this.$route.path.indexOf('/invoice/detail') != '-1') {
          this.initPage()
        }
      }

    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.routeData = { ...this.$route.query }
      this.queryInvoiceInfoById(this.routeData.id)
    },
    queryInvoiceInfoById(id) {
      invoiceType.queryInvoiceInfoById({
        invoiceId: id
      }).then(res => {
        if (res.data.code == '0000') {
          this.formData = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    goback() {
      this.$router.go(-1)
    },
    goProject(args) {
      this.$router.push({ path: '/balance/list', query: { batchNum: args }})
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }
  .form-items{
    width: 100%;
    display: flex;
    p{
      margin: 0;
      padding: 0;
    }
  }
  .auto-width{
    width: 100%;
  }

  /deep/ .el-form-item__content{
    margin-left: 0!important;
  }

  .el-checkbox-group{

  }
  .category-style{
    width: 450px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    .el-checkbox{
      flex: 1;
      margin: 0;
    }
  }
.invoice-img{
  width: 300px;
}
  .sure-order{
    width: 300px;
  }
  .invoice-ordinary{
    color: #f29c39;
  }
  .invoice-specially{
    color: #5791fd;
  }
  .goProject {
    color: #2b66fd;
    cursor: pointer;
  }
</style>
