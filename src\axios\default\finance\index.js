// 财务管理
import request from '@/axios/default/request'
import { API_account as API_Account } from '../index'

const api = {
  findAccountAdjustList: `${API_Account}/accountAdjust/findAccountAdjustList`, // 调账管理列表
  auditAccountAdjust: `${API_Account}/accountAdjust/auditAccountAdjust`, // 调账初审
  reAuditAccountAdjust: `${API_Account}/accountAdjust/reAuditAccountAdjust`, // 调账复审
  addAccountAdjust: `${API_Account}/accountAdjust/addAccountAdjust`, // 调账新增

  cashOrderInfoList: `${API_Account}/cashOrderInfo/cashOrderInfoList`, // 提现管理列表
  addCashOrderInfo: `${API_Account}/cashOrderInfo/addCashOrderInfo`, // 提现申请
  auditCashOrderInfo: `${API_Account}/cashOrderInfo/auditCashOrderInfo`, // 提现初审
  reAuditCashOrderInfo: `${API_Account}/cashOrderInfo/reAuditCashOrderInfo`, // 提现复审

  refundOrderInfoList: `${API_Account}/refundOrderInfo/refundOrderInfoList`, // 退款管理列表
  addRefundOrderInfo: `${API_Account}/refundOrderInfo/addRefundOrderInfo`, // 退款申请
  auditRefundOrderInfo: `${API_Account}/refundOrderInfo/auditRefundOrderInfo`, // 初审
  reAuditRefundOrderInfo: `${API_Account}/refundOrderInfo/reAuditRefundOrderInfo`, // 复审
  findTradeOrderByOrderNumber: `${API_Account}/refundOrderInfo/findTradeOrderByOrderNumber`, // 根据商户及订单号查询订单
  accountExcel: `${API_Account}/accountAdjust/excel`, // 调账下载
  refundExcel: `${API_Account}/refundOrderInfo/excel`, // 退款下载
  cashExcel: `${API_Account}/cashOrderInfo/excel`, // 提现下载
  addCashOrderInfoByYs: `${API_Account}/cashOrderInfo/addCashOrderInfoByYs` // 提交湖南郴税提现申请
}

const account = {
  findAccuntInfoList: params => {
    return request.postJson(api.findAccountAdjustList, params)
  },
  auditAccountAdjust: params => {
    return request.postJson(api.auditAccountAdjust, params)
  },
  reAuditAccountAdjust: params => {
    return request.postJson(api.reAuditAccountAdjust, params)
  },
  addAccountAdjust: params => {
    return request.postJson(api.addAccountAdjust, params)
  },
  cashOrderInfoList: params => {
    return request.postJson(api.cashOrderInfoList, params)
  },
  addCashOrderInfo: params => {
    return request.postJson(api.addCashOrderInfo, params)
  },

  refundOrderInfoList: params => {
    return request.postJson(api.refundOrderInfoList, params)
  },
  addRefundOrderInfo: params => {
    return request.postJson(api.addRefundOrderInfo, params)
  },
  auditRefundOrderInfo: params => {
    return request.postJson(api.auditRefundOrderInfo, params)
  },
  reAuditRefundOrderInfo: params => {
    return request.postJson(api.reAuditRefundOrderInfo, params)
  },
  findTradeOrderByOrderNumber: params => {
    return request.postJson(api.findTradeOrderByOrderNumber, params)
  },
  auditCashOrderInfo: params => {
    return request.postJson(api.auditCashOrderInfo, params)
  },
  reAuditCashOrderInfo: params => {
    return request.postJson(api.reAuditCashOrderInfo, params)
  },
  refundExcel: (params, filename, type) => {
    return request.downFiles(api.refundExcel, params, filename, type)
  },
  accountExcel: (params, filename, type) => {
    return request.downFiles(api.accountExcel, params, filename, type)
  },
  cashExcel: (params, filename, type) => {
    return request.downFiles(api.cashExcel, params, filename, type)
  },
  addCashOrderInfoByYs: params => {
    return request.postJson(api.addCashOrderInfoByYs, params)
  }
}

export default account
