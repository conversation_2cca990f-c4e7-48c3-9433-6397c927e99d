<template>
  <el-form ref="step2Form" size="mini" :model="formData" label-width="140px" label-position="right" :rules="rules" :disabled="viewMerchant">
    <div class="tab-box">
      <div class="sub-title">
        <i class="el-icon-edit" />
        结算信息</div>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否自由职业者签约" class="form-items" label-width="150px" prop="isFreeLancer">
            <el-radio v-model="formData.isFreeLancer" :label="1">是</el-radio>
            <el-radio v-model="formData.isFreeLancer" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否运营审核" class="form-items" label-width="150px" prop="isSetAudit">
            <el-radio v-model="formData.isSetAudit" :label="1">是</el-radio>
            <el-radio v-model="formData.isSetAudit" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否上传开票确认单" class="form-items" label-width="150px" prop="isUpConfirm">
            <el-radio v-model="formData.isUpConfirm" :label="1">是</el-radio>
            <el-radio v-model="formData.isUpConfirm" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否上传结算确认单" class="form-items" label-width="150px" prop="isUpSettlement">
            <el-radio v-model="formData.isUpSettlement" :label="1">是</el-radio>
            <el-radio v-model="formData.isUpSettlement" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :xs="24">
          <el-form-item label="是否鉴权" class="form-items" label-width="150px" prop="isCheck">
            <el-radio v-model="formData.isCheck" :label="1">是</el-radio>
            <el-radio v-model="formData.isCheck" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :lg="8" :xs="24">
          <el-form-item label="企业日限额" class="form-items" prop="frimLimitDay">
            <el-input v-model="formData.frimLimitDay" class="auto-width" />
            <span class="warming-tips">-1表示不限额</span>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :xs="24">
          <el-form-item label="企业月限额" class="form-items" prop="frimLimitMonth">
            <el-input v-model="formData.frimLimitMonth" class="auto-width" />
            <span class="warming-tips">-1表示不限额</span>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :xs="24">
          <el-form-item label="企业年限额" class="form-items" prop="frimLimitYear">
            <el-input v-model="formData.frimLimitYear" class="auto-width" />
            <span class="warming-tips">-1表示不限额</span>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="text-align: right">
        <el-button type="primary" size="mini" icon="el-icon-plus" @click="addLevyIds">新增代征主体</el-button>
      </div>

      <div v-for="(item,index) in formData.levys" :key="item.id" class="levys-layout">
        <el-row :gutter="20">
          <el-col :lg="8" :xs="24">
            <el-form-item
              label="自由职业者日限额"
              :rules="[{ required: true, message: '请填写正确的数字'},
                       { validator: (rule, value, callback) => {
                         const result = /(^(-1)$)|(^[0-9]*(\d*|\.\d*)$)/.test(value)
                         if (result) {
                           callback()
                         } else {
                           callback('请填写正确的数字')
                         }
                       }, trigger: ['blur', 'change']}]"
              class="form-items"
              label-width="150px"
              :prop="'levys.'+index+'.freelancerLimitDay'"
            >
              <el-input v-model="item.freelancerLimitDay" class="auto-width" />
              <span class="warming-tips">-1表示不限额</span>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xs="24">
            <el-form-item
              label="自由职业者月限额"
              :rules="[{ required: true, message: '请填写正确的数字'},{ validator: (rule, value, callback) => {
                const result = /(^(-1)$)|(^[0-9]*(\d*|\.\d*)$)/.test(value)
                if (result) {
                  callback()
                } else {
                  callback('请填写正确的数字')
                }
              }, trigger: ['blur', 'change']}]"
              class="form-items"
              label-width="150px"
              :prop="'levys.'+index+'.freelancerLimitMonth'"
            >
              <el-input v-model="item.freelancerLimitMonth" class="auto-width" />
              <span class="warming-tips">-1表示不限额</span>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xs="24">
            <el-form-item
              label="自由职业者年限额"
              :rules="[{ required: true, message: '请填写正确的数字'},{ validator: (rule, value, callback) => {
                const result = /(^(-1)$)|(^[0-9]*(\d*|\.\d*)$)/.test(value)
                if (result) {
                  callback()
                } else {
                  callback('请填写正确的数字')
                }
              }, trigger: ['blur', 'change']}]"
              class="form-items"
              label-width="150px"
              :prop="'levys.'+index+'.freelancerLimitYear'"
            >
              <el-input v-model="item.freelancerLimitYear" class="auto-width" />
              <span class="warming-tips">-1表示不限额</span>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item label-width="150px" label="平台" class="form-items" :rules="[{ required: true, message: '请选择平台'}]" :prop="'levys.'+index+'.levyIds'">
              <el-select v-model="item.levyIds" style="width:260px" placeholder="请选择" :disabled="isModifyCheckData" @change="changeChannel(item.levyIds,index)">
                <el-option
                  v-for="item in levyIdOpt"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-popconfirm v-if="isModifyCheckData" title="修改平台或服务费率后需重新审核" @onConfirm="sureEdit(item.levyIds)">
                <el-button slot="reference" size="mini" class="checkLevBtn">修改</el-button>
              </el-popconfirm>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item
              label-width="150px"
              label="上传合同"
              class="form-items"
            >
              <el-upload
                ref="upload"
                :disabled="hasLevyFile(item.levyIds) || upLoading"
                class="file-upload"
                :show-file-list="false"
                action=""
                accept=".pdf,.jpg,.jpeg,.png"
                :http-request="(params => uprequest(params,'1', item.levyIds))"
              >
                <el-button :loading="upLoading" :disabled="hasLevyFile(item.levyIds) || upLoading">上传合同</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item
              label-width="150px"
              label="最低开票金额"
              class="form-items"
              :rules="[{ required: true , message: '最低开票金额'}]"
              :prop="'levys.'+index+'.minInvoicedAmount'"
            >
              <el-input v-model="item.minInvoicedAmount" style="width:326px" />
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item label-width="150px" label="服务费率" class="form-items form-flex" :rules="[{ required: true, message: '请选择服务费率'}]" :prop="'levys.'+index+'.feeRates'">
              <el-radio v-model="item.feeRateTypes" :disabled="isModifyCheckData" label="3">外扣</el-radio>
              <el-radio v-model="item.feeRateTypes" :disabled="isModifyCheckData" label="4">内扣</el-radio>
              <el-input v-model="item.feeRates" :disabled="isModifyCheckData">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item label-width="150px" label="个税承担方" class="form-items form-flex" :rules="[{ required: true, message: '请选择个税承担方'}]" :prop="'levys.'+index+'.taxFeeRateTypes'">
              <el-radio v-model="item.taxFeeRateTypes" :disabled="isModifyCheckData" :label="0">个人</el-radio>
              <el-radio v-model="item.taxFeeRateTypes" :disabled="isModifyCheckData" :label="1">企业</el-radio>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item label-width="150px" label="实际服务费率" class="form-items form-flex" :rules="[{ required: false, message: '请选择服务费率'}]" :prop="'levys.'+index+'.lowerFee'">
              <el-input v-model="item.lowerFee" :disabled="isModifyCheckData">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item
              label-width="150px"
              label="奖励金额阈值"
              class="form-items"
              :rules="[{ required: false , message: '奖励金额阈值'}]"
              :prop="'levys.'+index+'.amt'"
            >
              <el-input v-model="item.amt" style="width:326px" />
            </el-form-item>
          </el-col>
          <el-col :lg="24" :xs="24">
            <el-form-item label-width="150px" label="是否上传证件照片" class="form-items form-flex" :rules="[{ required: true, message: '请选择是否上传身份证照片'}]" :prop="'levys.'+index+'.isUploadCardPic'">
              <el-radio v-model="item.isUploadCardPic" :label="0">不需要</el-radio>
              <el-radio v-model="item.isUploadCardPic" :label="1">需要</el-radio>
            </el-form-item>
          </el-col>
          <el-col v-if="payArr[index] && payArr[index].length > 0" :lg="24" :xs="24">
            <el-form-item :key="payArr[index].length" label-width="150px" label="代付通道" class="form-items" :prop="'levys.'+index+'.payChannelIds'">
              <el-checkbox-group v-model="item.payChannelIds">
                <el-checkbox v-for="(val,idx) in payArr[index]" :key="val.id" :label="val.id">{{ val.channelName }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col v-if="invoiceArr[index] && invoiceArr[index].length > 0" :lg="24" :xs="24">
            <el-form-item label-width="150px" label="开票类目" class="form-items" :rules="[{ required: true, message: '请选择开票类目'}]" :prop="'levys.'+index+'.invoiceTypeIds'">
              <el-cascader
                :key="item.levyIds"
                v-model="item.invoiceTypeIds"
                filterable
                :options="invoiceArr[index]"
                :props="{ multiple: true }"
                style="width: 300px"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col v-if="item.projectInfoList && item.projectInfoList.length > 0" :lg="24" :xs="24">
            <el-form-item label-width="150px" label="待审核项目" class="form-items">
              <el-row :gutter="20">
                <el-col v-for="itemChild in item.projectInfoList" :key="itemChild.id" :lg="4" :xs="8">
                  <span v-if="itemChild.status==2" class="projectItem" @click="onGoProject(itemChild.id)">{{ itemChild.name }}--项目ID：{{ itemChild.id }}</span>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="margin-left:5px">
          <el-button v-if="formData.levys.length > 1" size="mini" @click="delLevyIds(index)">删除</el-button>
        </div>
      </div>
    </div>
  </el-form>
</template>
<script>
import publics from '@/axios/default/public'
import { newAlert } from '../../../utils'
export default {
  name: 'Step2',
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    },
    id: {
      default: ''
    },
    isAudit: {
      type: Number,
      default: 0
    },
    editMerchant: {
      type: Boolean,
      default: false
    },
    viewMerchant: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      minInvoicedAmount: {},
      payArr: [],
      levyIdOpt: [],
      // 开票类目
      invoiceArr: [],
      // isModifyCheckData: false, // 是否为可编辑状态 true 则为"是"
      upLoading: false,
      upData: {
        file: '',
        fileType: '',
        levyId: ''
      },
      rules: {
        isFreeLancer: [
          { required: true, message: '请选择自由职业者签约', trigger: 'change' }
        ],
        isSetAudit: [
          { required: true, message: '请选择是否运营审核', trigger: 'change' }
        ],
        isUpConfirm: [
          { required: true, message: '请选择是否开票上传确认单', trigger: 'change' }
        ],
        isUpSettlement: [
          { required: true, message: '请选择是否结算上传确认单', trigger: 'change' }
        ],
        isCheck: [
          { required: true, message: '请选择是否鉴权', trigger: 'change' }
        ],
        frimLimitDay: [
          { required: true, message: '请填写企业日限额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^(-1)$)|(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请填写正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        frimLimitMonth: [
          { required: true, message: '请填写企业月限额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^(-1)$)|(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请填写正确的数字')
            }
          }, trigger: ['blur', 'change'] }
        ],
        frimLimitYear: [
          { required: true, message: '请填写企业年限额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^(-1)$)|(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请填写正确的数字')
            }
          }, trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  computed: {
    formData: {
      get() {
        return this.data
      },
      set(val) {
        // this.$emit('update:page', val)
      }
    },
    isModifyCheckData: {
      get() {
        return this.editMerchant && this.isAudit != 0 && this.formData.saveFlag == 0
      },
      set(val) {
        // console.log(val)
      }
    }
  },
  watch: {
    data(n, o) {
      if (n.id) {
        const data = this.formData.levys
        data.forEach((item, index, arr) => {
          // 设置个税承担方默认值
          if (item.taxFeeRateTypes === undefined || item.taxFeeRateTypes === null) {
            this.$set(item, 'taxFeeRateTypes', 0)
          } else if (typeof item.taxFeeRateTypes === 'string') {
            // 如果是字符串类型，转换为数字类型
            this.$set(item, 'taxFeeRateTypes', parseInt(item.taxFeeRateTypes))
          }

          // 处理开票类目的回显----star
          const tempTypeIds = JSON.parse(JSON.stringify(item.invoiceTypeIds))
          item.invoiceTypeIds = []
          item.invoiceTypeAllIds.forEach(itemChild => {
            if (itemChild.children.length > 0) {
              itemChild.children.forEach(itemCEr => {
                if (itemCEr.children.length > 0) {
                  itemCEr.children.forEach(itemCSan => {
                    if (tempTypeIds.includes(itemCSan.value)) {
                      item.invoiceTypeIds.push([itemChild.value, itemCEr.value, itemCSan.value])
                    }
                  })
                } else {
                  if (tempTypeIds.includes(itemCEr.value)) {
                    item.invoiceTypeIds.push([itemChild.value, itemCEr.value])
                  }
                }
              })
            } else {
              if (tempTypeIds.includes(itemChild.value)) {
                item.invoiceTypeIds.push([itemChild.value])
              }
            }
          })
          // 处理开票类目的回显----end

          // 回显所有开票类目与代付通道
          const invoiceTypeAllIds = item.invoiceTypeAllIds
          const payChannelAllIds = JSON.parse(item.payChannelAllIds)
          this.$set(this.invoiceArr, index, this.recursiveProcessing(invoiceTypeAllIds))
          this.$set(this.payArr, index, payChannelAllIds)
        })
      } else {
        this.queryLevyBodyInfos()
      }
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  methods: {
    sureEdit(levyId = 0) {
      this.isModifyCheckData = false
      this.formData.saveFlag = '1'
      this.$emit('changeModifyIsSave', false)

      // 修改代征主体时 删掉此代征主体下的合同
      if (levyId > 0) {
        this.fileList = this.$store.state.merchants.fileList
        const index = this.fileList.findIndex(item => item.levyId == levyId)
        if (index > -1) {
          this.fileList.splice(index, 1)
          this.$store.commit('merchants/setFileList', this.fileList)
        }
      }
    },
    hasLevyFile(levyId) {
      const fileList = this.$store.state.merchants.fileList
      const hasLevyFiles = fileList.filter(item => item.levyId == levyId)
      if (hasLevyFiles.length > 0) {
        return true
      } else {
        return false
      }
    },
    // 新增代征主体
    addLevyIds() {
      if (this.levyIdOpt.length > this.formData.levys.length) {
        this.formData.levys.push({
          levyIds: '',
          feeRateTypes: '3',
          feeRates: '',
          levyName: '',
          lowerFee: '',
          isUploadCardPic: 2,
          taxFeeRateTypes: 0,
          payChannelIds: [],
          invoiceTypeIds: [],
          minInvoicedAmount: '10000',
          invoiceTypeAllIds: '',
          payChannelAllIds: '',
          freelancerLimitDay: 98000,
          freelancerLimitMonth: 98000,
          freelancerLimitYear: 98000 * 12
        })
        this.payArr.push([])
        this.invoiceArr.push([])
        this.sureEdit()
      } else {
        this.$message.error('不能超过代征主体总数')
      }
    },

    // 上传
    uprequest(param, fileType, levyId) {
      const that = this
      const size = param.file.size
      const limitSize = size / 1024 / 1024 < 50
      if (!limitSize) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 50MB!')
        }, 100)
        return
      }
      that.upLoading = true
      that.upData.file = param.file
      that.upData.fileType = fileType
      that.upData.levyId = levyId
      this.fileListData()
    },

    fileListData() {
      const formData = new FormData()
      const data = this.upData
      for (const key in data) {
        formData.append(key, data[key])
      }
      publics.upFiles(formData).then(res => {
        if (res.data.code == '0000') {
          const { fileName, fileSize, fileExt, filePath, fileType } = res.data.data
          this.fileList = this.$store.state.merchants.fileList

          this.fileList.push({
            fileName,
            fileExt,
            filePath,
            fileSize: fileSize,
            fileType: fileType,
            levyId: this.upData.levyId
          })

          this.$store.commit('merchants/setFileList', this.fileList)
          this.upLoading = false
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
          this.upLoading = false
        }
      }).catch(res => {
        this.upLoading = false
      })
    },

    // 代征主体
    queryLevyBodyInfos() {
      publics.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyIdOpt = res.data.data.map(res => {
            res.id = res.id.toString()
            return res
          })
        }
      })
    },
    // 选择主体
    changeChannel(id, index) {
      publics.getChannelListByLevyId({
        levyBodyId: id
      }).then(res => {
        this.$set(this.formData.levys[index], 'payChannelIds', [])
        this.$set(this.formData.levys[index], 'invoiceTypeIds', [])
        this.$set(this.payArr, index, res.data.data)
        const name = this.levyIdOpt.find(r => r.id == id).name
        this.$set(this.formData.levys[index], 'levyName', name)
        const minInvoicedAmount = this.levyIdOpt.find(r => r.id == id).minInvoicedAmount
        this.$set(this.formData.levys[index], 'minInvoicedAmount', minInvoicedAmount)
      })
      this.queryInvoiceTypeInfo(id, index)
    },
    // 行业类型
    queryInvoiceTypeInfo(id, index) {
      publics.queryInvoiceTypeInfo({
        levyId: id
      }).then(res => {
        this.$set(this.invoiceArr, index, this.recursiveProcessing(res.data.data))
      })
    },

    recursiveProcessing(arr) {
      // 递归处理开票类型数据
      arr.forEach(item => {
        if (item.hasOwnProperty('children')) {
          if (item.children.length > 0) {
            // 有子节点
            this.recursiveProcessing(item.children)
          } else {
            // 无子节点
            delete item.children
          }
        }
      })
      return arr
    },
    onGoProject(id) {
      this.$router.push({ path: '/proManage/list', query: { projectId: id }})
    },
    // 删除代征主体
    delLevyIds(index) {
      this.formData.levys.splice(index, 1)
      this.payArr.splice(index, 1)
      this.invoiceArr.splice(index, 1)
      this.sureEdit()
    },
    step2Form() {
      return this.$refs.step2Form.validate()
    }
  }
}
</script>
<style scoped lang="scss">
.levys-layout{
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 15px;
  border:1px solid #ecd2d2;
  display: flex;
  justify-content: space-between;
}
.sub-title{
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 20px;
  background: #efefef;
  padding: 10px;
  margin-top: 20px;
}
.sub-title:first-child{
  margin-top: 0;
}

.form-flex{
  /deep/ .el-form-item__content{
    display: flex;
    align-items: center;
    .el-input{
      width: 135px;
    }
  }
}

.checkLevBtn{
  margin-left:10px!important;
}
.warming-tips{
  color: #F56C6C;
  font-size: 12px;
}
.auto-width{
  width: 100%;
}
.projectItem {
  color: #2b66fd;
  cursor: pointer;
}
</style>
