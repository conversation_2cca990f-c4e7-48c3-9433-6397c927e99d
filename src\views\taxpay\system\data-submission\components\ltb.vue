<template>
  <el-table :data="tableData" border stripe size="small">
    <el-table-column prop="序号" label="序号" width="60" fixed="left">
      <template slot-scope="scope">
        {{ scope.$index + 1 }}
      </template>
    </el-table-column>

    <el-table-column label="是否已取得登记证照">
      <el-table-column label="1" />
    </el-table-column>
    <el-table-column label="已取得登记证照的其他单位、个体工商户">
      <el-table-column label="名称（姓名）">
        <el-table-column label="2" />
      </el-table-column>
      <el-table-column label="统一社会信用代码（纳税人识别号）">
        <el-table-column label="3" />
      </el-table-column>
    </el-table-column>
    <el-table-column label="未取得登记证照的网络主播、其他自然人">
      <el-table-column label="姓名">
        <el-table-column label="4" />
      </el-table-column>
      <el-table-column label="证件类型">
        <el-table-column label="5" />
      </el-table-column>
      <el-table-column label="证件号码">
        <el-table-column label="6" />
      </el-table-column>
      <el-table-column label="国家或地区">
        <el-table-column label="7" />
      </el-table-column>
    </el-table-column>

    <el-table-column label="联系电话">
      <el-table-column label="8" />
    </el-table-column>

    <el-table-column label="结算（支付）账户信息（选填）">
      <el-table-column label="开户银行/非银行支付机构">
        <el-table-column label="9" />
      </el-table-column>
      <el-table-column label="账户名称">
        <el-table-column label="10" />
      </el-table-column>
      <el-table-column label="银行账号/支付账户">
        <el-table-column label="11" />
      </el-table-column>
    </el-table-column>

    <el-table-column label="销售服务取得的收入信息">
      <el-table-column label="已取得登记证照的其他单位、个体工商户">
        <el-table-column label="收入总额">
          <el-table-column label="15" />
        </el-table-column>
        <el-table-column label="退款金额">
          <el-table-column label="16" />
        </el-table-column>
        <el-table-column label="收入净额">
          <el-table-column label="17=15-16" />
        </el-table-column>
      </el-table-column>
      <el-table-column label="未取得登记证照的网络主播、其他自然人">
        <el-table-column label="收入总额">
          <el-table-column label="18" />
        </el-table-column>
        <el-table-column label="退款金额">
          <el-table-column label="19" />
        </el-table-column>
        <el-table-column label="收入净额">
          <el-table-column label="小计">
            <el-table-column label="20=18-19=21+22" />
          </el-table-column>
          <el-table-column label="其中：">
            <el-table-column label="劳务报酬">
              <el-table-column label="21" />
            </el-table-column>
            <el-table-column label="其他收入">
              <el-table-column label="22" />
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table-column>
    </el-table-column>

    <el-table-column label="从事其他网络交易活动取得的收入（净额）">
      <el-table-column label="11" />
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'Ltb',
  data() {
    return {
      tableData: [
        {
          shopName: '张三的小店',
          shopId: 'UID123456',
          transactionId: 'TXN202401010001',
          transactionDate: '2024-01-01 10:30:00',
          productName: '手机壳',
          productCategory: '实物商品',
          quantity: '2',
          unit: '个',
          unitPrice: '25.00',
          totalAmount: '50.00',
          commission: '5.00',
          actualAmount: '45.00',
          buyerName: '李四',
          buyerId: 'BUYER123',
          buyerPhone: '13900000000',
          paymentMethod: '支付宝',
          transactionStatus: '已完成',
          deliveryAddress: '北京市海淀区xxx小区',
          remarks: '正常交易',
          status: '新增'
        }
      ]
    }
  },
  methods: {}
}
</script>

<style scoped>
.transaction-info-table {
  padding: 20px;
}

.table-header {
  margin-bottom: 20px;
}

.table-header h3 {
  color: #333;
  font-size: 16px;
  margin: 0;
  text-align: center;
}

.data-table {
  width: 100%;
  margin-bottom: 20px;
}

.data-table .el-table__header-wrapper {
  background-color: #e9ecef;
}

.summary-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.summary-item .label {
  font-weight: bold;
  color: #495057;
}

.summary-item .value {
  color: #007bff;
  font-weight: bold;
}

.operation-buttons {
  text-align: center;
  padding-top: 20px;
}

.operation-buttons .el-button {
  margin: 0 10px;
}

/* 表格样式优化 */
.data-table .el-table__body-wrapper {
  overflow-x: auto;
}

.data-table .el-input__inner {
  border: none;
  background: transparent;
  padding: 0 5px;
}

.data-table .el-input__inner:focus {
  border: 1px solid #409eff;
  background: #fff;
}

.data-table .el-select {
  width: 100%;
}

.data-table .el-date-editor {
  width: 100%;
}

.data-table .el-textarea__inner {
  border: none;
  background: transparent;
  padding: 2px 5px;
  resize: none;
}

.data-table .el-textarea__inner:focus {
  border: 1px solid #409eff;
  background: #fff;
}
</style>
