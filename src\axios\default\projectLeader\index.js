// 项目负责人
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  addProjectLeader: `${API_Header}/projectLeader/addProjectLeader`, // 新增项目负责人
  proLeaderList: `${API_Header}/projectLeader/list`, // 项目负责人列表
  getOneProjectLeader: `${API_Header}/projectLeader/getOneProjectLeader`, // 根据id获取项目负责人
  proLeaderAllList: `${API_Header}/projectLeader/allList`, // 项目负责人列表（全部）
  updateProjectLeader: `${API_Header}/projectLeader/updateProjectLeader` // 修改项目负责人
}

const proleader = {
  addProjectLeader: params => {
    return request.postJson(api.addProjectLeader, params)
  },
  updateProjectLeader: params => {
    return request.postJson(api.updateProjectLeader, params)
  },
  proLeaderList: params => {
    return request.postJson(api.proLeaderList, params)
  },
  getOneProjectLeader: params => {
    return request.postJson(api.getOneProjectLeader, params)
  },
  proLeaderAllList: params => {
    return request.postJson(api.proLeaderAllList, params)
  }
}

export default proleader

