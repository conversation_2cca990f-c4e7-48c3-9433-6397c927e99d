<template>
  <div class="message">
    <div class="header-message" @click.stop="handleShow">
      <el-badge :value="$store.state.message.settingBar.notReadNum" :hidden="$store.state.message.settingBar.notReadNum == 0" class="item">
        <svg-icon class-name="message-icon" icon-class="message" style="margin-top: 2px"/>
      </el-badge>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Message',
  data() {
    return {

    }
  },
  components: {

  },
  computed: {

  },
  watch: {

  },
  methods: {
    handleShow() {
      this.$store.commit('message/openSettingBar', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.message {
  line-height: 24px !important;
  .header-message {
    font-size: 0 !important;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .message-icon {
      cursor: pointer;
      font-size: 22px;
      vertical-align: middle;
    }

  }
}

</style>
