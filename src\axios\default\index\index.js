// 首页数据
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  getHomeData: `${API_Header}/homeData/getHomeData`, // HomeData
  getRechargeTop10: `${API_Header}/homeData/getRechargeTop10`, // Top10
  getRechargeTop10ByAgent: `${API_Header}/homeData/getRechargeTop10ByAgent`, // Top10
  getWaitDealt: `${API_Header}/homeData/getWaitDealt`, // getWaitDealt
  messageList: `${API_Header}/homeData/messageList`, // 消息列表
  readMessage: `${API_Header}/homeData/readMessage` // 更改消息状态
}

const indexData = {
  getHomeData: params => {
    return request.postJson(api.getHomeData, params)
  },
  getRechargeTop10: params => {
    return request.postJson(api.getRechargeTop10, params)
  },
  getRechargeTop10ByAgent: params => {
    return request.postJson(api.getRechargeTop10ByAgent, params)
  },
  getWaitDealt: params => {
    return request.postJson(api.getWaitDealt, params)
  },
  messageList: params => {
    return request.postJson(api.messageList, params)
  },
  readMessage: params => {
    return request.postJson(api.readMessage, params)
  }
}

export default indexData

