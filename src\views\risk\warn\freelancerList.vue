<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="结算月份" class="form-items">
                    <el-date-picker
                      :clearable="false"
                      class="auto-width"
                      v-model="formData.blaDate"
                      format="yyyy 年 MM 月"
                      value-format="yyyy-MM"
                      type="month"
                      placeholder="请选择月份">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="产品类别" class="form-items">
                    <el-select v-model="formData.settlementType" placeholder="请选择" class="auto-width">
                      <el-option label="请选择" :value="''" />
                      <el-option label="委托代征" :value="'1'" />
                      <el-option label="自然人代发" :value="'2'" />
                      <el-option label="个体户" :value="'3'" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levys.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levys"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="姓名" class="form-items">
                    <el-input v-model="formData.payeeName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button type="primary" size="mini" @click="exportExcel"><i class="el-icon-download"></i>导出报表</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            prop="merName"
            fixed
            min-width="220"
            label="商户名称"
          />
          <el-table-column
            prop="merNo"
            min-width="180"
            label="商户编号"
          />
          <el-table-column
            prop="payeeName"
            min-width="120"
            label="姓名"
          />
          <el-table-column
            prop="name"
            min-width="200"
            label="代征主体"
          />
          <el-table-column
            prop="telephone"
            min-width="100"
            label="联系方式"
          />
          <el-table-column
            prop="payeeIdCard"
            min-width="220"
            label="证件号"
          />
          <el-table-column
            prop="money"
            min-width="220"
            align="right"
            label="结算金额"
          />
          <el-table-column
            fixed="right"
            prop="createTime"
            min-width="140"
            label="创建时间"
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import risk from '@/axios/default/risk'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'
import publicApi from '@/axios/default/public'
import { parseTime, formatNumber } from '@/utils'
export default {
  name: 'FreelancerList',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {
        merName: '',
        payeeName: '',
        blaDate: `${new Date().getFullYear()}-${new Date().getMonth() + 1}`,
        levyId: '',
        settlementType: '2'
      },
      levys: [],
      listData: [],
      loading: false,
      btnLoading: false
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },

    exportExcel() {
      risk.downLoadFreelancerList({
        ...this.formData
      }, `自由职业者监控报表${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xls`, '.xls').then(res => {

      })
    },

    list() {
      risk.freelancerList({
        ...this.formData,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          res.data.data.records.forEach(item => {
            item.money = formatNumber(item.settMoney)
          })
          this.listData = res.data.data.records
          this.total = res.data.data.total
          this.pageSize = res.data.data.size
          this.pageNum = res.data.data.current
        }
      })
    },
    // 换页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },

    tableRowClassName({ row }) {
      if (row.settMoney >= 98000) {
        return 'warning-row'
      } else if (row.settMoney >= 50000 && row.settMoney < 98000) {
        return 'warning-oldlace'
      }
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levys = res.data.data
          this.$set(this.formData, 'levyId', 7)
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.list-card{
  margin-bottom: 20px;
}

.auto-width{
  width: 100%;
}

.dialog-form{
  display: flex;
  flex-flow: column nowrap;
}
.el-form-item__content{
  margin-left: 0;
}

.list-table{
  margin: 0 0 20px 0;
  .el-button{
    padding: 0;
  }
  /deep/ .warning-row {
    background: #fbc4c4 !important;
  }
  /deep/ .warning-oldlace {
    background: oldlace !important;
  }

}
</style>
