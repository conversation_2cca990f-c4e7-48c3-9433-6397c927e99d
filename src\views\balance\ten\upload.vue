<template>
  <div class="content-main">
    <div class="general-layout">
      <div class="notice">
        <i class="el-icon-info notice-left" />
        <div class="notice-right">
          <p>批量打款业务说明:</p>
          <p class="font">1.请选择出款账户后，下载对应模板完成打款</p>
          <p class="font">2.单批次最大支持30000条订单</p>
          <p class="font">3.系统仅支持结算到一类储蓄卡中</p>
        </div>
      </div>

      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :lg="12" :xs="24">
            <el-form ref="formData" :model="formData" :rules="rules" size="mini" label-width="120px">
              <el-form-item label="企业名称" prop="merId">
                <el-select
                  v-model="formData.merId"
                  class="auto-width"
                  filterable
                  remote
                  placeholder="请输入关键词"
                  :remote-method="mccList"
                  :loading="loading"
                  @change="getlevyId"
                >
                  <el-option v-for="(item,index) in merNameArr" :key="item.id" :label="item.merName" :value="item.id" />
                </el-select>

              </el-form-item>
              <el-form-item label="代征主体" prop="levyId">
                <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width" @change="getChannel">
                  <el-option
                    v-for="(item,index) in levyIdArr"
                    :key="item.index"
                    :label="item.NAME"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="打款通道" prop="payChannel">
                <el-select v-model="formData.payChannel" placeholder="请选择" class="auto-width" @change="getMoney">
                  <el-option
                    v-for="item in channelIdArr"
                    :key="item.id"
                    :label="item.channel_name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="余额" prop="">
                <el-input v-model="moneyArr.canBalance" readonly>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>

              <el-form-item label="服务费率" prop="freeRate">
                <el-input v-model="formData.freeRate" type="text" placeholder="请输入内容">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>

              <el-form-item label="是否申报" prop="isDeclare">
                <el-radio v-model="formData.isDeclare" :label="1">申报</el-radio>
                <el-radio v-model="formData.isDeclare" :label="2">不申报</el-radio>
              </el-form-item>

              <el-form-item label="模版下载">
                <span class="down-model">
                  <svg-icon class-name="icon-style" icon-class="icon_payment_template" />
                  <a :href="temYhk">银行卡批量打款模版</a>
                </span>
                <span class="down-model">
                  <svg-icon class-name="icon-style" icon-class="icon_payment_template" />
                  <a :href="temZfb">支付宝批量打款模版</a>
                </span>
              </el-form-item>
              <el-form-item label="结算类型" prop="settlementType">
                <el-radio v-model="formData.settlementType" :label="1">委托代征</el-radio>
                <el-radio v-model="formData.settlementType" :label="2">自然人代发</el-radio>
                <el-radio v-model="formData.settlementType" :label="3">个体户</el-radio>
              </el-form-item>

              <el-form-item>
                <el-upload
                  ref="upload"
                  class="file-upload"
                  :file-list="fileList"
                  action=""
                  :http-request="uprequest"
                >
                  <el-button size="mini" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">只能上传xlsx文件，且不超过15mb</div>
                </el-upload>
              </el-form-item>
              <el-form-item>
                <div style="font-size: 12px">
                  结算金额：{{ settlement.settMoney }}元，服务费率：{{ settlement.feeRate }}% 服务费：{{ settlement.totalFree }}元，总额为：{{ settlement.totalMoney }}元
                </div>
              </el-form-item>
              <el-form-item>
                <el-button plain type="primary" :disabled="btnDisabled" :loading="btnLoading" @click="insertTradOrderInfos('formData')">提交审核</el-button>
              </el-form-item>
              <transition name="el-fade-in">
                <div v-if="btnDisabled" class="error-box">
                  上传文件数据异常，<span class="viewinfo" @click="errDialogShow = true">查看异常详情</span>
                </div>
              </transition>
            </el-form>
          </el-col>
        </el-row>

      </el-card>
    </div>
  </div>
</template>
<script>
// import XLSX from 'xlsx'
import tradeService from '@/axios/default/tradeService'
import rechInfo from '@/axios/default/rechInfo'
import { fixMoeny, addNum, downHost, newAlert } from '@/utils'

export default {
  name: 'Remittance',
  data() {
    return {
      btnDisabled: false,
      fileList: [],
      formData: {
        merId: '',
        merName: '',
        levyId: '',
        payChannel: '',
        channelName: '',
        freeRate: '',
        isDeclare: '',
        settlementType: '',
        file: ''
      },
      accountInfo: [],
      mainOpt: [],
      channelOpt: [],
      merNameArr: [],
      levyIdArr: [],
      channelIdArr: [],
      moneyArr: {
        balance: 0,
        canBalance: 0,
        accountNo: ''
      },
      rules: {
        merId: [{ required: true, message: '请选择企业名称', trigger: 'change' }],
        levyId: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        payChannel: [{ required: true, message: '请选择打款通道', trigger: 'change' }],
        freeRate: [
          { required: true, message: '请输入费率', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            //
            const num = fixMoeny(this.formData.freeRate)
            if (/^[0]+[0-9]*$/gi.test(value)) {
              if (value.length == '1') {
                callback()
              } else {
                this.formData.freeRate = 0
                callback(new Error('请输入正确的费率'))
              }
            } else {
              if (value < 0) {
                this.formData.freeRate = 0
                callback(new Error('请输入正确的费率'))
              } else {
                callback()
              }
            }
          }, trigger: ['blur', 'change'] }],
        isDeclare: [{ required: true, message: '请选择是否申报', trigger: 'change' }],
        settlementType: [{ required: true, message: '请选择结算类型', trigger: 'change' }]
      },
      xlsxTotalMoney: 0,
      btnLoading: false,
      settlement: this.initSettlement(),
      temZfb: downHost + 'zfb-template.xls',
      temYhk: downHost + 'bankCard-template.xls',
      loading: false
    }
  },
  computed: {
    // totalMoney() {
    //   return this.xlsxTotalMoney
    // },
    // freeRate() {
    //   return this.formData.freeRate || 0
    // },
    // serverMoney() {
    //   return fixMoeny(this.formData.freeRate / 100 * this.xlsxTotalMoney)
    // },
    // allMoney() {
    //   return fixMoeny(this.xlsxTotalMoney + this.formData.freeRate / 100 * this.xlsxTotalMoney)
    // }
  },
  mounted() {
    // this.queryAccountInfo()
    this.moneyArr.canBalance = 0
    this.fileList = []
    this.settlement = this.initSettlement()
    this.$refs.formData.resetFields()
  },
  methods: {
    initSettlement() {
      return {
        settMoney: 0,
        feeRate: 0,
        totalFree: 0,
        totalMoney: 0
      }
    },
    // 提交
    insertTradOrderInfos(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          this.formData.merName = this.merNameArr.find(res => res.id == this.formData.merId).merName
          this.formData.channelName = this.channelIdArr.find(res => res.id == this.formData.payChannel).channel_name
          const formData = new FormData()
          const data = this.formData
          for (const key in data) {
            formData.append(key, data[key])
          }
          tradeService.insertTradOrderInfos(formData).then(res => {
            this.btnLoading = false
            if (res.data.code == '0000') {
              this.settlement = this.initSettlement()
              this.$refs[formName].clearValidate()
              this.$refs[formName].resetFields()
              this.moneyArr.balance = 0
              this.moneyArr.canBalance = 0
              this.$message.success('操作成功')
              this.fileList = []
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 1
    initAddRechInfo(data, item) {
      rechInfo.initAddRechInfo(data).then(res => {
        if (res.data.code === '0000') {
          this.loading = false
          this[item] = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    getlevyId() {
      this.formData.levyId = ''
      this.formData.payChannel = ''
      this.moneyArr.balance = 0
      this.moneyArr.canBalance = 0
      this.fileList = []
      this.settlement = this.initSettlement()
      this.$nextTick(function() {
        this.$refs.formData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '2', merId: this.formData.merId }, 'levyIdArr')
    },
    getChannel() {
      this.formData.payChannel = ''
      this.moneyArr.balance = 0
      this.moneyArr.canBalance = 0
      this.fileList = []
      this.settlement = this.initSettlement()
      this.$nextTick(function() {
        this.$refs.formData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '3', merId: this.formData.merId, levyId: this.formData.levyId }, 'channelIdArr')
    },
    getMoney() {
      this.moneyArr.balance = 0
      this.moneyArr.canBalance = 0
      this.fileList = []
      this.settlement = this.initSettlement()
      this.initAddRechInfo({ queryType: '4', merId: this.formData.merId, channelId: this.formData.payChannel }, 'moneyArr')
    },
    mccList(query) {
      if (query !== '') {
        this.loading = true
        // this.initAddRechInfo({ queryType: '1' }, 'merNameArr')
        this.initAddRechInfo({ queryType: '1', merName: query }, 'merNameArr')
      } else {
        this.merNameArr = []
      }
    },
    // 上传前处理
    uprequest(param) {
      const that = this
      this.fileList = []
      const size = param.file.size
      const isLt5M = size / 1024 / 1024 < 15
      if (!isLt5M) {
        setTimeout(function() {
          that.$message.error('上传大小不能超过 15MB!')
        }, 100)
        return
      }
      if (this.formData.merId === '') {
        newAlert(this.$tips, '请选择企业名称')
        return
      }
      if (this.formData.levyId === '') {
        newAlert(this.$tips, '请选择代征主体')
        return
      }
      if (this.formData.payChannel === '') {
        newAlert(this.$tips, '请填写打款通道')
        return
      }
      if (this.formData.freeRate === '') {
        newAlert(this.$tips, '请填写服务费率')
        return
      }
      // this.getFileMoney(param)
      this.formData.file = param.file
      this.fileList.push({ name: param.file.name })
      this.queryExcelMoney(param)
      // }
    },
    queryExcelMoney(param) {
      const formData = new FormData()
      const data = {
        merId: this.formData.merId,
        levyId: this.formData.levyId,
        payChannel: this.formData.payChannel,
        freeRate: this.formData.freeRate,
        file: this.formData.file
      }
      for (const key in data) {
        formData.append(key, data[key])
      }
      tradeService.queryExcelMoney(formData).then(res => {
        console.log(res)
        if (res.data.code == '0000') {
          this.settlement = res.data.data
          this.btnDisabled = false
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }
    // 获取xlsx金额
    // getFileMoney(param) {
    //   const that = this
    //   const reader = new FileReader()
    //   reader.readAsBinaryString(param.file)
    //   reader.onload = function(e) {
    //     const workbook = XLSX.read(e.target.result, { type: 'binary' })
    //     const sheetList = workbook.SheetNames
    //     const resultJson = []
    //     // 存放字符串数组格式的表格数据
    //     const resultFormulae = []
    //     sheetList.forEach(function(y) {
    //       const worksheet = workbook.Sheets[y]
    //       const json = XLSX.utils.sheet_to_json(workbook.Sheets[y])
    //       const formulae = XLSX.utils.sheet_to_formulae(workbook.Sheets[y])
    //       if (json.length > 0) {
    //         // 数据处理与存放
    //         resultJson.push(json)
    //         resultFormulae.push(formulae)
    //       }
    //     })
    //
    //     resultJson[0].shift()
    //     let importInfo = resultJson[0].map((item, index) => {
    //       if (!isNaN(item.__EMPTY_4)) {
    //         // const num = fixMoeny(item.__EMPTY_4 * 0.06)
    //         const num = fixMoeny(item.__EMPTY_4 * 0.06)
    //         return Math.round((num * 100))
    //       }
    //     })
    //     importInfo = importInfo.filter(current => {
    //       return current !== null && current !== undefined
    //     })
    //     console.log(importInfo)
    //     const result = importInfo.reduce((total, num) => {
    //       // return addNum(total,num)
    //       return total + num
    //       // return fixMoeny(total) + fixMoeny(num)
    //       // return Math.floor(total*100) + Math.floor(num*100)
    //     }, 0)
    //     // that.xlsxTotalMoney = fixMoeny(result)
    //     that.xlsxTotalMoney = result / 100
    //   }
    // }
  }
}
</script>
<style scoped lang="scss">
  .notice {
    background: #fdf6ec;
    margin-bottom: 20px;
    color: #E6A23C;
    padding: 15px 20px;
    border-radius: 5px;
    font-size: 12px;
    display: flex;

    .notice-left {
      width: 50px;
      font-size: 30px;
      margin-top: 2px;
    }

    .notice-right {
      flex: 1;
    }

    .font {
      margin: 0;
      line-height: 20px;
    }
  }

  .bank-list{
    display: flex;
    margin-top: 40px;
    flex-flow: row wrap;
    align-items: center;
    .bank-name{
      font-size: 14px;
      font-weight: bold;
    }
    .totalVisit{
      font-size: 36px;
      font-weight: bold;
      color: #f56c6c;
    }
    .list-card{
      margin-bottom: 20px;
      margin-right: 10px;
      max-width: 300px;
      width: 100%;
    }
  }

  .auto-width{
    width: 100%;
  }

  .down-model{
    margin: 0;
    line-height: 24px;
    padding-left: 15px;
    cursor: pointer;
    a{
      color: #F54343!important;
    }
    a:hover{
      text-decoration: underline;
    }
  }

</style>
