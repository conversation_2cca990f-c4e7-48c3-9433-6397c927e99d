<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form ref="formSearch" :inline="true" :model="formData" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="开始时间" prop="createTimeFrom" class="form-items">
                    <el-date-picker
                      v-model="formData.createTimeFrom"
                      clearable
                      type="datetime"
                      placeholder="选择开始时间"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="结束时间" prop="createTimeTo" class="form-items">
                    <el-date-picker
                      v-model="formData.createTimeTo"
                      clearable
                      type="datetime"
                      placeholder="选择结束时间"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="8" :xs="24">
                  <el-form-item label="结算类型" prop="tradeType" class="form-items">
                    <el-select v-model="formData.tradeType" clearable placeholder="请选择结算类型" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option label="充值" value="1" />
                      <el-option label="退款" value="2" />
                      <el-option label="结算" value="5" />
                      <el-option label="结算退回" value="6" />
                      <el-option label="调账" value="7" />
                      <el-option label="提现" value="8" />
                      <el-option label="提现退回" value="9" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="代征主体" prop="levyId" class="form-items">
                    <el-select v-model="formData.levyId" clearable placeholder="请选择代征主体" class="auto-width" @change="queryChannelInfo">
                      <el-option label="全部" value="" />
                      <el-option
                        v-for="item in mainOpt"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="打款通道" prop="channelId" class="form-items">
                    <el-select v-model="formData.channelId" clearable placeholder="请选择打款通道" class="auto-width">
                      <el-option label="全部" value="" />
                      <el-option
                        v-for="item in channelOpt"
                        :key="item.id"
                        :label="item.channelName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="7" :xs="24">
                  <el-form-item label="商户编号" prop="merNo" class="form-items">
                    <el-input v-model="formData.merNo" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="3" :xs="24">
                  <div style="text-align: right">
                    <el-button icon="el-icon-search" size="mini" type="primary" @click="onQuery">查询</el-button>
                    <!--                    <el-button class="xzb-btn xzb-sub-btn-style" @click="resetForm('formSearch')">重置</el-button>-->
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>
      </el-card>

      <el-card>
        <el-table
          :data="listData"
          border
          stripe
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merNo"
            label="商户编号"
            fixed
            min-width="160"
          />
          <el-table-column
            prop="merchantName"
            label="企业名称"
            width="200"
          />
          <el-table-column
            prop="accountNo"
            label="账户编号"
            min-width="150"
          />
          <el-table-column
            prop="levyBodyName"
            label="主体名称"
            min-width="220"
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            min-width="140"
          />
          <el-table-column
            label="结算类型"
            min-width="100"
          >
            <template slot-scope="scope">
              {{ scope.row.tradeType==1?'充值':(scope.row.tradeType==2?'退款':(scope.row.tradeType==5?'结算':(scope.row.tradeType==6?'结算退回':(scope.row.tradeType==7?'调账':(scope.row.tradeType==8?'提现':(scope.row.tradeType==9?'提现退回':'未知')))))) }}
            </template>
          </el-table-column>
          <el-table-column
            label="资金变更方向"
            min-width="100"
          >
            <template slot-scope="scope">
              {{ scope.row.accountChangeType==1?'收入':(scope.row.accountChangeType==2?'支出':(scope.row.accountChangeType==3?'冻结':(scope.row.accountChangeType==4?'解冻':'未知'))) }}
            </template>
          </el-table-column>
          <el-table-column
            label="交易金额"
            min-width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.accountChangeType==1 || scope.row.accountChangeType==4" style="color: rgb(251,0,11)">
                +{{ scope.row.trxAmount }}
              </span>
              <span v-else-if="scope.row.accountChangeType==2 || scope.row.accountChangeType==3" style="color: rgb(89,141,9)">
                -{{ scope.row.trxAmount }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="fee"
            label="服务费"
            min-width="100"
          />
          <el-table-column
            prop="nowCanBalance"
            label="账户余额"
            min-width="100"
          />
          <el-table-column
            prop="userRemark"
            label="备注"
            min-width="220"
          />
          <el-table-column
            prop="createTime"
            label="	时间"
            fixed="right"
            width="140"
          />
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>
  </div>
</template>
<script>
import financial from '@/axios/default/financial'
import Pagination from '@/components/Pagination'
import publicApi from '@/axios/default/public'
import moment from 'moment'
import { newAlert } from '@/utils'

export default {
  name: 'AccountHis',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,

      formData: {
        createTimeFrom: new Date(moment(new Date()).startOf('day')),
        createTimeTo: new Date(moment(new Date()).endOf('day')),
        levyId: '',
        channelId: '',
        tradeType: '',
        merNo: ''
      },

      mainOpt: [], // 代征主体
      channelOpt: [], // 打款通道

      listData: []
    }
  },
  activated() {
    this.list()
  },
  mounted() {
    // 代征主体
    publicApi.queryLevyBodyInfos().then(res => {
      if (res.data.code === '0000') {
        this.mainOpt = res.data.data
      }
    })
  },
  methods: {
    moment,
    onQuery() {
      this.pageNum = 1
      this.list()
    },

    list() {
      this.formData.createTimeFrom = moment(this.formData.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
      this.formData.createTimeTo = moment(this.formData.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
      financial.queryAccHistoryByPage({
        'pageSize': this.pageSize,
        'pageNum': this.pageNum,
        ...this.formData
      }).then(res => {
        if (res.data.code === '0000') {
          const data = { ...res.data.data }
          this.listData = data.rows
          this.pageNum = data.pageNum
          this.pageSize = data.pageSize
          this.total = data.total
        }
      })
    },
    handleSizeChange(data) {
      this.pageSize = data.pageSize
      this.pageNum = data.pageNum
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    },
    initSearchTime() {
      const date = new Date()
      const start = new Date(date.getFullYear(), parseInt(date.getMonth()), 1)
      // const end = new Date(date.setDate(date.getDate()))
      const end = new Date()
      return [start, end]
    },

    // 商户渠道
    queryChannelInfo(levyBodyId) {
      if (levyBodyId) {
        publicApi.queryChannelInfoByLevyId({ levyBodyId }).then(res => {
          this.channelOpt = res.data.data
          this.$set(this.formData, 'channelId', '')
        })
      } else {
        this.channelOpt = []
        this.$set(this.formData, 'channelId', '')
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }
  .form-items{
    width: 100%;
    display: flex;
  }
  .auto-width{
    width: 100%;
  }

  .page-layout{
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  /deep/ .el-date-editor.el-input {
    width: 100% !important;
  }
</style>

