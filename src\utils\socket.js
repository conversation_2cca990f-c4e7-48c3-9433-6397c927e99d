import { Notification } from 'element-ui'
import store from '@/store/index'
const socket = {
  websocket: null,
  connection() {
    const id = store.state.account.user.userId
    this.websocket = new WebSocket(`${process.env.VUE_APP_BASE_WS}/message/ist/${id}`)
    console.log(this.websocket)
  },
  onopen() {
    this.websocket.onopen = function(event) {
      console.log('链接socket')
    }
  },
  getMessage() {
    this.websocket.onmessage = function(event) {
      const message = JSON.parse(event.data)
      if (message.textMessage) {
        Notification({
          title: '系统提示',
          message: message.textMessage,
          duration: 10000
        })
      }
    }
  },
  onclose() {
    this.websocket.onclose = function(event) {
      // connection()
      console.log('断开socket')
    }
  },
  close() {
    this.websocket.close()
  }
}

export default socket
