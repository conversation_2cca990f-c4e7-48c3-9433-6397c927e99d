// 代理
import request from '@/axios/default/request'
import { API_system as API_Header } from '../index'

const api = {
  addAgentGrade: `${API_Header}/agentGrade/addAgentGrade`, // 新增代理级别
  list: `${API_Header}/agentGrade/list`, // 代理级别列表
  getAgentGrade: `${API_Header}/agentGrade/getAgentGrade`, // 根据id查询代理级别
  updateAgentGrade: `${API_Header}/agentGrade/updateAgentGrade`, // 修改代理级别
  addAgentInfo: `${API_Header}/agentInfo/addAgentInfo`, // 新增代理
  agentInfoList: `${API_Header}/agentInfo/list`, // 代理列表
  updateAgentInfo: `${API_Header}/agentInfo/updateAgentInfo`, // 修改代理
  getAgentInfo: `${API_Header}/agentInfo/getAgentInfo`, // 根据id查询代理
  updateStatus: `${API_Header}/agentInfo/updateStatus`, // 开通禁用代理
  getAllAgentGrades: `${API_Header}/agentGrade/getAllAgentGrades`, // 查询所有代理级别
  getAgentGradeByParentId: `${API_Header}/agentGrade/getAgentGradeByParentId`, // 根据代理级别查询上级代理
  allList: `${API_Header}/agentInfo/allList`, // 代理列表（全部）
  agentDownLoadData: `${API_Header}/agentInfo/agentDownLoadData`, // 代理列表（全部）
  agentDownNewData: `${API_Header}/agentInfo/agentDownNewData` // 代理关系（全部）

}

const agentGrade = {
  addAgentGrade: params => {
    return request.postJson(api.addAgentGrade, params)
  },
  list: params => {
    return request.postJson(api.list, params)
  },
  getAgentGrade: params => {
    return request.postJson(api.getAgentGrade, params)
  },
  updateAgentGrade: params => {
    return request.postJson(api.updateAgentGrade, params)
  },
  addAgentInfo: params => {
    return request.postJson(api.addAgentInfo, params)
  },
  agentInfoList: params => {
    return request.postJson(api.agentInfoList, params)
  },
  updateAgentInfo: params => {
    return request.postJson(api.updateAgentInfo, params)
  },
  getAgentInfo: params => {
    return request.postJson(api.getAgentInfo, params)
  },
  updateStatus: params => {
    return request.postJson(api.updateStatus, params)
  },
  getAllAgentGrades: params => {
    return request.postJson(api.getAllAgentGrades, params)
  },
  getAgentGradeByParentId: params => {
    return request.postJson(api.getAgentGradeByParentId, params)
  },
  allList: params => {
    return request.postJson(api.allList, params)
  },
  agentDownLoadData: (params, filename, type) => {
    return request.downFiles(api.agentDownLoadData, params, filename, type)
  },
  agentDownNewData: (params, filename, type) => {
    return request.downFiles(api.agentDownNewData, params, filename, type)
  }
}

export default agentGrade

