<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="发布时间" class="form-items"> <el-date-picker
                    v-model="pickTime"
                    class="auto-width"
                    type="daterange"
                    align="right"
                    unlink-panels
                    value-format="yyyy-MM-dd"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :picker-options="pickerOptions"
                    @change="resetTime"
                  />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="平台" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :xs="24">
                  <el-form-item label="通知账号" class="form-items">
                    <el-input v-model="formData.accountName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-download" plain type="primary" @click="openbox">添加站内信</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="name"
            label="站内信标题"
          />
          <el-table-column
            prop="content"
            label="站内信内容"
          />
          <el-table-column
            prop="type"
            label="消息类型"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
          />
          <el-table-column
            label="操作"
          >
            <template slot-scope="scope">
              修改
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    添加站内信-->
    <el-dialog title="添加站内信" :visible.sync="dialogMain" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form :model="dialogMainData" size="mini" class="form-style" label-width="90px">
          <el-form-item label="平台">
            <el-select v-model="dialogMainData.receiveScopeType" placeholder="请选择活动区域" class="auto-width">
              <el-option label="运营平台" :value="0" />
              <el-option label="商户平台" :value="1" />
              <el-option label="代理平台" :value="2" />
            </el-select>
          </el-form-item>

          <el-form-item label="站内信名称">
            <el-input v-model="dialogMainData.name" placeholder="请输入内容" />
          </el-form-item>

          <el-form-item label="站内信内容">
            <el-input v-model="dialogMainData.content" type="textarea" :rows="4" placeholder="请输入内容" />
          </el-form-item>

          <el-form-item label="通知时间">
            <el-date-picker
              v-model="pickTime"
              class="auto-width"
              type="daterange"
              align="right"
              unlink-panels
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :picker-options="pickerOptions"
              @change="resetTime"
            />
          </el-form-item>

          <el-form-item label="" class="form-items">
            <el-button type="primary" @click="addMessage">保存</el-button>
            <el-button @click="dialogMain=false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import message from '@/axios/default/message'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Message',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        createTimeFrom: '',
        createTimeTo: '',
        type: '3'
      },
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogMain: false,
      dialogMainData: {}
    }
  },
  computed: {
    user() {
      return this.$store.state.account.user
    }
  },
  mounted() {
    this.list()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      message.findNoticeByUserId({
        receiveScopes: this.user.userId,
        type: this.formData.type,
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          // this.total = res.data.data.total
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetTime(val) {
      val = val || ['', '']
      this.formData.createTimeFrom = val[0]
      this.formData.createTimeTo = val[1]
    },
    openbox() {
      this.dialogMain = true
      this.dialogMainData = this.initDialog()
    },
    initDialog() {
      return {

      }
    },
    addMessage() {

    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }

</style>
