$bg:#F1F4FA;
$light_gray:#fff;
$cursor: #555;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}
.login-container {
  .login_user_input {
    position: relative;
    .img-icon {
      position: absolute;
      z-index: 100;
      top: 10px;
      height: 20px;
      padding:0 14px;
      border-right: 1px solid #808191;;
      display: flex;
      align-items: center;
      >img {
        width: 16px;
        height: 16px;
      }
    }

    input {
      padding-left: 50px;
    }
    .el-form-item__error {
      color: #FF7A68;
    }
    /*padding-left: 88px;*/
  }
  .el-input {
    display: inline-block;
    font-size: .9rem !important;
    input {
      background: #F1F4FA;
      border: 0;
      border: none;
      -webkit-appearance: none;
      border-radius: 0;
      height: 40px;
      font-size: 13px;
    }


  }

  .el-form-item {
    border: none;
    border-radius: 2px;
    color: #454545;
    transition: all .3s;
    &:hover {
      border-color: #57a3f3;
    }
  }
}
