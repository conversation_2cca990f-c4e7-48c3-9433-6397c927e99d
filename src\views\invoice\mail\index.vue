<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="10" :xs="24">
                  <el-form-item label="开票申请时间" class="form-items">
                    <el-date-picker
                      v-model="pickTime"
                      class="auto-width"
                      type="daterange"
                      align="right"
                      unlink-panels
                      value-format="yyyy-MM-dd"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :picker-options="pickerOptions"
                    />
                  </el-form-item>
                </el-col>

                <el-col :lg="6" :xs="24">
                  <el-form-item label="是否预开" class="form-items">
                    <el-select v-model="formData.invPreopen" placeholder="请选择是否预开" class="auto-width">
                      <el-option label="请选择" value="" />
                      <el-option label="是" value="1" />
                      <el-option label="否" value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="代征主体" class="form-items">
                    <el-select v-model="formData.levyId" placeholder="请选择" class="auto-width">
                      <el-option v-if="levyBodyIdArr.length>1" label="请选择" value="" />
                      <el-option
                        v-for="item in levyBodyIdArr"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          stripe
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merName"
            label="企业名称"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="invoiceTypeName"
            label="发票类目"
            width="300"
            show-overflow-tooltip
          />
          <el-table-column
            prop="invAmount"
            label="开票金额"
            width="110"
          />
          <el-table-column
            prop="invQuantity"
            label="开票数量（张）"
            width="110"
          />
          <el-table-column
            prop="createTime"
            label="发票申请时间"
            width="150"
          />
          <!--          <el-table-column-->
          <!--            prop="fileSize"-->
          <!--            label="开票状态"-->
          <!--          />-->
          <el-table-column
            prop="levyName"
            label="开票方"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="auditRemark"
            show-overflow-tooltip
            label="审核情况"
          />

          <el-table-column
            prop="invCategory"
            label="开票类型"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invCategory == '1'" class="invoice-ordinary">普票</span>
              <span v-if="scope.row.invCategory == '2'" class="invoice-specially">专票</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="invPreopen"
            label="是否预开"
            width="70"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.invPreopen == '1'" style="color: #f54343">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="操作"
            width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="jumpDetail(scope.row.id)">查看详情</el-button>
              <el-button type="text" @click="tomail(scope.row)">邮寄</el-button>
              <el-button type="text" @click="toAudit(scope.row.id)">退回审核</el-button>
              <!-- <el-button type="text" @click="toRedFlush(scope.row)">红冲发票</el-button> -->

            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <!--    发票邮寄-->
    <el-dialog title="发票邮寄" :visible.sync="dialogA" width="40%" class="reset-dialog reset-dialog-small " @close="clickbox">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogAData" :model="dialogAData" label-width="100px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="商户名称" class="form-items">
            <el-input v-model="dialogAData.merName" disabled />
          </el-form-item>
          <el-form-item label="开票金额" class="form-items">
            <el-input v-model="dialogAData.invAmount" disabled />
          </el-form-item>
          <el-form-item label="开票类目" class="form-items">
            <el-input v-model="dialogAData.invoiceTypeName" type="textarea" disabled />
          </el-form-item>
          <el-form-item label="是否预开" class="form-items">
            {{ dialogAData.invPreopen == '1' ? '是' : '否' }}
          </el-form-item>

          <el-form-item label="初审备注" class="form-items">
            <el-input v-model="trialauditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="复审备注" class="form-items">
            <el-input v-model="reviewauditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="审核备注" class="form-items">
            <el-input v-model="auditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="快递单号" class="form-items" prop="trackNumber">
            <el-input v-model="dialogAData.trackNumber" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="上传开票" class="form-items" prop="fileList">
            <el-upload
              ref="uploaderkp"
              action=""
              multiple
              accept="image/*"
              list-type="picture-card"
              :on-remove="handleRemove"
              :http-request="uploader"
              :file-list="imgForm.fileList"
            >
              <i slot="default" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogAData')">保存</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>




        <!--    发票冲红-->
        <el-dialog title="红冲发票" :visible.sync="dialogB" width="40%" class="reset-dialog reset-dialog-small " @close="clickbox">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogBData" :model="dialogBData" label-width="100px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="商户名称" class="form-items">
            <el-input v-model="dialogBData.merName" disabled />
          </el-form-item>
          <el-form-item label="开票金额" class="form-items">
            <el-input v-model="dialogBData.invAmount" disabled />
          </el-form-item>
          <el-form-item label="开票类目" class="form-items">
            <el-input v-model="dialogBData.invoiceTypeName" type="textarea" disabled />
          </el-form-item>
          <el-form-item label="是否预开" class="form-items">
            {{ dialogBData.invPreopen == '1' ? '是' : '否' }}
          </el-form-item>

          <el-form-item label="初审备注" class="form-items">
            <el-input v-model="trialauditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="复审备注" class="form-items">
            <el-input v-model="reviewauditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="审核备注" class="form-items">
            <el-input v-model="auditRemark" type="textarea" :rows="2" placeholder="请输入内容" disabled />
          </el-form-item>

          <el-form-item label="红冲备注" class="form-items" prop="redFlushRemake">
            <el-input v-model="dialogBData.redFlushRemake" type="textarea" :rows="2" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="上传开票" class="form-items" prop="fileList">
            <el-upload
              ref="uploaderkp"
              action=""
              multiple
              accept="image/*"
              list-type="picture-card"
              :on-remove="handleRemove"
              :http-request="uploader"
              :file-list="imgForm.fileList"
            >
              <!-- <i slot="default" class="el-icon-plus" /> -->
            </el-upload>
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submit('dialogBData')">保存</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import invoiceType from '@/axios/default/invoiceType'
import publicApi from '@/axios/default/public'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      pickTime: '',
      formData: {
        'createTimeFrom': '',
        'createTimeTo': '',
        'auditState': '7',
        'merName': '',
        'invPreopen': '',
        'levyId': ''
      },
      levyBodyIdArr: [],
      formLabelWidth: '180px',
      listData: [],
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogA: false,
      dialogAData: {
        trackNumber: '',
        fileList: ''
      },
      dialogB: false,
      dialogBData: {
        trackNumber: '',
        fileList: ''
      },
      trialauditRemark: '',
      reviewauditRemark: '',
      auditRemark: '',
      trackNumber: '',
      rules: {
        trackNumber: [{ required: true, message: '请填写快递单号', trigger: 'blur' }]
      },
      btnLoading: false,
      imgForm: {
        fileList: [],
        ID: []
      }
    }
  },
  mounted() {
    this.queryLevyBodyInfos()
    this.queryInvoiceInfo()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.queryInvoiceInfo()
    },
    //   列表
    queryInvoiceInfo() {
      invoiceType.queryInvoiceInfo({
        'request': {
          'pageSize': this.pageSize,
          'pageNum': this.pageNum
        },
        'InvoiceInfo': {
          'createTimeFrom': this.pickTime[0],
          'createTimeTo': this.pickTime[1],
          'auditState': this.formData.auditState,
          'merName': this.formData.merName,
          'invPreopen': this.formData.invPreopen,
          'levyId': this.formData.levyId
        }
      }).then(res => {
        this.listData = res.data.data.rows
        this.total = res.data.data.total
        this.pageSize = res.data.data.pageSize
        this.pageNum = res.data.data.pageNum
      })
    },
    // 打开复审框
    tomail(data) {
      this.dialogA = true
      this.dialogAData = data
      this.dialogAData.trackNumber = ''

      this.imgForm.fileList = data.listAtt.map(res => {
        return { url: res.filePath, id: res.id }
      })

      this.queryInvoiceAuditRecord(data.id)
    },
    toRedFlush(data) {
      this.dialogB = true
      this.dialogBData = data

      this.imgForm.fileList = data.listAtt.map(res => {
        return { url: res.filePath, id: res.id }
      })

      this.queryInvoiceAuditRecord(data.id)
    },
    // 退回终审
    toAudit(id) {
      let invoiceId = id
      debugger
      invoiceType.invoiceToAudit({invoiceId}).then(res => {
          if (res.data.code == '0000') {
            this.$message.success('操作成功')
            window.location.reload();          
          } else {
            newAlert(this.$tips, res.data.message)
          }
        })
    },

    // 提交审核
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          const formData = new FormData()
          const data = this.imgForm.fileList.filter(res => res.url && res.url.indexOf('blob') !== -1)
          for (let i = 0; i < data.length; i++) {
            formData.append('files', data[i].files)
          }
          const ID = [...new Set(this.imgForm.ID)].join(',')
          formData.append('id', this.dialogAData.id)
          formData.append('attId', ID)
          formData.append('trackNumber', this.dialogAData.trackNumber)
          invoiceType.updateInvoiceInfoById(formData).then(res => {
            this.btnLoading = false
            this.$refs[formName].clearValidate()
            this.$refs[formName].resetFields()
            if (res.data.code == '0000') {
              this.imgForm.fileList = []
              this.dialogA = false
              this.$message({ type: 'success', message: '操作成功' })
              this.queryInvoiceInfo()
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 分页
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryInvoiceInfo()
    },
    queryInvoiceAuditRecord(id) {
      invoiceType.queryInvoiceAuditRecord({
        invId: id
      }).then(res => {
        this.trialauditRemark = res.data.data.listRec[0].auditReason
        this.reviewauditRemark = res.data.data.listRec[1].auditReason
        this.auditRemark = res.data.data.listRec[2].auditReason
      })
    },
    // 查看详情
    jumpDetail(id) {
      this.$router.push({
        path: '/invoice/detail',
        query: {
          origin: 'mail',
          id
        }
      })
    },
    clickbox() {
      this.imgForm.fileList = []
      this.$refs.uploaderkp.clearFiles()
    },
    // 删除更新图片列表
    handleRemove(file, fileList) {
      if (file.url.indexOf('blob') === -1) {
        // 删除上传的对象
        console.log('url')
        this.imgForm.ID.push(file.id)
      } else {
        // 删除url对象
        console.log('blob')
      }

      this.imgForm.fileList = fileList.map(res => {
        return res
      })
    },
    // 上传更新图片列表
    uploader(param) {
      const _URL = window.URL || window.webkitURL
      const fileName = param.file.uid
      const imgObj = {
        url: URL.createObjectURL(param.file),
        uid: fileName,
        files: param.file
      }
      this.imgForm.fileList.unshift(imgObj)
    },
    queryLevyBodyInfos() {
      publicApi.queryLevyBodyInfos().then(res => {
        if (res.data.code === '0000') {
          this.levyBodyIdArr = res.data.data
          if (this.levyBodyIdArr.length === 1) {
            this.formData.levyId = this.levyBodyIdArr[0].id
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    }

  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  /deep/ .el-form-item__content{
    margin-left: 0!important;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .invoice-ordinary{
    color: #f29c39;
  }
  .invoice-specially{
    color: #5791fd;
  }
</style>
