<template>
  <div :class="{'has-logo':showLogo}">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="true"
        mode="vertical"
        @select="menuResetPage"
      >

        <sidebar-item v-for="route in userRoutes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  computed: {
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.setting.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.$store.state.setting.sidebar.opened
    },
    userRoutes() {
      return this.$store.state.account.routes
    }
  },
  methods: {
    // 点击刷新页面
    menuResetPage(key, keyPath) {
      // return
      const url = this.$route.fullPath
      if (key === url) {
        this.$router.replace({
          path: '/redirect' + url
        })
      }
      this.$route.meta.noCache = true
    }
  }
}
</script>
