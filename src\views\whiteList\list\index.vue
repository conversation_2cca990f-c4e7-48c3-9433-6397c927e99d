<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="6" :xs="24">
                  <el-form-item label="商户名称" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
                <el-col :lg="18" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                    <el-button icon="el-icon-add" type="primary" @click="addList">新增</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merchantName"
            label="商户名称"
            width="200"
          />
          <el-table-column
            prop="levyBodyName"
            label="代征主体"
            width="200"
          />
          <el-table-column
            prop="channelName"
            label="通道名称"
            width="200"
          />
          <el-table-column
            prop="ip"
            width="200"
            label="ip地址"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="150"
          />

          <el-table-column
            prop=""
            fixed="right"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="editData(scope.row.id)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog :title="dialogFormTitle" :visible.sync="dialogFormVisible" width="30%" class="reset-dialog">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" :label-width="formLabelWidth" size="mini" class="form-style" :rules="rules">

          <el-form-item label="商户名称" prop="merchantId">
            <el-select
              v-model="dialogData.merchantId"
              class="auto-width"
              filterable
              remote
              placeholder="请输入关键词"
              :remote-method="mccList"
              :loading="loading"
              @change="getlevyId"
            >
              <el-option v-for="(item,index) in merNameArr" :key="index" :label="item.merName" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="代征主体" prop="levyBodyId">
            <el-select v-model="dialogData.levyBodyId" placeholder="请选择" class="auto-width" @change="getChannel">
              <el-option
                v-for="(item,index) in levyIdArr"
                :key="item.index"
                :label="item.NAME"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="通道" prop="channelId">
            <el-select v-model="dialogData.channelId" placeholder="请选择" class="auto-width">
              <el-option
                v-for="item in channelIdArr"
                :key="item.id"
                :label="item.channel_name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否启用" prop="status">
            <el-radio v-model="dialogData.status" :label="1">启用</el-radio>
            <el-radio v-model="dialogData.status" :label="0">不启用</el-radio>
          </el-form-item>
          <el-form-item label="IP" prop="ip">
            <el-input v-model="dialogData.ip" type="textarea" :autosize="{minRows:2}" autocomplete="off" />
            ip地址多个请用，隔开
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button :loading="btnLoading" type="primary" @click="submitData('dialogData')">保存</el-button>
              <el-button @click="dialogFormVisible = false">取消</el-button>
            </div>
          </el-form-item>

        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import whiteList from '@/axios/default/whiteList'
import publics from '@/axios/default/public'
import { newAlert } from '@/utils'
// import moment from 'moment'
import Pagination from '@/components/Pagination'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      dialogFormVisible: false,
      dialogData: this.initDialogData(),
      formData: {
        merchantName: ''
      },
      merNameArr: [],
      levyIdArr: [],
      channelIdArr: [],
      listData: [],
      loading: false,
      formLabelWidth: '100px',
      dialogFormTitle: '',
      rules: {
        merchantId: [{ required: true, message: '请填写商户', trigger: 'blur' }],
        levyBodyId: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        channelId: [{ required: true, message: '请选择通道', trigger: 'change' }],
        ip: [{ required: true, message: '请填写IP', trigger: 'blur' }]
      },
      btnLoading: false
    }
  },
  created() {
    this.pageNum = this.$route.query.page ? Number(this.$route.query.page) : 1
  },
  mounted() {
    this.list()
    this.initAddRechInfo({ queryType: '1', merName: '' }, 'merNameArr')
  },
  methods: {
    // moment,
    onSearch() {
      this.pageNum = 1
      this.list()
    },
    list() {
      whiteList.whiteList({
        pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
        pageNum: this.pageNum,
        ...this.formData
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    initDialogData() {
      return {
        merchantId: '',
        merchantName: '',
        levyBodyId: '',
        levyBodyName: '',
        channelId: '',
        channelName: '',
        status: 1,
        ip: ''
      }
    },
    initAddRechInfo(data, item) {
      publics.initAddRechInfo(data).then(res => {
        if (res.data.code === '0000') {
          this.loading = false
          this[item] = res.data.data
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 商户
    mccList(query) {
      if (query !== '') {
        this.loading = true
        this.initAddRechInfo({ queryType: '1', merName: query }, 'merNameArr')
      } else {
        this.merNameArr = []
      }
    },
    // 主体
    getlevyId() {
      this.dialogData.levyBodyId = ''
      this.dialogData.channelId = ''
      this.$nextTick(function() {
        this.$refs.dialogData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '2', merId: this.dialogData.merchantId }, 'levyIdArr')
    },
    // 通道
    getChannel() {
      this.formData.channelId = ''
      this.$nextTick(function() {
        this.$refs.dialogData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '3', merId: this.dialogData.merchantId, levyId: this.dialogData.levyBodyId }, 'channelIdArr')
    },

    addList() {
      this.dialogFormTitle = '添加白名单'
      this.dialogFormVisible = true
      this.dialogData = this.initDialogData()
      this.$nextTick(function() {
        this.resetForm('dialogData')
      })
    },
    editData(id) {
      this.dialogFormTitle = '修改白名单'
      this.dialogFormVisible = true
      this.getOneWhiteList(id)
    },
    getOneWhiteList(id) {
      whiteList.getOneWhiteList({
        id
      }).then(res => {
        if (res.data.code === '0000') {
          this.dialogData = res.data.data
          this.initAddRechInfo({ queryType: '2', merId: this.dialogData.merchantId }, 'levyIdArr')
          this.initAddRechInfo({ queryType: '3', merId: this.dialogData.merchantId, levyId: this.dialogData.levyBodyId }, 'channelIdArr')
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    submitData(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          if (this.dialogFormTitle === '修改白名单') {
            // 编辑
            this.whiteListUpdate()
          } else {
            // 添加
            this.whiteListAdd()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    whiteListAdd() {
      this.dialogData.merchantName = this.merNameArr.find(res => res.id == this.dialogData.merchantId).merName
      this.dialogData.levyBodyName = this.levyIdArr.find(res => res.id == this.dialogData.levyBodyId).NAME
      this.dialogData.channelName = this.channelIdArr.find(res => res.id == this.dialogData.channelId).channel_name
      whiteList.whiteListAdd({
        ...this.dialogData
      }).then(res => {
        this.btnLoading = false
        this.resetForm('dialogData')
        if (res.data.code === '0000') {
          this.$message.success('操作成功')
          this.dialogFormVisible = false
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    whiteListUpdate() {
      this.dialogData.merchantName = this.merNameArr.find(res => res.id == this.dialogData.merchantId).merName
      this.dialogData.levyBodyName = this.levyIdArr.find(res => res.id == this.dialogData.levyBodyId).NAME
      this.dialogData.channelName = this.channelIdArr.find(res => res.id == this.dialogData.channelId).channel_name
      whiteList.whiteListUpdate({
        ...this.dialogData
      }).then(res => {
        this.btnLoading = false
        this.resetForm('dialogData')
        if (res.data.code === '0000') {
          this.$message.success('操作成功')
          this.dialogFormVisible = false
          this.list()
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.list()
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate()
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

</style>
