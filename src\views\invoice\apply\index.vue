<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row>
          <el-col :lg="14" :xs="24">
            <el-form ref="formData" label-width="150px" label-position="right" :model="formData" :rules="rules" size="mini">
              <el-form-item label="企业名称" class="form-items" prop="merId">
                <!--                <el-select v-model="formData.merId" placeholder="请选择" class="auto-width" @change="getFormInfo">-->
                <!--                  <el-option-->
                <!--                    v-for="(item,index) in merIdArr"-->
                <!--                    :key="item.index"-->
                <!--                    :label="item.merName"-->
                <!--                    :value="item.id"-->
                <!--                  />-->
                <!--                </el-select>-->

                <el-select
                  v-model="formData.merId"
                  class="auto-width"
                  filterable
                  remote
                  placeholder="请输入关键词"
                  :remote-method="mccList"
                  :loading="loading"
                  @change="getFormInfo"
                >
                  <el-option v-for="(item,index) in merIdArr" :key="index" :label="item.merName" :value="item.id" />
                </el-select>

              </el-form-item>
              <el-form-item label="代征主体" class="form-items" prop="levyId">
                <el-select v-model="formData.levyId" placeholder="请选择代征主体" class="auto-width" @change="queryInvoiceTypeByMerId">
                  <el-option v-for="(item,index) in levyArr" :key="item.id" :label="item.NAME" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="开票信息" class="form-items" prop="InvInfo">
                <el-input
                  v-model="formData.InvInfo"
                  type="textarea"
                  disabled
                  :autosize="{ minRows: 4, maxRows: 6}"
                  placeholder="请输入内容"
                />
              </el-form-item>
              <el-form-item label="邮寄人" class="form-items" prop="conPerson">
                <el-input v-model="formData.conPerson" placeholder="请输入内容" clearable />
              </el-form-item>
              <el-form-item label="邮寄电话" class="form-items" prop="conTelephone">
                <el-input v-model="formData.conTelephone" placeholder="请输入内容" clearable />
              </el-form-item>
              <el-form-item label="邮寄地址" class="form-items" prop="conAddress">
                <el-input v-model="formData.conAddress" placeholder="请输入内容" clearable />
              </el-form-item>
              <el-form-item label="可开票金额" class="form-items" prop="InvInfoMoney">
                <el-input v-model="formData.InvInfoMoney" type="text" placeholder="0" disabled>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
              <el-form-item label="开票金额" class="form-items" prop="invAmount">
                <el-input v-model="formData.invAmount" type="text" placeholder="请输入内容" clearable />
              </el-form-item>
              <el-form-item label="开票类型" class="form-items" prop="invCategory">
                <el-radio v-model="formData.invCategory" label="2" style="color: #5791fd">专票</el-radio>
                <el-radio v-model="formData.invCategory" label="1" style="color: #f29c39">普票</el-radio>
              </el-form-item>
              <el-form-item label="开票类目" class="form-items" prop="billTypes">
                <el-checkbox-group v-model="formData.billTypes" class="category-style">
                  <el-checkbox v-for="(item,index) in billTypesArr" :key="index" :label="item.id">{{ item.typeName }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item class="form-items" label="">
                <div style="padding-left: 150px">
                  <el-button plain type="primary" :loading="btnLoading" @click="submit('formData')">提交审核</el-button>
                  <el-button plain type="primary" @click="resetForm">重置</el-button>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import invoiceType from '@/axios/default/invoiceType'
import publics from '@/axios/default/public'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  data() {
    return {
      formData: this.initForm(),
      invPreopen: '',
      levyArr: '',
      merIdArr: '',
      loading: false,
      rules: {
        merId: [{ required: true, message: '请选择企业名称', trigger: 'change' }],
        levyId: [{ required: true, message: '请选择代征主体', trigger: 'change' }],
        conPerson: [{ required: true, message: '请填写邮寄人', trigger: 'blur' }],
        conTelephone: [{ required: true, message: '请填写邮寄电话', trigger: 'blur' }],
        conAddress: [{ required: true, message: '请填写邮寄地址', trigger: 'blur' }],
        invAmount: [{ required: true, message: '请填写开票金额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        type1: [{ required: true, message: '请选择项目类型', trigger: 'blur' }],
        describe: [{ required: true, message: '请填写项目描述', trigger: 'blur' }],
        invCategory: [{ required: true, message: '请选择开票类型', trigger: 'change' }],
        billTypes: [{ required: true, message: '请选择开票类目', trigger: 'change' }]
      },
      taxNo: '',
      regAddr: '',
      taxMobile: '',
      taxOpenBank: '',
      taxAcc: '',
      billTypesArr: '',
      btnLoading: false
    }
  },
  mounted() {
    this.pageName()
    this.queryMerchantInfo()
    this.resetForm()
  },
  methods: {
    pageName() {
      const page = this.$route.name
      if (page == '开票申请') {
        this.invPreopen = 2
      } else if (page == '预开票申请') {
        this.invPreopen = 1
      }
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          invoiceType.insertInvoiceInfo({
            'invoice': {
              'merId': this.formData.merId,
              'merName': this.merIdArr.find(res => res.id == this.formData.merId).merName,
              'invAmount': this.formData.invAmount,
              'levyId': this.formData.levyId,
              'levyName': this.levyArr.find(res => res.id == this.formData.levyId).NAME,
              'invCategory': this.formData.invCategory,
              'invPreopen': this.invPreopen
            },
            'record': {
              'taxNo': this.taxNo,
              'regAddr': this.regAddr,
              'taxMobile': this.taxMobile,
              'taxOpenBank': this.taxOpenBank,
              'taxAcc': this.taxAcc,
              'conPerson': this.formData.conPerson,
              'conTelephone': this.formData.conTelephone,
              'conAddress': this.formData.conAddress
            },
            'billTypes': this.formData.billTypes.join(',')
          }).then(res => {
            if (res.data.code == '0000') {
              // this.formData = this.initForm()
              // this.$refs[formName].clearValidate()
              this.resetForm()
              this.billTypesArr = []
              this.$message({ type: 'success', message: '提交成功' })
            } else {
              newAlert(this.$tips, res.data.message)
            }
            this.btnLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    initForm() {
      return {
        'merId': '',
        InvInfo: '',
        InvInfoMoney: 0,
        'merName': '',
        'invAmount': '',
        'levyId': '',
        'levyName': '',
        'invCategory': '',
        'invPreopen': '',
        'merIdenNumber': '',
        'merAddress': '',
        'merTelephone': '',
        'merBank': '',
        'merBankCode': '',
        'conPerson': '',
        'conTelephone': '',
        'conAddress': '',
        'billTypes': []
      }
    },
    initAddRechInfo(data, item) {
      publics.initAddRechInfo(data).then(res => {
        if (res.data.code === '0000') {
          this.loading = false
          this[item] = res.data.data
          if (item == 'levyArr') {
            if (this[item].length === 1) {
              this.formData.levyId = this[item][0].id
              this.queryInvoiceTypeByMerId(this.formData.levyId)
            }
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    mccList(query) {
      if (query !== '') {
        this.loading = true
        this.initAddRechInfo({ queryType: '1', merName: query }, 'merIdArr')
      } else {
        this.merIdArr = []
      }
    },
    // 查询商户开票信息
    getFormInfo(val) {
      this.formData.levyId = ''
      this.formData.InvInfoMoney = ''
      this.billTypesArr = []
      this.$nextTick(function() {
        this.$refs.formData.clearValidate()
      })
      this.initAddRechInfo({ queryType: '2', merId: val }, 'levyArr')
      invoiceType.queryMerchantInvInfo({
        merchantId: val,
        invPreopen: this.invPreopen
      }).then(res => {
        if (res.data.code == '0000') {
          if (res.data.data) {
            const { data } = res.data
            this.taxNo = data.taxNo
            this.regAddr = data.regAddr
            this.taxMobile = data.taxMobile
            this.taxOpenBank = data.taxOpenBank
            this.taxAcc = data.taxAcc
            this.formData.conAddress = data.contractPostAddr
            this.formData.conPerson = data.postName
            this.formData.conTelephone = data.postMobile
            this.formData.InvInfo = `名称：${data.merName || '暂无数据'}\n纳税人识别号：${this.taxNo || '暂无数据'}\n地址：${this.regAddr}\n电话：${this.taxMobile || '暂无数据'}\n开户行：${this.taxOpenBank || '暂无数据'}\n银行账号：${this.taxAcc}`
          } else {
            this.resetForm()
          }
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },
    // 可开票金额企业接口
    queryMerchantInfo() {
      invoiceType.queryMerchantInfo({
        invPreopen: this.invPreopen
      }).then(res => {
        this.merIdArr = res.data.data
      })
    },
    queryInvoiceTypeByMerId() {
      this.formData.InvInfoMoney = this.levyArr.find(res => res.id == this.formData.levyId).invoiceBalance
      invoiceType.queryInvoiceTypeByMerId({
        merId: this.formData.merId,
        levyId: this.formData.levyId
      }).then(res => {
        this.billTypesArr = res.data.data
      })
    },
    // // 查询主体
    // initAddRechInfo(id) {
    //   publics.initAddRechInfo({
    //     queryType: '2',
    //     merId: id
    //   }).then(res => {
    //     if (res.data.code == '0000') {
    //       this.levyArr = res.data.data
    //     } else {
    //                 this.$alert(res.data.message, '系统提示', {
    //         type: 'warning',
    //         showConfirmButton: false,
    //         iconClass: 'el-icon-my-alert'
    //       })
    //     }
    //   })
    // },
    resetForm() {
      this.$refs.formData.clearValidate()
      this.$refs.formData.resetFields()
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  /deep/ .el-form-item__content{
    margin-left: 0!important;
  }

  .el-checkbox-group{

  }
  .category-style{
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    .el-checkbox{
      flex: 1;
      margin: 0;
    }
  }

</style>
