{"name": "mohist", "version": "4.2.1", "description": "知汇灵工", "author": "知汇灵工", "license": "Apache 2.0", "scripts": {"download": "yarn --registry https://registry.npm.taobao.org || npm install --registry https://registry.npm.taobao.org", "dev": "vue-cli-service serve --mode development", "test": "vue-cli-service build --mode development", "pro": "vue-cli-service build --mode production", "preview": "node build/index.js --preview", "lint": "vue-cli-service lint", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": []}, "keywords": ["TaxPay", "vue", "admin", "dashboard", "element-ui", "management-system"], "dependencies": {"@fortawesome/fontawesome-free": "^5.15.1", "@riophae/vue-treeselect": "0.0.38", "axios": "^0.26.1", "clipboard": "2.0.4", "core-js": "^3.35.0", "default-passive-events": "^2.0.0", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "2.13.2", "font-awesome": "^4.7.0", "fuse.js": "3.4.4", "moment": "^2.27.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "showdown": "^1.9.1", "sortablejs": "1.8.4", "vue": "2.6.10", "vue-count-to": "^1.0.13", "vue-i18n": "7.3.2", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuex": "3.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "3.9.1", "@vue/cli-plugin-unit-jest": "^4.5.6", "@vue/cli-service": "^4.5.6", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "7.2.3", "babel-jest": "^26.3.0", "babel-types": "^6.26.0", "chalk": "2.4.2", "chokidar": "2.1.5", "compression-webpack-plugin": "^5.0.1", "connect": "3.6.6", "eslint": "6.8.0", "eslint-plugin-vue": "6.0.1", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "moment-locales-webpack-plugin": "^1.2.0", "node-sass": "^4.9.0", "plop": "2.3.0", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "umi-webpack-bundle-analyzer": "^3.6.0", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}