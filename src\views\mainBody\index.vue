<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20" style="margin-top: 40px">
                <el-col :span="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" plain type="primary" @click="addLevyBody">新增</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="name"
            width="200"
            label="代征主体企业名称"
          />
          <el-table-column
            prop="status"
            width="80"
            label="状态"
          >
            <template slot-scope="scope">
              {{ scope.row.status == "0" ? "未使用" : "使用中" }}
            </template>
          </el-table-column>
          <el-table-column
            width="140"
            label="代征主体企业所属地"
          >
            <template slot-scope="scope">
              {{ scope.row.province }}
            </template>
          </el-table-column>
          <el-table-column
            prop="comTaxRate"
            label="企业增值税率"
          >
            <template slot-scope="scope">
              {{ scope.row.comTaxRate }}%
            </template>
          </el-table-column>
          <el-table-column
            prop="addTaxRate"
            label="企业附加税率"
          >
            <template slot-scope="scope">
              {{ scope.row.addTaxRate }}%
            </template>
          </el-table-column>
          <el-table-column
            prop="personStartPoint"
            width="150"
            label="自由职业者个税起征点"
          />
          <el-table-column
            prop="orderNum"
            label="优先级"
          />
          <el-table-column
            prop="address"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="editLevyBody(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />
      </el-card>
    </div>

    <el-dialog :title="dialogFormTitle" :visible.sync="dialogFormVisible" width="50%" class="reset-dialog">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" :label-width="formLabelWidth" size="mini" class="form-style" :rules="rules">

          <el-form-item label="代征主体名称" prop="name">
            <el-input v-model="dialogData.name" autocomplete="off" placeholder="请选择主体名称" />
          </el-form-item>

          <el-form-item label="代征所属地" prop="province">
            <el-select v-model="dialogData.province" placeholder="请选择代征所属地" class="auto-width">
              <el-option v-for="(item,index) in regions" :key="index" :label="item.areaName" :value="item.areaName" />
            </el-select>
          </el-form-item>

          <el-form-item label="纳税识别号" prop="taxNum">
            <el-input v-model="dialogData.taxNum" autocomplete="off" placeholder="请填写纳税识别号" />
          </el-form-item>

          <el-form-item label="开户行名称" prop="bankOpenName">
            <el-input v-model="dialogData.bankOpenName" autocomplete="off" placeholder="请填写开户行名称" />
          </el-form-item>

          <el-form-item label="账号" prop="entNumber">
            <el-input v-model="dialogData.entNumber" autocomplete="off" placeholder="请填写账号" />
          </el-form-item>

          <el-form-item label="地址" prop="address">
            <el-input v-model="dialogData.address" autocomplete="off" placeholder="请填写地址" />
          </el-form-item>

          <el-form-item label="电话" prop="telphone">
            <el-input v-model="dialogData.telphone" autocomplete="off" placeholder="请填写电话" />
          </el-form-item>

          <el-form-item label="增值税率" prop="comTaxRate">
            <div class="form-row">
              <el-input v-model="dialogData.comTaxRate" autocomplete="off" placeholder="请选择增值税率">
                <template slot="append">%</template>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item label="附加税率" prop="addTaxRate">
            <div class="form-row">
              <el-input v-model="dialogData.addTaxRate" autocomplete="off" placeholder="请选择附加税率">
                <template slot="append">%</template>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item label="自由职业者个税起征点" prop="personStartPoint">
            <div class="form-row">
              <el-input v-model="dialogData.personStartPoint" autocomplete="off" placeholder="请选择自由职业者个税起征点">
                <template slot="append">元</template>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item label="自由职业者单人／月税前基线" prop="personBaseLineMonth">
            <div class="form-row">
              <el-input v-model="dialogData.personBaseLineMonth" autocomplete="off" placeholder="请选择自由职业者单人／月税前基线">
                <template slot="append">元</template>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item label="自由职业者单人／年税前基线" prop="personBaseLineYear">
            <div class="form-row">
              <el-input v-model="dialogData.personBaseLineYear" autocomplete="off" placeholder="请选择自由职业者单人／年税前基线">
                <template slot="append">元</template>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item label="优先级" prop="orderNum">
            <div class="form-row">
              <el-input v-model="dialogData.orderNum" autocomplete="off" placeholder="请选择优先级" />
            </div>
          </el-form-item>

          <el-form-item>
            <div style="text-align: right">
              <el-button type="primary" @click="submitData('dialogData')">保存</el-button>
              <el-button @click="dialogFormVisible = false">取消</el-button>
            </div>
          </el-form-item>

        </el-form>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script>
import levyBody from '@/axios/default/levyBody'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

var checkPhone = (rule, value, callback) => {
  const reg = /^1[0-9]\d{9}$/
  if (reg.test(value)) {
    callback()
  } else {
    return callback(new Error('请输入正确的手机号'))
  }
}
export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,
      formData: {},
      dialogData: this.initdialogData(),
      dialogFormVisible: false,
      formLabelWidth: '210px',
      listData: [],
      regions: [],
      actionType: '',
      dialogFormTitle: '',
      rules: {
        name: [{ required: true, message: '请填写代征主体名称', trigger: 'blur' }, { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }],
        province: [{ required: true, message: '请选择所属地', trigger: 'change' }],
        taxNum: [{ required: true, message: '请填写纳税识别号', trigger: 'blur' }],
        bankOpenName: [{ required: true, message: '请填写开户行名称', trigger: 'blur' }],
        entNumber: [{ required: true, message: '请填写账号', trigger: 'blur' }],
        address: [{ required: true, message: '请填写地址', trigger: 'blur' }],
        telphone: [{ required: true, message: '请填写电话', trigger: 'blur' }, { validator: checkPhone, trigger: ['blur', 'change'] }],
        comTaxRate: [{ required: true, message: '请填写增值税率', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        addTaxRate: [{ required: true, message: '请填写附加税率', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        personStartPoint: [{ required: true, message: '请填写自由职业者个税起征点', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        personBaseLineMonth: [{ required: true, message: '请填写自由职业者单人／月税前基线', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        personBaseLineYear: [{ required: true, message: '请填写自由职业者单人／年税前基线', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }],
        orderNum: [{ required: true, message: '请填写优先级', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            const result = /(^[0-9]*(\d*|\.\d*)$)/.test(value)
            if (result) {
              callback()
            } else {
              callback('请输入正确的数字')
            }
          }, trigger: ['blur', 'change'] }]
      }
    }
  },
  mounted() {
    this.queryRegions()
    this.queryLevyBodyInfos()
  },
  methods: {
    initdialogData(data) {
      if (data) {
        return {
          'id': data.id,
          'name': data.name,
          'province': data.province,
          'city': data.city,
          'county': data.county,
          'comTaxRate': data.comTaxRate,
          'addTaxRate': data.addTaxRate,
          'personStartPoint': data.personStartPoint,
          'personBaseLineMonth': data.personBaseLineMonth,
          'personBaseLineYear': data.personBaseLineYear,
          'orderNum': data.orderNum,
          taxNum: data.taxNum,
          bankOpenName: data.bankOpenName,
          entNumber: data.entNumber,
          address: data.address,
          telphone: data.telphone
        }
      } else {
        return {
          'id': '',
          'name': '',
          taxNum: '',
          bankOpenName: '',
          entNumber: '',
          address: '',
          telphone: '',

          'province': '',
          'city': '',
          'county': '',
          'comTaxRate': '',
          'addTaxRate': '',
          'personStartPoint': '',
          'personBaseLineMonth': '',
          'personBaseLineYear': '',
          'orderNum': ''
        }
      }
    },
    // 查询
    queryLevyBodyInfos() {
      levyBody.queryLevyBodyInfosByPage({
        'pageSize': this.pageSize,
        'pageNum': this.pageNum
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.queryLevyBodyInfos()
    },
    queryRegions() {
      levyBody.queryRegions().then(res => {
        this.regions = res.data.data
      })
    },
    addLevyBody() {
      this.dialogFormVisible = true
      this.dialogData = this.initdialogData()
      this.dialogFormTitle = '新增代征主体'
      this.actionType = '1' // 添加
    },
    editLevyBody(data) {
      this.dialogFormVisible = true
      console.log(data)
      this.dialogData = this.initdialogData(data)
      this.dialogFormTitle = '修改代征主体'
      this.actionType = '2' // 修改
    },
    submitData(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.actionType == '1') {
            levyBody.insertLevyBodyInfo(this.dialogData).then(res => {
              if (res.data.code == '0000') {
                this.dialogFormVisible = false
                this.queryLevyBodyInfos()
                this.$message.success('操作成功')
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          } else {
            levyBody.updateLevyBodyInfo(this.dialogData).then(res => {
              if (res.data.code == '0000') {
                this.dialogFormVisible = false
                this.queryLevyBodyInfos()
                this.$message.success('操作成功')
              } else {
                newAlert(this.$tips, res.data.message)
              }
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }
  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }
  .dialog-scroll{
    overflow-y: hidden;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 40px;
  }
  .form-row{
    display: flex;
  }
  .form-row-style{
    margin: 0 5px;
  }
  .pagination{
    text-align: right;
  }
</style>
