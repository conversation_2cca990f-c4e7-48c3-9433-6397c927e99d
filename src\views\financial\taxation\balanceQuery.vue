<template>
  <div class="content-main">
    <el-card class="list-card" v-if="balanceQueryArr.length != 0">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in balanceQueryArr" :key="item.name">
          <el-card shadow="hover">
            <div class="companyItem">
              <div class="useBal">{{ item.useBal }}</div>
              <div class="company">{{ item.name }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import financial from '@/axios/default/financial'
import { newAlert } from '@/utils'

export default {
  name: 'BalanceQuery',
  components: {
  },
  data() {
    return {
      balanceQueryArr: []
    }
  },

  mounted() {
    this.list()
  },
  methods: {
    list() {
      financial.queryFeeByBank().then(res => {
        if (res.data.code === '0000') {
          this.balanceQueryArr = res.data.data
        } else {
          newAlert(res.data.message)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.el-row {
  .el-col {
    margin-bottom: 16px;
    .companyItem {
      text-align: center;
      .useBal {
        font-size: 36px;
        font-weight: 700;
        color: #f56c6c;
        margin-bottom: 20px;
      }
      .company {
        font-size: 18px;
        font-weight: 700;
        color: #9a9a9a;
      }
    }
  }
}
</style>
