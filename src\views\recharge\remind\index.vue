<template>
  <div class="content-main">
    <div class="general-layout">
      <el-card class="list-card">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form :inline="true" :model="formData" class="demo-form-inline" size="mini">
              <el-row :gutter="20">
                <el-col :lg="8" :xs="24">
                  <el-form-item label="企业名称" class="form-items">
                    <el-input v-model="formData.merchantName" placeholder="请输入内容" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-search" type="primary" @click="onSearch">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :lg="24" :xs="24">
                  <el-form-item class="form-items" style="text-align:right">
                    <el-button icon="el-icon-plus" type="primary" plain @click="onAddMerchant">添加企业</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>

        <el-table
          :data="listData"
          border
          size="mini"
          class="list-table"
        >
          <el-table-column
            prop="merNo"
            label="商户号"
            max-width="220"
            show-overflow-tooltip
          />
          <el-table-column
            prop="merName"
            label="企业名称"
            max-width="250"
            show-overflow-tooltip
          />
          <el-table-column
            prop="sendNumber"
            label="手机号"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            label="是否提醒"
            min-width="80"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.isSendMsgBalance ==1 ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="onModifyMerchant(scope.row)">修改</el-button>
              <el-button type="text" @click="onSetBalance(scope.row)">设置余额</el-button>
              <el-popconfirm title="确定删除吗？" @onConfirm="delData(scope.row)">
                <a slot="reference" class="tb-active-red">删除</a>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleSizeChange" />

      </el-card>
    </div>

    <el-dialog title="添加/编辑企业" :visible.sync="dialogFormVisible" width="40%" class="reset-dialog reset-dialog-small">
      <el-scrollbar class="dialog-scroll">
        <el-form ref="dialogData" :model="dialogData" label-width="110px" size="mini" class="form-style" :rules="rules">
          <el-form-item label="企业名称" prop="merchantName">
            <el-select
              v-model="dialogData.merchantName"
              filterable
              remote
              reserve-keyword
              placeholder="请输入企业名称"
              :disabled="!isAdd"
              :remote-method="getMerchant"
              class="auto-width"
              :loading="loading"
              @change="getMessageInfo"
            >
              <el-option
                v-for="item in merchantList"
                :key="item.id"
                :label="item.merName"
                :value="item.merName"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="手机号" prop="sendNumber">
            <el-input v-model="dialogData.sendNumber" placeholder="多个手机号，以逗号(英文)隔开" type="textarea" :rows="2" autocomplete="off" class="auto-width" />
          </el-form-item>
          <el-form-item label="提醒开关">
            <el-switch
              v-model="dialogData.isSendMsgBalance"
              active-color="#ff4949"
              inactive-color="#dcdfe6"
            />
          </el-form-item>
          <el-form-item>
            <div style="text-align: right">
              <el-button @click="dialogFormVisible=false">取消</el-button>
              <el-button type="primary" :loading="btnLoading" @click="submit('dialogData')">确定</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-dialog>

  </div>
</template>
<script>
import rechInfo from '@/axios/default/rechInfo'
import merchants from '@/axios/default/merchants'
import Pagination from '@/components/Pagination'
import { newAlert } from '@/utils'

export default {
  name: 'Index',
  components: {
    Pagination
  },
  data() {
    return {
      formData: {
        merchantName: ''
      },
      pageSize: this.$route.query.pageSize ? Number(this.$route.query.pageSize) : 10,
      pageNum: this.$route.query.page ? Number(this.$route.query.page) : 1,
      total: 0,

      dialogData: {
        isSendMsgBalance: false,
        sendNumber: '',
        merchantName: ''
      },

      loading: false,
      merchantList: [],

      isAdd: true,
      dialogFormVisible: false,
      listData: [],
      btnLoading: false,
      rules: {
        merchantName: { required: true, message: '企业名称不可为空', trigger: 'blur' },
        sendNumber: { required: true, message: '手机号不可为空', trigger: 'blur' }
      }
    }
  },
  mounted() {
    this.findSendMsgBalance()
  },
  methods: {
    onSearch() {
      this.pageNum = 1
      this.findSendMsgBalance()
    },
    onAddMerchant() {
      this.isAdd = true
      this.dialogFormVisible = true
      if (this.dialogData.merchantName.length > 0) {
        this.$set(this.dialogData, 'isSendMsgBalance', false)
        this.$set(this.dialogData, 'sendNumber', '')
        this.$set(this.dialogData, 'merchantName', '')
      }
    },
    onModifyMerchant(data) {
      this.isAdd = false
      this.dialogFormVisible = true
      this.$set(this.dialogData, 'isSendMsgBalance', data.isSendMsgBalance == 1 ? true : data.isSendMsgBalance == 0 ? false : data.isSendMsgBalance)
      this.$set(this.dialogData, 'sendNumber', data.sendNumber)
      this.$set(this.dialogData, 'merchantName', data.merName)
      this.getMerchant(data.merName)
    },

    onSetBalance(data) {
      this.$router.push({
        path: '/recharge/balance',
        query: { merId: data.id, merName: data.merName }
      })
    },
    delData(args) {
      this.$set(this.dialogData, 'sendNumber', args.sendNumber)
      this.$set(this.dialogData, 'merchantName', args.merName)
      const data = JSON.parse(JSON.stringify(this.dialogData))
      data.isSendMsgBalance = '2'
      rechInfo.addSendMsgBalance(data).then(res => {
        this.$refs.dialogData.clearValidate()
        this.$refs.dialogData.resetFields()
        if (res.data.code === '0000') {
          this.findSendMsgBalance()
          this.$message({ type: 'success', message: '操作成功' })
        } else {
          newAlert(this.$tips, res.data.message)
        }
      })
    },

    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          const data = JSON.parse(JSON.stringify(this.dialogData))
          data.isSendMsgBalance = data.isSendMsgBalance ? '1' : '0'
          rechInfo.addSendMsgBalance(data).then(res => {
            this.btnLoading = false
            this.$refs.dialogData.clearValidate()
            this.$refs.dialogData.resetFields()
            if (res.data.code === '0000') {
              this.dialogFormVisible = false
              this.findSendMsgBalance()
              this.$message({ type: 'success', message: '操作成功' })
            } else {
              newAlert(this.$tips, res.data.message)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    findSendMsgBalance() {
      rechInfo.findSendMsgBalance({
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        merchantName: this.formData.merchantName
      }).then(res => {
        if (res.data.code === '0000') {
          this.listData = res.data.data.rows
          this.total = res.data.data.total
          this.pageSize = res.data.data.pageSize
          this.pageNum = res.data.data.pageNum
        }
      })
    },
    handleSizeChange(data) {
      this.pageNum = data.pageNum
      this.pageSize = data.pageSize
      this.findSendMsgBalance()
    },
    getMerchant(query) {
      this.loading = true
      merchants.list({
        merName: query
      }).then(res => {
        if (res.data.code === '0000') {
          this.loading = false
          this.merchantList = res.data.data.rows
        }
      })
    },
    getMessageInfo(v) {
      const currentMerchant = this.merchantList.filter(item => item.merName === v)
      if (currentMerchant.length > 0) {
        rechInfo.findMerchantMsgById({
          id: currentMerchant[0].id
        }).then(res => {
          if (res.data.code === '0000') {
            const data = res.data.data
            this.$set(this.dialogData, 'isSendMsgBalance', data.isSendMsgBalance == 1 ? true : data.isSendMsgBalance == 0 ? false : data.isSendMsgBalance)
            this.$set(this.dialogData, 'sendNumber', data.sendNumber)
          }
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .list-card{
    margin-bottom: 20px;
  }

  .auto-width{
    width: 100%;
  }

  .dialog-form{
    display: flex;
    flex-flow: column nowrap;
  }
  .el-form-item__content{
    margin-left: 0;
  }

  .list-table{
    margin: 0 0 20px 0;
    .el-button{
      padding: 0;
    }
  }

  .dialog-scroll{
    overflow-y: auto;
    height: calc(100%);
  }

  .reset-dialog{
    /deep/ .el-dialog{
      height: 80vh;
      overflow: hidden;
      .el-dialog__body{
        height: calc(100% - 54px);
      }
    }
    overflow: hidden;
  }
  .reset-dialog-small{
    /deep/ .el-dialog{
      height: 65vh;
    }
    overflow: hidden;
  }
  .form-style{
    padding-right: 20px;
  }
  .pagination{
    text-align: right;
  }
</style>
